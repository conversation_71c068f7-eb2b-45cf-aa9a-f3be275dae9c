# train_stage_1.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 第一阶段训练脚本
# 训练分层深度监督玻璃分割网络 (ProteusGlassNet)
# 融合多尺度ViT特征提取、FPN跳跃连接、分层深度监督、SCSA注意力
# 
# 架构特点：
# - P2层边缘监督：低级细节强化
# - 深层透明度监督：高级语义理解
# - 主预测监督：融合决策
# - 层间一致性：协调优化
# -----------------------------------------------------------------------------

import os
import sys
import time
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.cuda.amp import autocast, GradScaler
from tqdm import tqdm
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from glass_dataloader import create_glass_dataloaders
from models import ProteusGlassNet
from utils.losses import create_hierarchical_loss, create_stage1_loss
from utils.metrics import SegmentationMetrics
from utils.utils import (
    create_optimizer, create_scheduler,
    adjust_learning_rate, count_parameters, setup_seed,
    format_time, create_experiment_dir, Timer
)

class Stage1Trainer:
    """第一阶段训练器 - 分层深度监督版本"""
    
    def __init__(self, config):
        """
        第一阶段训练器初始化
        Args:
            config: 配置对象
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建数据加载器
        print("🔄 创建数据加载器...")
        self.train_loader, self.val_loader = create_glass_dataloaders(
            data_dir=config.PROJECT_ROOT,
            batch_size=config.STAGE1_BATCH_SIZE,
            target_size=config.STAGE1_IMG_SIZE,
            split_ratio=config.GLASS_DATALOADER['split_ratio'],
            random_seed=config.GLASS_DATALOADER['random_seed'],
            num_workers=config.NUM_WORKERS,
            glass_aug_config=config.GLASS_DATALOADER['glass_augmentation']
        )
        
        # 创建模型
        print("🔄 创建模型...")
        self.model = ProteusGlassNet(
            vit_model_name=config.VIT_MODEL_NAME,
            backbone_path=config.BACKBONE_PRETRAINED_PATH,
            extract_layers=[3, 6, 9, 11],  # 固定提取层
            crf_iter=config.CRF_ITER,
            trainable_crf=config.CRF_TRAINABLE
        ).to(self.device)
        
        # 打印模型信息
        print("📊 模型参数统计:")
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"  - 总参数数量: {total_params:,} ({total_params/1e6:.2f}M)")
        print(f"  - 可训练参数: {trainable_params:,} ({trainable_params/1e6:.2f}M)")
        
        # 创建损失函数
        print("🎯 使用分层深度监督损失函数 - 完整模式...")
        print("📊 第一阶段损失权重配置 (基于IG_SLAM优化经验):")
        print(f"  - Focal损失权重: {config.STAGE1_FOCAL_WEIGHT}")
        print(f"  - IoU损失权重: {config.STAGE1_IOU_WEIGHT}")
        print(f"  - 边缘损失权重: {config.STAGE1_DT_WEIGHT}")
        print(f"  - 总权重: {config.STAGE1_TOTAL_WEIGHT} (避免过高导致数值不稳定)")
        
        self.criterion = create_stage1_loss()
        
        # 创建优化器 - 差分学习率
        print(f"🔧 创建差分学习率优化器: AdamW, 基础LR: {config.STAGE1_LR}, ViT LR: {config.STAGE1_LR * 0.1}")
        self.optimizer = create_optimizer(
            self.model,
            optimizer_type='ADAMW',
            lr=config.STAGE1_LR,
            weight_decay=config.STAGE1_WEIGHT_DECAY,
            vit_lr_ratio=0.1  # ViT使用0.1倍学习率
        )
        
        # 创建学习率调度器
        print(f"📈 创建学习率调度器: {config.SCHEDULER_TYPE}")
        self.scheduler = create_scheduler(
            self.optimizer,
            scheduler_type=config.SCHEDULER_TYPE,
            epochs=config.STAGE1_EPOCHS,
            step_size=config.STEP_SCHEDULER_STEP_SIZE,
            gamma=config.STEP_SCHEDULER_GAMMA
        )
        
        # 创建混合精度训练的梯度缩放器
        self.scaler = GradScaler(enabled=config.MIXED_PRECISION)
        
        # 创建指标计算器
        self.metrics = SegmentationMetrics()
        
        # 创建实验目录
        self.exp_dir = create_experiment_dir(config.OUTPUT_DIR)
        self.checkpoint_dir = os.path.join(self.exp_dir, 'checkpoints')
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # 加载检查点（如果存在）
        self.start_epoch = 0
        self.best_iou = 0.0
        self.best_epoch = 0
        
        # 🔧 添加早停机制，防止过拟合
        self.early_stopping_patience = 5
        self.early_stopping_counter = 0
        self.early_stopping_min_delta = 0.01  # IoU下降超过1%就计数
        
        # 🔧 修复检查点加载逻辑 - 权重与训练状态分离策略
        if config.STAGE1_BEST_MODEL_PATH and os.path.exists(config.STAGE1_BEST_MODEL_PATH):
            print(f"📦 检测到模型权重文件: {config.STAGE1_BEST_MODEL_PATH}")
            
            # 尝试加载权重
            try:
                # 如果是纯权重文件，直接加载state_dict
                checkpoint = torch.load(config.STAGE1_BEST_MODEL_PATH, map_location='cpu')
                
                if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                    # 包含训练状态的完整检查点
                    print("📋 检测到完整训练检查点")
                    self.model.load_state_dict(checkpoint['model_state_dict'])
                    
                    # 如果有训练状态，也加载它们
                    if 'epoch' in checkpoint:
                        self.start_epoch = checkpoint['epoch']
                    if 'best_iou' in checkpoint:
                        self.best_iou = checkpoint['best_iou']
                    if 'best_epoch' in checkpoint:
                        self.best_epoch = checkpoint['best_epoch']
                    if 'optimizer_state_dict' in checkpoint:
                        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                    if 'scheduler_state_dict' in checkpoint:
                        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                    if 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
                        self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
                        
                    print(f"✅ 完整检查点加载成功 - Epoch: {self.start_epoch}, Best IoU: {self.best_iou:.4f}")
                    
                else:
                    # 纯权重文件
                    print("⚡ 检测到纯权重文件，只加载模型权重")
                    self.model.load_state_dict(checkpoint)
                    print("✅ 模型权重加载成功，训练状态将重新开始")
                    
            except Exception as e:
                print(f"❌ 权重加载失败: {e}")
                print("🔧 将从随机初始化开始训练")
        else:
            print("⚠️ 未找到预训练权重，将从随机初始化开始训练")
        
        print("✅ 第一阶段训练器初始化完成!")
    
    def _cleanup_old_checkpoints(self, current_epoch: int, keep_last_n: int = 5):
        """清理旧的检查点文件，避免磁盘空间不足"""
        try:
            import glob
            
            # 清理模型权重文件
            model_files = glob.glob(os.path.join(self.checkpoint_dir, 'stage1_model_epoch_*.pth'))
            model_files.sort(key=os.path.getmtime)  # 按修改时间排序
            
            if len(model_files) > keep_last_n:
                files_to_remove = model_files[:-keep_last_n]
                for file_path in files_to_remove:
                    try:
                        os.remove(file_path)
                        print(f"🗑️ 清理旧权重文件: {os.path.basename(file_path)}")
                    except Exception as e:
                        print(f"⚠️ 无法删除文件 {file_path}: {e}")
            
            # 清理训练状态文件
            state_files = glob.glob(os.path.join(self.checkpoint_dir, 'stage1_training_state_epoch_*.pth'))
            state_files.sort(key=os.path.getmtime)
            
            if len(state_files) > keep_last_n:
                files_to_remove = state_files[:-keep_last_n]
                for file_path in files_to_remove:
                    try:
                        os.remove(file_path)
                        print(f"🗑️ 清理旧状态文件: {os.path.basename(file_path)}")
                    except Exception as e:
                        print(f"⚠️ 无法删除文件 {file_path}: {e}")
                        
        except Exception as e:
            print(f"⚠️ 清理检查点文件失败: {e}")
    
    def load_training_state(self, training_state_path: str):
        """
        加载训练状态以恢复训练
        Args:
            training_state_path: 训练状态文件路径
        """
        if not os.path.exists(training_state_path):
            print(f"⚠️ 训练状态文件不存在: {training_state_path}")
            return False
            
        try:
            print(f"📦 正在加载训练状态: {training_state_path}")
            checkpoint = torch.load(training_state_path, map_location=self.device)
            
            # 加载模型权重
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            
            # 加载训练进度
            self.start_epoch = checkpoint.get('epoch', 0)
            self.best_iou = checkpoint.get('best_iou', 0.0)
            self.best_epoch = checkpoint.get('best_epoch', 0)
            
            # 尝试加载optimizer状态（从单独文件）
            optimizer_path = training_state_path.replace('state', 'optimizer').replace('state_', 'optimizer_')
            if os.path.exists(optimizer_path):
                try:
                    optimizer_state = torch.load(optimizer_path, map_location=self.device)
                    if isinstance(optimizer_state, dict) and 'state' in optimizer_state:
                        # 完整的optimizer state_dict
                        self.optimizer.load_state_dict(optimizer_state)
                        print(f"   🔧 优化器状态: 已恢复")
                    else:
                        # 简化的optimizer信息
                        print(f"   🔧 优化器状态: 使用简化信息")
                except Exception as e:
                    print(f"   ⚠️ 优化器状态加载失败: {e}")
            
            # 尝试加载scheduler状态（从单独文件）
            scheduler_path = training_state_path.replace('state', 'scheduler').replace('state_', 'scheduler_')
            if os.path.exists(scheduler_path):
                try:
                    scheduler_state = torch.load(scheduler_path, map_location=self.device)
                    if isinstance(scheduler_state, dict) and 'last_epoch' in scheduler_state:
                        # 完整的scheduler state_dict
                        self.scheduler.load_state_dict(scheduler_state)
                        print(f"   📅 学习率调度: 已恢复")
                    else:
                        # 简化的scheduler信息
                        print(f"   📅 学习率调度: 使用简化信息")
                except Exception as e:
                    print(f"   ⚠️ 调度器状态加载失败: {e}")
            
            print(f"✅ 训练状态加载成功:")
            print(f"   📈 开始轮次: {self.start_epoch}")
            print(f"   🏆 最佳IoU: {self.best_iou:.4f} (第{self.best_epoch}轮)")
            
            return True
            
        except Exception as e:
            print(f"❌ 训练状态加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        self.metrics.reset()
        total_loss = 0
        total_main_loss = 0
        total_edge_loss = 0
        total_iou_loss = 0
        total_trans_loss = 0
        total_consist_loss = 0
        total_iou = 0
        
        # 首次训练时进行数据和模型诊断
        if epoch == 0:
            print("🔍 进行首次诊断...")
            try:
                # 获取一个批次进行诊断
                images, masks, dt_maps = next(iter(self.train_loader))
                images = images.to(self.device, non_blocking=True)
                masks = masks.to(self.device, non_blocking=True)
                dt_maps = dt_maps.to(self.device, non_blocking=True)
                
                print(f"📊 数据诊断:")
                print(f"  - 图像形状: {images.shape}, 范围: [{images.min().item():.3f}, {images.max().item():.3f}]")
                print(f"  - 掩码形状: {masks.shape}, 范围: [{masks.min().item():.3f}, {masks.max().item():.3f}]")
                print(f"  - DT图形状: {dt_maps.shape}, 范围: [{dt_maps.min().item():.3f}, {dt_maps.max().item():.3f}]")
                
                # 详细验证数据范围
                print(f"🔍 详细数据范围验证:")
                
                # 验证图像（ImageNet标准化后应该在大约[-2.1, 2.6]范围）
                img_min, img_max = images.min().item(), images.max().item()
                print(f"  - 图像: [{img_min:.3f}, {img_max:.3f}] (ImageNet标准化)")
                if img_min < -3.0 or img_max > 3.0:
                    print(f"    ⚠️ 图像范围异常，可能归一化有问题")
                
                # 验证掩码（应该在[0, 1]范围）
                mask_min, mask_max = masks.min().item(), masks.max().item()
                print(f"  - 掩码: [{mask_min:.3f}, {mask_max:.3f}] (应该在[0,1])")
                if mask_min < 0.0 or mask_max > 1.0:
                    print(f"    ⚠️ 掩码范围错误，应该在[0,1]")
                
                # 验证距离变换图（应该在[0, 1]范围）
                dt_min, dt_max = dt_maps.min().item(), dt_maps.max().item()
                print(f"  - DT图: [{dt_min:.3f}, {dt_max:.3f}] (应该在[0,1])")
                if dt_min < 0.0 or dt_max > 1.0:
                    print(f"    ⚠️ DT图范围错误，应该在[0,1]")
                
                # 检查数据中的异常值
                nan_checks = {
                    '图像': torch.isnan(images).any(),
                    '掩码': torch.isnan(masks).any(),
                    'DT图': torch.isnan(dt_maps).any()
                }
                
                inf_checks = {
                    '图像': torch.isinf(images).any(),
                    '掩码': torch.isinf(masks).any(),
                    'DT图': torch.isinf(dt_maps).any()
                }
                
                for name, has_nan in nan_checks.items():
                    if has_nan:
                        print(f"    ⚠️ {name}包含NaN")
                        
                for name, has_inf in inf_checks.items():
                    if has_inf:
                        print(f"    ⚠️ {name}包含Inf")
                
                # 测试模型前向传播
                print("🔍 测试模型前向传播...")
                with torch.no_grad():
                    outputs = self.model(images[:1])  # 只用一张图像测试
                    print(f"📊 模型输出:")
                    for key, value in outputs.items():
                        val_min, val_max = value.min().item(), value.max().item()
                        print(f"  - {key}: 形状={value.shape}, 范围=[{val_min:.3f}, {val_max:.3f}]")
                        
                        # 检查模型输出范围
                        if key == 'main_pred':
                            if val_min < 0.0 or val_max > 1.0:
                                print(f"    ⚠️ {key}范围错误，应该在[0,1] (sigmoid输出)")
                        elif key == 'dt_pred':
                            if val_min < 0.0 or val_max > 1.0:
                                print(f"    ⚠️ {key}范围错误，应该在[0,1] (sigmoid输出)")
                        
                        if torch.isnan(value).any():
                            print(f"    ⚠️ {key}包含NaN")
                        if torch.isinf(value).any():
                            print(f"    ⚠️ {key}包含Inf")
                
                # 测试损失计算
                print("🔍 测试损失计算...")
                try:
                    loss, loss_dict = self.criterion(outputs, masks[:1], dt_maps[:1])
                    print(f"📊 损失诊断:")
                    print(f"  - 总损失: {loss.item():.6f}")
                    for key, value in loss_dict.items():
                        val = value.item() if hasattr(value, 'item') else value
                        print(f"  - {key}: {val:.6f}")
                        
                    # 损失值合理性检查
                    if loss.item() > 10.0:
                        print(f"    ⚠️ 损失值过高，可能存在问题")
                    elif loss.item() < 0.0:
                        print(f"    ⚠️ 损失值为负，存在错误")
                    elif torch.isnan(loss) or torch.isinf(loss):
                        print(f"    ⚠️ 损失值包含NaN/Inf")
                    else:
                        print(f"    ✅ 损失值正常")
                        
                except Exception as e:
                    print(f"❌ 损失计算失败: {e}")
                    import traceback
                    traceback.print_exc()
                    
            except Exception as e:
                print(f"❌ 诊断失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 学习率预热
        if epoch < self.config.STAGE1_WARMUP_EPOCHS:
            lr = adjust_learning_rate(
                self.optimizer, epoch, self.config.STAGE1_LR, self.config.STAGE1_WARMUP_EPOCHS
            )
            print(f"🔥 预热阶段 - 学习率: {lr:.6f}")
        
        # 创建训练进度条
        pbar = tqdm(
            self.train_loader,
            desc=f"Epoch [{epoch+1}/{self.config.STAGE1_EPOCHS}]",
            ncols=120,
            leave=True
        )
        
        for batch_idx, (images, masks, dt_maps) in enumerate(pbar):
            # 移动到GPU
            images = images.to(self.device, non_blocking=True)
            masks = masks.to(self.device, non_blocking=True)
            dt_maps = dt_maps.to(self.device, non_blocking=True)
            
            # 数据范围验证 (每100个批次验证一次，避免性能影响)
            if batch_idx % 100 == 0 or epoch == 0:
                # 验证数据范围
                if masks.min() < 0.0 or masks.max() > 1.0:
                    print(f"⚠️ 批次{batch_idx}: 掩码范围错误 [{masks.min():.3f}, {masks.max():.3f}]")
                    masks = torch.clamp(masks, 0.0, 1.0)
                    
                if dt_maps.min() < 0.0 or dt_maps.max() > 1.0:
                    print(f"⚠️ 批次{batch_idx}: DT图范围错误 [{dt_maps.min():.3f}, {dt_maps.max():.3f}]")
                    dt_maps = torch.clamp(dt_maps, 0.0, 1.0)
                    
                # 检查NaN/Inf
                if torch.isnan(images).any() or torch.isnan(masks).any() or torch.isnan(dt_maps).any():
                    print(f"⚠️ 批次{batch_idx}: 检测到NaN，跳过此批次")
                    continue
                    
                if torch.isinf(images).any() or torch.isinf(masks).any() or torch.isinf(dt_maps).any():
                    print(f"⚠️ 批次{batch_idx}: 检测到Inf，跳过此批次")
                    continue
            
            # 前向传播
            with autocast(enabled=self.config.MIXED_PRECISION):
                outputs = self.model(images)
                
                # 验证模型输出范围
                if batch_idx % 100 == 0 or epoch == 0:
                    for key, value in outputs.items():
                        if key in ['main_pred', 'dt_pred']:
                            if value.min() < 0.0 or value.max() > 1.0:
                                print(f"⚠️ 批次{batch_idx}: {key}范围错误 [{value.min():.3f}, {value.max():.3f}]")
                        
                        if torch.isnan(value).any() or torch.isinf(value).any():
                            print(f"⚠️ 批次{batch_idx}: {key}包含NaN/Inf，跳过此批次")
                            continue
                
                loss, loss_dict = self.criterion(outputs, masks, dt_maps)
            
            # 反向传播
            self.optimizer.zero_grad()
            
            if self.config.MIXED_PRECISION:
                self.scaler.scale(loss).backward()
                # 先unscale，再检查是否有inf/nan
                self.scaler.unscale_(self.optimizer)
                # 检查梯度是否有inf/nan
                if torch.isfinite(loss):
                    if self.config.GRAD_CLIP_NORM > 0:
                        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.GRAD_CLIP_NORM)
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    # 如果检测到inf/nan，跳过这个批次
                    self.scaler.update()
                    print(f"⚠️ 检测到损失值异常(nan/inf)，跳过当前批次")
                    continue
            else:
                loss.backward()
                if self.config.GRAD_CLIP_NORM > 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.GRAD_CLIP_NORM)
                self.optimizer.step()
            
            # 更新总损失
            total_loss += loss.item()
            total_main_loss += loss_dict['focal_loss']
            total_edge_loss += loss_dict['dt_loss']
            total_iou_loss += loss_dict['iou_loss']
            total_trans_loss += loss_dict.get('transparency_loss', 0.0)
            total_consist_loss += loss_dict.get('consistency', 0.0)
            
            # 更新指标
            pred_prob = outputs['main_pred']  # 已经经过sigmoid
            self.metrics.update(pred_prob, masks)
            
            # 更新进度条
            avg_loss = total_loss / (batch_idx + 1)
            avg_main = total_main_loss / (batch_idx + 1)
            avg_edge = total_edge_loss / (batch_idx + 1)
            avg_iou_loss = total_iou_loss / (batch_idx + 1)
            avg_trans = total_trans_loss / (batch_idx + 1)
            avg_consist = total_consist_loss / (batch_idx + 1)
            
            metrics = self.metrics.compute()
            
            pbar.set_postfix({
                'loss': f"{avg_loss:.4f}",
                'main': f"{avg_main:.4f}",
                'edge': f"{avg_edge:.4f}",
                'iou_loss': f"{avg_iou_loss:.4f}",
                'iou': f"{metrics['iou']:.4f}",
                'lr': f"{self.optimizer.param_groups[0]['lr']:.6f}"
            })
        
        # 计算平均损失
        avg_loss = total_loss / len(self.train_loader)
        avg_main_loss = total_main_loss / len(self.train_loader)
        avg_edge_loss = total_edge_loss / len(self.train_loader)
        avg_iou_loss = total_iou_loss / len(self.train_loader)
        avg_trans_loss = total_trans_loss / len(self.train_loader)
        avg_consist_loss = total_consist_loss / len(self.train_loader)
        
        # 更新学习率
        self.scheduler.step()
        
        # 获取最终metrics，如果没有有效批次则创建默认metrics
        if 'metrics' not in locals():
            print("⚠️ 没有有效的训练批次，使用默认metrics")
            metrics = {
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0,
                'accuracy': 0.0,
                'iou': 0.0
            }
        
        return {
            'loss': avg_loss,
            'main_loss': avg_main_loss,
            'edge_loss': avg_edge_loss,
            'iou_loss': avg_iou_loss,
            'trans_loss': avg_trans_loss,
            'consist_loss': avg_consist_loss,
            'lr': self.optimizer.param_groups[0]['lr'],
            'metrics': metrics
        }
    
    def validate_epoch(self, epoch):
        """验证一个epoch"""
        self.model.eval()
        self.metrics.reset()
        total_loss = 0
        total_main_loss = 0
        total_edge_loss = 0
        total_iou_loss = 0
        total_trans_loss = 0
        total_consist_loss = 0
        total_iou = 0
        
        # 创建验证进度条
        pbar = tqdm(
            self.val_loader,
            desc=f"验证 Epoch [{epoch}/{self.config.STAGE1_EPOCHS}]",
            ncols=100,
            leave=True
        )
        
        with torch.no_grad():
            for batch_idx, (images, masks, dt_maps) in enumerate(pbar):
                images = images.to(self.device, non_blocking=True)
                masks = masks.to(self.device, non_blocking=True)
                dt_maps = dt_maps.to(self.device, non_blocking=True)
                
                # 前向传播
                outputs = self.model(images)
                loss, loss_dict = self.criterion(outputs, masks, dt_maps)
                
                # 计算IoU
                pred = outputs['main_pred']  # 已经经过sigmoid
                iou = calculate_iou(pred > 0.5, masks)  # 🔧 使用二值化IoU，保持一致性
                
                # 更新总损失
                total_loss += loss.item()
                total_main_loss += loss_dict['focal_loss']
                total_edge_loss += loss_dict['dt_loss']
                total_iou_loss += loss_dict['iou_loss']
                total_trans_loss += loss_dict.get('transparency_loss', 0.0)
                total_consist_loss += loss_dict.get('consistency', 0.0)
                total_iou += iou
                
                # 更新指标
                self.metrics.update(pred, masks)
                
                # 更新进度条
                avg_loss = total_loss / (batch_idx + 1)
                avg_main = total_main_loss / (batch_idx + 1)
                avg_edge = total_edge_loss / (batch_idx + 1)
                avg_iou_loss = total_iou_loss / (batch_idx + 1)
                avg_trans = total_trans_loss / (batch_idx + 1)
                avg_consist = total_consist_loss / (batch_idx + 1)
                
                metrics = self.metrics.compute()
                
                pbar.set_postfix({
                    'loss': f"{avg_loss:.4f}",
                    'main': f"{avg_main:.4f}",
                    'edge': f"{avg_edge:.4f}",
                    'iou_loss': f"{avg_iou_loss:.4f}",
                    'iou': f"{metrics['iou']:.4f}",
                    'lr': f"{self.optimizer.param_groups[0]['lr']:.6f}"
                })
        
        # 计算平均值
        avg_loss = total_loss / len(self.val_loader)
        avg_main_loss = total_main_loss / len(self.val_loader)
        avg_edge_loss = total_edge_loss / len(self.val_loader)
        avg_iou_loss = total_iou_loss / len(self.val_loader)
        avg_trans_loss = total_trans_loss / len(self.val_loader)
        avg_consist_loss = total_consist_loss / len(self.val_loader)
        avg_iou = total_iou / len(self.val_loader)
        
        return {
            'loss': avg_loss,
            'main_loss': avg_main_loss,
            'edge_loss': avg_edge_loss,
            'iou_loss': avg_iou_loss,
            'trans_loss': avg_trans_loss,
            'consist_loss': avg_consist_loss,
            'iou': avg_iou,
            'metrics': metrics
        }
    
    def save_model(self, epoch, val_results, is_best=False):
        """保存模型 - 分文件夹保存权重、优化器、训练状态，并写明IoU和MAE"""
        import os
        metrics = val_results['metrics']
        iou = val_results['iou']  # 🔧 直接使用验证时计算的IoU，确保一致性
        mae = metrics.get('mae', 0.0) if 'mae' in metrics else 0.0
        
        # 1. 保存模型权重
        model_dir = os.path.join(self.checkpoint_dir, 'model')
        os.makedirs(model_dir, exist_ok=True)
        model_filename = f'stage1_model_epoch_{epoch+1:03d}_iou_{iou:.4f}_mae_{mae:.4f}.pth'
        model_filepath = os.path.join(model_dir, model_filename)
        torch.save(self.model.state_dict(), model_filepath)
        if is_best:
            best_model_path = os.path.join(model_dir, 'stage1_model_best.pth')
            torch.save(self.model.state_dict(), best_model_path)
            print(f"✅ 最佳模型权重已保存到: {best_model_path}")
        
        # 2. 保存优化器状态
        optimizer_dir = os.path.join(self.checkpoint_dir, 'optimizer')
        os.makedirs(optimizer_dir, exist_ok=True)
        optimizer_filename = f'stage1_optimizer_epoch_{epoch+1:03d}.pth'
        optimizer_filepath = os.path.join(optimizer_dir, optimizer_filename)
        # 单独保存optimizer状态，避免PyCapsule问题
        try:
            torch.save(self.optimizer.state_dict(), optimizer_filepath)
        except Exception as e:
            print(f"⚠️ 优化器状态保存失败: {e}")
            # 保存一个简化的optimizer信息
            optimizer_info = {
                'param_groups': self.optimizer.param_groups,
                'defaults': self.optimizer.defaults
            }
            torch.save(optimizer_info, optimizer_filepath)
        
        # 3. 保存训练状态（包含scheduler、best_iou等）
        state_dir = os.path.join(self.checkpoint_dir, 'state')
        os.makedirs(state_dir, exist_ok=True)
        state_filename = f'stage1_state_epoch_{epoch+1:03d}.pth'
        state_filepath = os.path.join(state_dir, state_filename)
        # 只保存metrics中的数值，避免PyCapsule报错
        metrics_dict = {k: float(v) for k, v in metrics.items() if isinstance(v, (int, float))}
        # 避免保存config，它包含不可序列化的PyCapsule对象
        training_state = {
            'epoch': epoch,
            'best_iou': self.best_iou,
            'metrics': metrics_dict,  # 只保存数值
            'learning_rate': self.optimizer.param_groups[0]['lr']  # 只保存学习率
        }
        torch.save(training_state, state_filepath)
        
        # 4. 保存scheduler状态（单独文件）
        scheduler_dir = os.path.join(self.checkpoint_dir, 'scheduler')
        os.makedirs(scheduler_dir, exist_ok=True)
        scheduler_filename = f'stage1_scheduler_epoch_{epoch+1:03d}.pth'
        scheduler_filepath = os.path.join(scheduler_dir, scheduler_filename)
        try:
            torch.save(self.scheduler.state_dict(), scheduler_filepath)
        except Exception as e:
            print(f"⚠️ 调度器状态保存失败: {e}")
            # 保存scheduler的基本信息
            scheduler_info = {
                'last_epoch': getattr(self.scheduler, 'last_epoch', epoch),
                'base_lrs': getattr(self.scheduler, 'base_lrs', [self.config.STAGE1_LR])
            }
            torch.save(scheduler_info, scheduler_filepath)
        
        print(f"💾 第 {epoch+1} 轮检查点已保存。\n  - 模型权重: {model_filename}\n  - 优化器: {optimizer_filename}\n  - 调度器: {scheduler_filename}\n  - 训练状态: {state_filename}\n  - 验证IoU: {iou:.4f}  MAE: {mae:.4f}")
    
    def train(self):
        """完整训练流程"""
        print("\n🚀 开始第一阶段训练 - 分层深度监督玻璃分割网络")
        print(f"📊 训练配置:")
        print(f"  - 总轮数: {self.config.STAGE1_EPOCHS}")
        print(f"  - 批次大小: {self.config.STAGE1_BATCH_SIZE}")
        print(f"  - 学习率: {self.config.STAGE1_LR}")
        print(f"  - 图像尺寸: {self.config.STAGE1_IMG_SIZE}")
        print(f"  - 混合精度: {self.config.MIXED_PRECISION}")
        print(f"  - 损失函数: hierarchical")
        print(f"  - 实验目录: {self.exp_dir}")
        
        print(f"\n🎯 分层深度监督架构:")
        print(f"  - P2层边缘监督: 低级细节强化")
        print(f"  - 深层透明度监督: 高级语义理解")
        print(f"  - 主预测监督: 融合决策优化")
        print(f"  - 层间一致性: 预测协调性")
        print(f"  - 目标IoU: 90%+ (基于87.95%成功经验优化)\n")
        
        timer = Timer()
        
        for epoch in range(self.start_epoch, self.config.STAGE1_EPOCHS):
            print(f"\n{'='*50}")
            print(f"Epoch {epoch+1}/{self.config.STAGE1_EPOCHS}")
            print(f"{'='*50}")
            
            # 训练
            train_results = self.train_epoch(epoch)
            
            # 验证
            if (epoch + 1) % self.config.EVAL_EVERY_N_EPOCHS == 0:
                val_results = self.validate_epoch(epoch)
                
                # 检查是否为最佳模型
                is_best = val_results['metrics']['iou'] > self.best_iou
                if is_best:
                    self.best_iou = val_results['metrics']['iou']
                    self.best_metrics = val_results['metrics']
                    self.early_stopping_counter = 0  # 重置早停计数器
                    print(f"🏆 新的最佳模型! IoU: {self.best_iou:.4f}")
                else:
                    # 🔧 早停检查：如果IoU下降超过阈值，增加计数器
                    iou_drop = self.best_iou - val_results['metrics']['iou']
                    if iou_drop > self.early_stopping_min_delta:
                        self.early_stopping_counter += 1
                        print(f"⚠️ IoU下降 {iou_drop:.4f}，早停计数器: {self.early_stopping_counter}/{self.early_stopping_patience}")
                    else:
                        self.early_stopping_counter = 0  # 重置计数器
                
                # 检查是否触发早停
                if self.early_stopping_counter >= self.early_stopping_patience:
                    print(f"🛑 触发早停！验证IoU连续{self.early_stopping_patience}次下降，停止训练")
                    print(f"🏆 最佳IoU: {self.best_iou:.4f} (第{self.best_epoch}轮)")
                    break
                
                # 保存模型
                self.save_model(epoch, val_results, is_best)
            
            # 打印当前状态
            current_lr = self.optimizer.param_groups[0]['lr']
            elapsed_time = timer.elapsed()
            
            print(f"\n Epoch {epoch+1} 总结:")
            print(f"  - 训练损失: {train_results['loss']:.4f}")
            print(f"  - 训练IoU_loss: {train_results['iou_loss']:.4f}")
            if (epoch + 1) % self.config.EVAL_EVERY_N_EPOCHS == 0:
                print(f"  - 验证损失: {val_results['loss']:.4f}")
                print(f"  - 验证IoU: {val_results['iou']:.4f}")
            print(f"  - 学习率: {current_lr:.6f}")
            print(f"  - 已用时间: {format_time(elapsed_time)}")
        
        # 训练完成
        total_time = timer.elapsed()
        print(f"\n🎉 第一阶段训练完成！")
        print(f"🏆 最佳验证指标:")
        for key, value in self.best_metrics.items():
            print(f"  - {key}: {value:.4f}")
        print(f"📊 最终结果:")
        print(f"  - 总训练时间: {format_time(total_time)}")
        print(f"  - 最佳IoU: {self.best_iou:.4f}")
        print(f"  - 模型保存路径: {self.config.STAGE1_BEST_MODEL_PATH}")

def calculate_iou(pred: torch.Tensor, target: torch.Tensor) -> float:
    """
    计算IoU指标 - 修复版本
    Args:
        pred: [B, 1, H, W] 预测掩码
        target: [B, 1, H, W] 目标掩码
    Returns:
        iou: float IoU指标 (单个样本的IoU)
    """
    pred = pred.float()
    target = target.float()
    
    # 计算每个样本的IoU
    intersection = (pred * target).sum(dim=(2, 3))  # [B, 1]
    union = pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) - intersection  # [B, 1]
    
    # 避免除零
    union = torch.clamp(union, min=1e-7)
    
    # 计算IoU
    iou = intersection / union  # [B, 1]
    
    # 返回第一个样本的IoU（因为通常batch_size=1）
    return iou[0, 0].item()

def main():
    """主函数"""
    # 设置随机种子
    setup_seed(42)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='ProteusGlassDiffusion Stage1 Training')
    parser.add_argument('--debug', action='store_true', help='调试模式：减少训练轮数')
    parser.add_argument('--resume', type=str, default=None, 
                       help='恢复训练的状态文件路径，例如: checkpoints/exp_xxx/checkpoints/stage1_training_latest.pth')
    args = parser.parse_args()
    
    # 调试模式：减少训练轮数
    if args.debug:
        print("🔧 调试模式：减少训练轮数")
        config.STAGE1_EPOCHS = 3
        config.EVAL_EVERY_N_EPOCHS = 1
        config.SAVE_EVERY_N_EPOCHS = 1
    
    # 创建训练器
    trainer = Stage1Trainer(config)
    
    # 如果指定了恢复路径，加载训练状态
    if args.resume:
        print(f"🔄 恢复训练模式")
        success = trainer.load_training_state(args.resume)
        if not success:
            print("❌ 训练状态恢复失败，将从头开始训练")
    
    # 开始训练
    trainer.train()

if __name__ == "__main__":
    main() 