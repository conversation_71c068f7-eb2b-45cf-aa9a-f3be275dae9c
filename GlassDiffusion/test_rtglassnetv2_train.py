#!/usr/bin/env python3
"""
RTGlassNetv2 训练集测试脚本 - 在训练数据上验证性能
验证模型是否真正学会了特征，还是只是过拟合
"""
import os
import torch
import numpy as np
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
import argparse
from GlassDiffusion.models.RTGlassNetv2 import RTGlassNetv2
from GlassDiffusion.glass_dataloader import GlassDataLoader
import cv2
from sklearn.model_selection import KFold

# 导入评估指标函数
from ig_glass.misc import compute_iou, compute_acc, compute_fmeasure, compute_mae, compute_ber, compute_aber, compute_precision_recall

def parse_arguments():
    parser = argparse.ArgumentParser(description='RTGlassNetv2训练集测试')
    parser.add_argument('--model_path', default='./ckpt/RTGlassNetv2/weights/BEST_epoch004_iou0.9826.pth', 
                       type=str, help='模型权重路径')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--glass_threshold', default=0.5, type=float, help='玻璃检测阈值')
    parser.add_argument('--save_results', action='store_true', help='保存预测结果')
    parser.add_argument('--result_dir', default='./test_results_train', type=str, help='结果保存目录')
    parser.add_argument('--test_subset', default='train', type=str, choices=['train', 'val', 'all'], 
                       help='测试数据子集')
    parser.add_argument('--num_samples', default=None, type=int, help='限制测试样本数量（用于快速测试）')
    return parser.parse_args()

class RTGlassNetv2TrainTester:
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建结果目录
        if args.save_results:
            os.makedirs(args.result_dir, exist_ok=True)
            os.makedirs(os.path.join(args.result_dir, 'predictions'), exist_ok=True)
        
        print(f"🔧 RTGlassNetv2训练集测试配置:")
        print(f"   模型路径: {args.model_path}")
        print(f"   测试子集: {args.test_subset}")
        print(f"   图像尺寸: {args.img_size}x{args.img_size}")
        print(f"   玻璃检测阈值: {args.glass_threshold}")
        print(f"   设备: {self.device}")
        
        if args.num_samples:
            print(f"   限制样本数: {args.num_samples}")
        
        # 记录硬件信息
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            print(f"   GPU: {device_name}")
        
    def load_model(self):
        """加载模型和权重"""
        print(f"\n📥 加载模型...")
        
        # 创建模型
        model = RTGlassNetv2(backbone_type='inceptionnext_base').to(self.device)
        
        # 加载权重
        if os.path.exists(self.args.model_path):
            checkpoint = torch.load(self.args.model_path, map_location=self.device)
            model.load_state_dict(checkpoint['model'])
            
            print(f"✅ 成功加载模型权重")
            print(f"   训练轮数: {checkpoint.get('epoch', 'Unknown')}")
            print(f"   验证IoU: {checkpoint.get('val_iou', 'Unknown'):.4f}")
            
            # 打印配置信息
            if 'config' in checkpoint:
                config = checkpoint['config']
                print(f"   训练配置: {config.get('backbone_type', 'Unknown')}")
                print(f"   损失权重: Focal({config.get('focal_weight', 'Unknown')}) + "
                      f"IoU({config.get('iou_weight', 'Unknown')}) + "
                      f"DT({config.get('dt_weight', 'Unknown')})")
        else:
            print(f"❌ 模型权重文件不存在: {self.args.model_path}")
            raise FileNotFoundError(f"模型权重文件不存在: {self.args.model_path}")
        
        model.eval()
        
        # 模型复杂度分析
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 模型参数量: {total_params/1e6:.2f} M")
        
        return model
    
    def create_train_dataset(self):
        """创建训练数据集（与训练时相同的设置）"""
        print(f"\n📁 创建训练数据集...")
        
        # 创建全数据集（与训练脚本保持一致）
        full_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/',
            split='all',  # 使用全部数据
            target_size=(self.args.img_size, self.args.img_size),
            split_ratio=1.0,  # 使用100%数据
            random_seed=42,
            glass_aug_config='none'  # 测试时不使用数据增强
        )
        
        print(f"📊 原始数据集信息:")
        print(f"   总数据量: {len(full_data)}张图像")
        
        # 使用与训练脚本相同的9:1拆分
        np.random.seed(42)  # 确保与训练时一致
        indices = np.arange(len(full_data))
        np.random.shuffle(indices)
        
        # 计算训练集和验证集大小
        train_size = int(len(full_data) * 0.9)
        train_indices = indices[:train_size]
        val_indices = indices[train_size:]
        
        print(f"📊 数据集拆分 (与训练时一致):")
        print(f"   训练集: {len(train_indices)}张图像 (90%)")
        print(f"   验证集: {len(val_indices)}张图像 (10%)")
        
        # 根据指定的测试子集选择数据
        if self.args.test_subset == 'train':
            test_indices = train_indices
            subset_name = "训练集"
        elif self.args.test_subset == 'val':
            test_indices = val_indices
            subset_name = "验证集"
        else:  # 'all'
            test_indices = indices
            subset_name = "全数据集"
        
        # 限制样本数量（如果指定）
        if self.args.num_samples and len(test_indices) > self.args.num_samples:
            test_indices = test_indices[:self.args.num_samples]
            print(f"⚠️ 限制测试样本数量为: {self.args.num_samples}")
        
        print(f"🎯 测试数据集: {subset_name} ({len(test_indices)}张图像)")
        
        # 创建测试数据集
        test_dataset = Subset(full_data, test_indices)
        
        return test_dataset, subset_name
    
    def evaluate_on_train_data(self, model, test_dataset, subset_name):
        """在训练数据上评估模型"""
        print(f"\n🔍 在{subset_name}上评估模型...")
        
        # 创建数据加载器
        test_loader = DataLoader(
            test_dataset,
            batch_size=8,  # 批次大小
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # 统计变量
        total_intersection = 0.0
        total_union = 0.0
        count = 0
        
        # 详细统计
        iou_sum, acc_sum, fm_sum, mae_sum, ber_sum, aber_sum = 0, 0, 0, 0, 0, 0
        
        inference_times = []
        batch_ious = []
        
        # 性能优化设置
        torch.cuda.empty_cache()
        torch.backends.cudnn.benchmark = False
        torch.backends.cudnn.deterministic = True
        
        with torch.no_grad():
            test_pbar = tqdm(test_loader, desc=f"{subset_name}测试进度")
            
            for batch_idx, (inp_imgs, gt_masks, _) in enumerate(test_pbar):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                
                # 推理时间计时
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)
                
                start_time.record()
                # 模型推理
                outputs = model(inp_imgs)
                refined_pred = outputs['refined_pred']
                
                # 确保预测值在有效范围内
                refined_pred = torch.clamp(refined_pred, 0, 1)
                end_time.record()
                
                torch.cuda.synchronize()
                inference_time = start_time.elapsed_time(end_time) / 1000.0
                inference_times.append(inference_time)
                
                # 二值化预测
                pred_binary = (refined_pred > self.args.glass_threshold).float()
                
                # 批次IoU计算
                for i in range(inp_imgs.size(0)):
                    # 单张图像的预测和GT
                    pred_single = pred_binary[i, 0].cpu().numpy()  # [H, W]
                    gt_single = gt_masks[i, 0].cpu().numpy()       # [H, W]
                    
                    # 计算各项指标
                    tiou = compute_iou(pred_single, gt_single)
                    tacc = compute_acc(pred_single, gt_single)
                    precision, recall = compute_precision_recall(pred_single, gt_single)
                    tfm = compute_fmeasure(precision, recall)
                    tmae = compute_mae(pred_single, gt_single)
                    tber = compute_ber(pred_single, gt_single)
                    taber = compute_aber(pred_single, gt_single)
                    
                    # 累积统计
                    iou_sum += tiou
                    acc_sum += tacc
                    fm_sum += tfm
                    mae_sum += tmae
                    ber_sum += tber
                    aber_sum += taber
                    batch_ious.append(tiou)
                    count += 1
                    
                    # 全局IoU计算
                    intersection = (pred_single * gt_single).sum()
                    union = pred_single.sum() + gt_single.sum() - intersection
                    total_intersection += intersection
                    total_union += union
                
                # 更新进度条
                if count > 0:
                    current_avg_iou = iou_sum / count
                    current_global_iou = total_intersection / (total_union + 1e-7)
                    test_pbar.set_postfix({
                        'avg_iou': f'{current_avg_iou:.4f}',
                        'global_iou': f'{current_global_iou:.4f}',
                        'samples': f'{count}'
                    })
                
                # 保存预测结果（前几个批次）
                if self.args.save_results and batch_idx < 10:
                    self.save_batch_predictions(inp_imgs, gt_masks, refined_pred, batch_idx)
                
                # 定期清理GPU缓存
                if batch_idx % 50 == 0:
                    torch.cuda.empty_cache()
        
        # 计算最终指标
        if count > 0:
            final_results = {
                'mean_iou': iou_sum / count,
                'global_iou': total_intersection / (total_union + 1e-7),
                'std_iou': np.std(batch_ious),
                'accuracy': acc_sum / count,
                'f_measure': fm_sum / count,
                'mae': mae_sum / count,
                'ber': ber_sum / count,
                'aber': aber_sum / count,
                'total_samples': count,
                'subset_name': subset_name
            }
            
            # 性能指标
            if len(inference_times) > 5:
                avg_inference_time = np.mean(inference_times[5:])
                pure_fps = 1.0 / avg_inference_time
                final_results['avg_inference_time'] = avg_inference_time
                final_results['pure_fps'] = pure_fps
        else:
            final_results = {'error': 'No valid samples processed'}
        
        return final_results
    
    def save_batch_predictions(self, inp_imgs, gt_masks, pred_masks, batch_idx):
        """保存预测结果"""
        batch_size = inp_imgs.size(0)
        
        for i in range(min(batch_size, 4)):  # 每个批次最多保存4张图
            # 反标准化图像
            img = inp_imgs[i].cpu()
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
            img = img * std + mean
            img = torch.clamp(img, 0, 1)
            img = (img.permute(1, 2, 0).numpy() * 255).astype(np.uint8)
            
            # GT和预测
            gt = (gt_masks[i, 0].cpu().numpy() * 255).astype(np.uint8)
            pred = (pred_masks[i, 0].cpu().numpy() * 255).astype(np.uint8)
            
            # 保存预测结果
            filename = f'train_batch{batch_idx:03d}_sample{i:02d}_pred.png'
            cv2.imwrite(os.path.join(self.args.result_dir, 'predictions', filename), pred)
    
    def print_results(self, results):
        """打印详细的测试结果"""
        print("\n" + "="*60)
        print(f"        {results['subset_name']}测试结果")
        print("="*60)
        
        print("\n📊 IoU指标:")
        print(f"  平均IoU:                       {results['mean_iou']:.4f}")
        print(f"  全局IoU:                       {results['global_iou']:.4f}")
        print(f"  IoU标准差:                     {results['std_iou']:.4f}")
        
        print("\n📊 其他指标:")
        print(f"  准确率:                        {results['accuracy']:.4f}")
        print(f"  F1分数:                        {results['f_measure']:.4f}")
        print(f"  MAE:                          {results['mae']:.4f}")
        print(f"  BER:                          {results['ber']:.4f}")
        print(f"  ABER:                         {results['aber']:.4f}")
        
        if 'pure_fps' in results:
            print("\n⚡ 性能指标:")
            print(f"  推理时间:                      {results['avg_inference_time']*1000:.2f} ms")
            print(f"  推理FPS:                       {results['pure_fps']:.2f}")
        
        print(f"\n📊 数据统计:")
        print(f"  测试样本数:                    {results['total_samples']}")
        
        # 性能评估
        print(f"\n📈 性能评估:")
        mean_iou = results['mean_iou']
        if mean_iou > 0.95:
            print("🎯 优秀! IoU > 95% - 模型在训练数据上表现出色")
        elif mean_iou > 0.90:
            print("🎯 很好! IoU > 90% - 模型学习效果良好")
        elif mean_iou > 0.85:
            print("🎯 良好! IoU > 85% - 模型基本掌握了特征")
        elif mean_iou > 0.80:
            print("🎯 一般! IoU > 80% - 模型学习尚可")
        else:
            print("🎯 需要改进! IoU < 80% - 模型可能没有很好地学习特征")
        
        # 泛化能力分析
        if results['std_iou'] < 0.05:
            print("✅ 一致性优秀: IoU标准差 < 5%")
        elif results['std_iou'] < 0.10:
            print("✅ 一致性良好: IoU标准差 < 10%")
        else:
            print("⚠️ 一致性一般: IoU标准差较大")
        
        print("\n" + "="*60)
    
    def run(self):
        """运行完整的测试流程"""
        try:
            # 加载模型
            model = self.load_model()
            
            # 创建训练数据集
            test_dataset, subset_name = self.create_train_dataset()
            
            # 评估模型
            results = self.evaluate_on_train_data(model, test_dataset, subset_name)
            
            # 打印结果
            if 'error' not in results:
                self.print_results(results)
            else:
                print(f"❌ 评估失败: {results['error']}")
            
            if self.args.save_results:
                print(f"\n💾 预测结果已保存到: {self.args.result_dir}")
            
            return results
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

def main():
    args = parse_arguments()
    tester = RTGlassNetv2TrainTester(args)
    results = tester.run()
    
    # 保存结果到文件
    if args.save_results and 'error' not in results:
        import json
        results_file = os.path.join(args.result_dir, f'train_test_results_{args.test_subset}.json')
        with open(results_file, 'w') as f:
            # 转换numpy类型为Python原生类型
            json_results = {k: float(v) if isinstance(v, (np.float32, np.float64)) else v 
                          for k, v in results.items()}
            json.dump(json_results, f, indent=2)
        print(f"📄 测试结果已保存到: {results_file}")

if __name__ == '__main__':
    main() 