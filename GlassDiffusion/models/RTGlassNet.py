import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 🔧 修复：使用timm库导入InceptionNeXt模型
try:
    import timm
    INCEPTIONNEXT_AVAILABLE = True
    print("✅ InceptionNeXt models (timm) imported successfully")
except ImportError as e:
    print(f"❌ Warning: timm not available: {e}")
    INCEPTIONNEXT_AVAILABLE = False

from ig_glass.attention_scsa import SCSA
from .diff_crf import SimplifiedDiffCRF

# 1. FeatureExtractor
class FeatureExtractor(nn.Module):
    """
    多尺度特征提取器，支持InceptionNeXt-T/Small/Base，预留Swin、MobileViT接口。
    forward输出4个尺度特征(C2, C3, C4, C5)。
    """
    def __init__(self, backbone_type='inceptionnext_tiny', pretrained=True):
        super().__init__()
        self.backbone_type = backbone_type.lower()
        
        if not INCEPTIONNEXT_AVAILABLE:
            raise ImportError("InceptionNeXt models not available!")
        
        if 'inceptionnext' in self.backbone_type:
            if 'tiny' in self.backbone_type:
                model_name = 'inception_next_tiny.sail_in1k'
            elif 'small' in self.backbone_type:
                model_name = 'inception_next_small.sail_in1k'
            elif 'base' in self.backbone_type:
                model_name = 'inception_next_base.sail_in1k'
            else:
                raise ValueError(f"Unknown InceptionNeXt backbone: {backbone_type}")
                
            print(f"🔧 创建backbone: {model_name} (pretrained={pretrained})")
            try:
                # InceptionNeXt的特征层索引从0开始
                # 0: stem输出
                # 1: stage1输出
                # 2: stage2输出
                # 3: stage3输出
                self.backbone = timm.create_model(
                    model_name,
                    pretrained=pretrained,
                    features_only=True,
                    out_indices=(0, 1, 2, 3)  # 对应C2, C3, C4, C5
                )
                print("✅ Backbone创建成功")
                
                # 打印每层的输出通道数
                if hasattr(self.backbone, 'feature_info'):
                    print("📊 特征层信息:")
                    for idx, info in enumerate(self.backbone.feature_info):
                        print(f"   Layer {idx}: {info['num_chs']} channels")
                
            except Exception as e:
                print(f"❌ Backbone创建失败: {str(e)}")
                raise
        else:
            raise ValueError(f"Unsupported backbone: {backbone_type}")

    def forward(self, x):
        # 🔧 修复：使用timm的features_only模式
        feats = self.backbone(x)  # 返回多尺度特征列表
        return feats  # [C2, C3, C4, C5]

    @property
    def out_channels(self):
        """返回backbone各层的输出通道数"""
        if hasattr(self.backbone, 'feature_info'):
            return [info['num_chs'] for info in self.backbone.feature_info]
        
        # 如果feature_info不可用，使用默认值
        if 'inceptionnext' in self.backbone_type:
            if 'tiny' in self.backbone_type:
                return [96, 192, 384, 768]  # InceptionNeXt-Tiny
            elif 'small' in self.backbone_type:
                return [96, 192, 384, 768]  # InceptionNeXt-Small
            elif 'base' in self.backbone_type:
                return [128, 256, 512, 1024]  # InceptionNeXt-Base
        else:
            raise ValueError(f"Unsupported backbone: {self.backbone_type}")

# 2. FPNDecoder
class FPNDecoder(nn.Module):
    """
    轻量级特征金字塔解码器（FPN），输入4个尺度特征，输出融合后的[P2, P3, P4, P5]。
    - 所有横向连接均为元素级相加
    - 每层可选1x1卷积调整通道数
    """
    def __init__(self, in_channels_list, out_channels=128):
        super().__init__()
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(in_ch, out_channels, kernel_size=1)
            for in_ch in in_channels_list
        ])
        self.smooth_convs = nn.ModuleList([
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
            for _ in in_channels_list
        ])

    def forward(self, features):
        feats = [l_conv(f) for l_conv, f in zip(self.lateral_convs, features)]
        num_levels = len(feats)
        for i in range(num_levels-1, 0, -1):
            upsample = nn.functional.interpolate(feats[i], size=feats[i-1].shape[2:], mode='nearest')
            feats[i-1] = feats[i-1] + upsample
        outs = [smooth(f) for smooth, f in zip(self.smooth_convs, feats)]
        return outs  # [P2, P3, P4, P5]

# 3. LightEdgeEnhancer（用您原本的高精度实现）
class LightEdgeEnhancer(nn.Module):
    """Lightweight module to enhance edge features (from ig_glass/gdnet_scsa.py)"""
    def __init__(self, in_channels):
        super(LightEdgeEnhancer, self).__init__()
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(inplace=True)
        )
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))

    def forward(self, x):
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        enhanced = self.edge_conv(x)
        return x + enhanced * edge_features

# 4. PredictionHead（LeakyReLU版本）
class PredictionHead(nn.Module):
    """
    双分支预测头：主预测 + DT预测
    训练时使用双分支，测试时只用主分支
    """
    def __init__(self, in_channels):
        super().__init__()
        # 共享特征提取
        self.shared_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(inplace=True)
        )
        
        # 主预测分支
        self.main_branch = nn.Conv2d(in_channels, 1, 1)
        
        # DT预测分支 (仅训练时使用)
        self.dt_branch = nn.Conv2d(in_channels, 1, 1)
        
    def forward(self, x, is_training=True):
        shared_feat = self.shared_conv(x)
        main_logits = self.main_branch(shared_feat)
        
        if is_training:
            dt_logits = self.dt_branch(shared_feat)
            return main_logits, dt_logits
        return main_logits, None

# SCSA包装器，支持通道数调整
class SCSAWithProj(nn.Module):
    def __init__(self, in_channels, out_channels, **kwargs):
        super().__init__()
        self.scsa = SCSA(dim=in_channels, **kwargs)
        self.proj = nn.Conv2d(in_channels, out_channels, 1)
    def forward(self, x):
        x = self.scsa(x)
        x = self.proj(x)
        return x

# 5. RTGlassNet主干
class RTGlassNet(nn.Module):
    """
    实时玻璃分割终极架构：Backbone + FPN + EdgeEnhancer + SCSA + diffCRF
    支持主预测和DT预测的双分支输出
    """
    def __init__(self, backbone_type='inceptionnext_tiny', fpn_out=128, scsa_reduction=8, crf_iter=3, crf_bilateral_weight=10.0):
        super().__init__()
        self.feature_extractor = FeatureExtractor(backbone_type=backbone_type)
        in_channels_list = self.feature_extractor.out_channels
        self.fpn_decoder = FPNDecoder(in_channels_list, out_channels=fpn_out)
        self.main_conv = nn.Sequential(
            nn.Conv2d(fpn_out, fpn_out, 3, padding=1),
            nn.BatchNorm2d(fpn_out),
            nn.LeakyReLU(inplace=True)
        )
        self.edge_enhancer = LightEdgeEnhancer(fpn_out)
        # 用SCSAWithProj替换原SCSA，支持通道降维
        self.scsa = SCSAWithProj(in_channels=fpn_out*2, out_channels=fpn_out, head_num=8)
        self.pred_head = PredictionHead(fpn_out)
        # CRF layer with parameters from grid search [1.5, 40, 3, 5, 10] - 与GDNetSCSA一致
        self.diff_crf = SimplifiedDiffCRF(
            n_iter=crf_iter,                    # 3
            bilateral_weight=10.0,              # From compat=10
            gaussian_weight=5.0,                # From compat=5
            bilateral_spatial_sigma=40.0,       # From sxy=40
            bilateral_color_sigma=3.0,          # From srgb=3
            gaussian_sigma=1.5,                 # From sxy=1.5
            trainable=True                     # 训练参数
        )
        
        # Class balancing weights for handling imbalanced data - 与GDNetSCSA一致
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)

    def forward(self, x):
        """
        前向传播
        训练时和测试时都返回完整字典
        """
        input_size = x.shape[2:]
        
        # 特征提取和融合
        c2, c3, c4, c5 = self.feature_extractor(x)
        p2, p3, p4, p5 = self.fpn_decoder([c2, c3, c4, c5])
        
        # 边缘增强的王牌流程
        main_features = self.main_conv(p2)
        edge_features = self.edge_enhancer(p2)
        
        # SCSA融合
        fused = self.scsa(torch.cat([main_features, edge_features], dim=1))
        
        # 根据模式选择不同的前向路径
        if not self.training:
            # 测试模式：只计算主分支
            main_logits, _ = self.pred_head(fused, is_training=False)
            main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
            main_pred = torch.sigmoid(main_logits)
            
            # CRF后处理（仅对主预测）
            main_prob = torch.clamp(main_pred, min=1e-7, max=1.0 - 1e-7)
            
            # 创建2通道logits（背景，前景）
            bg_logits = torch.log((1 - main_prob) * self.class_weights[0])
            fg_logits = torch.log(main_prob * self.class_weights[1])
            combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
            
            # 归一化输入图像
            normalized_img = torch.clamp(x, 0.0, 1.0)
            
            # 应用CRF
            try:
                crf_output = self.diff_crf(combined_logits, normalized_img)
                if crf_output.size(1) == 2:
                    refined_pred = crf_output[:, 1:2, :, :]  # 只取前景通道
                else:
                    refined_pred = crf_output
            except Exception as e:
                print(f"CRF错误: {e}")
                refined_pred = main_pred
            
            # 测试模式：返回完整字典
            return {
                'main_pred': main_pred,
                'dt_pred': main_pred,  # 测试时dt_pred使用main_pred
                'main_logits': main_logits,
                'dt_logits': main_logits,  # 测试时dt_logits使用main_logits
                'refined_pred': refined_pred
            }
        
        # 训练模式：计算所有分支
        main_logits, dt_logits = self.pred_head(fused, is_training=True)
        
        # 上采样到原始尺寸
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        dt_logits = F.interpolate(dt_logits, size=input_size, mode='bilinear', align_corners=True)
        
        # 激活函数
        main_pred = torch.sigmoid(main_logits)
        dt_pred = torch.sigmoid(dt_logits)
        
        # CRF后处理（仅对主预测）
        main_prob = torch.clamp(main_pred, min=1e-7, max=1.0 - 1e-7)
        
        if torch.isnan(main_prob).any() or torch.isinf(main_prob).any():
            print("警告: 主预测中检测到NaN或Inf值")
            main_prob = torch.nan_to_num(main_prob, nan=0.5, posinf=1.0, neginf=0.0)
        
        bg_logits = torch.log((1 - main_prob) * self.class_weights[0])
        fg_logits = torch.log(main_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        
        try:
            crf_output = self.diff_crf(combined_logits, normalized_img)
            if crf_output.size(1) == 0:
                print("警告: CRF返回了通道数为0的张量。创建备用输出。")
                refined_pred = torch.cat([1 - main_prob, main_prob], dim=1)
            elif crf_output.size(1) == 2:
                refined_pred = crf_output[:, 1:2, :, :]
            else:
                refined_pred = crf_output
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_pred = torch.cat([1 - main_prob, main_prob], dim=1)
            if refined_pred.size(1) == 2:
                refined_pred = refined_pred[:, 1:2, :, :]
        
        return {
            'main_pred': main_pred,
            'dt_pred': dt_pred,
            'main_logits': main_logits,
            'dt_logits': dt_logits,
            'refined_pred': refined_pred
        }