"""
专门用于玻璃检测的数据加载器 V3 (最终版)
- 采用'先增强，后填充缩放'的策略，100%保护图像结构和边缘信息。
- 融合了ig_glass dataloader的核心思想和albumentations的强大功能。
- 提供了'heavy'和'moderate'两种级别的保护性增强策略。
"""
import cv2
import numpy as np
import os
import torch
from torch.utils.data import Dataset, DataLoader
from scipy.ndimage import distance_transform_edt
import albumentations as A
from albumentations.pytorch import ToTensorV2
import glob
import random
from torchvision import transforms

# 玻璃增强配置（简化版）
GLASS_AUGMENTATION_CONFIGS = {
    'conservative': {
        'brightness': 0.1,
        'contrast': 0.1,
        'saturation': 0.1,
        'hue': 0.05
    },
    'moderate': {
        'brightness': 0.2,
        'contrast': 0.2,
        'saturation': 0.2,
        'hue': 0.1
    },
    'aggressive': {
        'brightness': 0.3,
        'contrast': 0.3,
        'saturation': 0.3,
        'hue': 0.15
    }
}

def apply_glass_augmentation(img, mask, config):
    """简化的玻璃感知数据增强"""
    # 这里可以添加更复杂的玻璃增强逻辑
    # 暂时返回原始图像和掩码
    return img, mask

# --- 从ig_glass/dataloader.py中引入的核心函数 ---
def pad_resize_image(inp_img, out_img=None, target_size=416):
    """
    通过填充黑边来无损地调整图像尺寸，确保所有信息都被保留。
    """
    h, w, c = inp_img.shape
    size = max(h, w)

    padding_h = (size - h) // 2
    padding_w = (size - w) // 2

    if out_img is None:
        temp_x = cv2.copyMakeBorder(inp_img, top=padding_h, bottom=padding_h, left=padding_w, right=padding_w,
                                    borderType=cv2.BORDER_CONSTANT, value=[0, 0, 0])
        temp_x = cv2.resize(temp_x, (target_size, target_size), interpolation=cv2.INTER_AREA)
        return temp_x
    else:
        temp_x = cv2.copyMakeBorder(inp_img, top=padding_h, bottom=padding_h, left=padding_w, right=padding_w,
                                    borderType=cv2.BORDER_CONSTANT, value=[0, 0, 0])
        temp_y = cv2.copyMakeBorder(out_img, top=padding_h, bottom=padding_h, left=padding_w, right=padding_w,
                                    borderType=cv2.BORDER_CONSTANT, value=[0]) # 掩码用0填充
        temp_x = cv2.resize(temp_x, (target_size, target_size), interpolation=cv2.INTER_AREA)
        temp_y = cv2.resize(temp_y, (target_size, target_size), interpolation=cv2.INTER_AREA)
        return temp_x, temp_y

class GlassDataset(Dataset):
    """
    玻璃数据集加载器 V3
    """
    def __init__(
        self,
        data_dir: str,
        split: str = 'train',
        target_size: tuple = (416, 416),
        split_ratio: float = 0.9,
        random_seed: int = 42,
        aug_config: str = 'heavy'
    ):
        super(GlassDataset, self).__init__()
        
        self.data_dir = data_dir
        self.split = split
        self.target_size = target_size
        
        self.image_dir = os.path.join(data_dir, 'ig_glass/dataset/train_gdd/image')
        self.mask_dir = os.path.join(data_dir, 'ig_glass/dataset/train_gdd/mask')
        
        all_files = sorted([f for f in os.listdir(self.image_dir) if f.endswith('.jpg')])
        
        state = np.random.RandomState(random_seed)
        indices = state.permutation(len(all_files))
        n_train = int(len(all_files) * split_ratio)
        
        if split == 'train':
            self.image_files = [all_files[i] for i in indices[:n_train]]
        elif split == 'valid':
            self.image_files = [all_files[i] for i in indices[n_train:]]
        else:
            self.image_files = all_files

        print(f"[{split.upper()}] 使用 {len(self.image_files)} 张图像。增强配置: {aug_config if split == 'train' else 'none'}")
        
        if split == 'train':
            self.transform = self.get_train_transforms(aug_config)
        else:
            self.transform = None

    def __len__(self):
        return len(self.image_files)

    @staticmethod
    def get_train_transforms(config='heavy'):
        # 定义保护性的、高强度的增强策略
        if config == 'heavy':
            return A.Compose([
                A.HorizontalFlip(p=0.5),
                # ShiftScaleRotate配置为不裁剪，只在内部变换
                A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.1, rotate_limit=25, p=0.8, 
                                   border_mode=cv2.BORDER_CONSTANT, value=0, mask_value=0),
                A.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.2, p=0.8),
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.5),
                A.GaussianBlur(blur_limit=(3, 7), p=0.5),
                A.MotionBlur(p=0.3),
            ])
        else: # moderate
            return A.Compose([
                A.HorizontalFlip(p=0.5),
                A.ShiftScaleRotate(shift_limit=0.05, scale_limit=0.05, rotate_limit=15, p=0.5,
                                   border_mode=cv2.BORDER_CONSTANT, value=0, mask_value=0),
                A.ColorJitter(brightness=0.2, contrast=0.2, p=0.5),
            ])

    @staticmethod
    def generate_dt_map(mask: np.ndarray) -> np.ndarray:
        mask = (mask > 127).astype(np.uint8)
        dt_pos = distance_transform_edt(mask)
        dt_neg = distance_transform_edt(1 - mask)
        dt_map = dt_pos - dt_neg
        if dt_map.max() != dt_map.min():
            dt_map = (dt_map - dt_map.min()) / (dt_map.max() - dt_map.min())
        return dt_map.astype(np.float32)

    def __getitem__(self, idx: int):
        image_path = os.path.join(self.image_dir, self.image_files[idx])
        mask_path = os.path.join(self.mask_dir, self.image_files[idx].replace('.jpg', '.png'))
        
        try:
            image = cv2.imread(image_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)

            if self.transform:
                transformed = self.transform(image=image, mask=mask)
                image = transformed['image']
                mask = transformed['mask']

            # 先生成DT Map，再进行填充缩放
            dt_map = self.generate_dt_map(mask)

            # 关键：使用pad_resize_image来无损缩放
            image_resized, mask_resized = pad_resize_image(image, mask, self.target_size[0])
            # dt_map也需要同样处理
            dt_map_resized, _ = pad_resize_image(np.stack([dt_map]*3, axis=-1).astype(np.uint8), mask, self.target_size[0])
            dt_map_resized = dt_map_resized[:,:,0] # 取单通道

            # 归一化和转换为Tensor
            normalize = A.Compose([
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2(),
            ])
            
            image_tensor = normalize(image=image_resized)['image']
            mask_tensor = torch.from_numpy(mask_resized).float().unsqueeze(0) / 255.0
            dt_map_tensor = torch.from_numpy(dt_map_resized).float().unsqueeze(0)

            return image_tensor, torch.clamp(mask_tensor, 0.0, 1.0), torch.clamp(dt_map_tensor, 0.0, 1.0)

        except Exception as e:
            print(f"错误处理数据 {image_path}: {e}")
            return torch.zeros((3, *self.target_size)), torch.zeros((1, *self.target_size)), torch.zeros((1, *self.target_size))

GlassDataLoader = GlassDataset


class GlassCrossValidationLoader(Dataset):
    """
    玻璃检测的交叉验证数据加载器
    支持自定义折数的交叉验证
    """
    def __init__(
        self, 
        mode='train', 
        fold=0,
        augment_data=False, 
        target_size=416,
        glass_augmentation='moderate',
        n_folds=3
    ):
        # 使用train_gdd数据集进行交叉验证
        self.inp_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/image'
        self.out_path = '/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/mask'
        
        self.mode = mode
        self.fold = fold
        self.augment_data = augment_data
        self.target_size = target_size
        self.glass_augmentation = glass_augmentation
        self.n_folds = n_folds
        
        # 获取玻璃增强配置
        if glass_augmentation in GLASS_AUGMENTATION_CONFIGS:
            self.augmentation_config = GLASS_AUGMENTATION_CONFIGS[glass_augmentation]
        else:
            self.augmentation_config = GLASS_AUGMENTATION_CONFIGS['moderate']
            
        self.normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )

        # 获取所有文件
        all_inp_files = sorted(glob.glob(self.inp_path + '/*'))
        all_out_files = sorted(glob.glob(self.out_path + '/*'))
        
        # 通用的K折交叉验证数据分割
        total_files = len(all_inp_files)
        
        # 创建索引列表并打乱（使用固定种子确保可重复）
        import random
        indices = list(range(total_files))
        random.seed(42)
        random.shuffle(indices)
        
        # 计算每折的大小
        fold_size = total_files // self.n_folds
        remainder = total_files % self.n_folds
        
        # 计算当前fold的起始和结束索引
        if fold < remainder:
            start_idx = fold * (fold_size + 1)
            end_idx = start_idx + fold_size + 1
        else:
            start_idx = fold * fold_size + remainder
            end_idx = start_idx + fold_size
        
        if mode == 'train':
            # 训练集：除当前fold外的所有数据
            selected_indices = indices[:start_idx] + indices[end_idx:]
        elif mode == 'test':
            # 测试集：当前fold的数据
            selected_indices = indices[start_idx:end_idx]
        else:
            raise ValueError(f"不支持的mode: {mode}")
        
        # 选择对应的文件
        self.inp_files = [all_inp_files[i] for i in selected_indices]
        self.out_files = [all_out_files[i] for i in selected_indices]
        
        print(f"交叉验证 {self.n_folds}折 Fold {fold+1} - {mode}数据集: {len(self.inp_files)}张图像")
        print(f"   总数据: {total_files}张, 当前fold: {end_idx-start_idx}张, 其他fold: {len(selected_indices)}张")

    def __getitem__(self, idx):
        # 读取输入图像
        inp_img = cv2.imread(self.inp_files[idx])
        if inp_img is None:
            raise ValueError(f"无法读取图像: {self.inp_files[idx]}")
        
        inp_img = cv2.cvtColor(inp_img, cv2.COLOR_BGR2RGB)
        inp_img = inp_img.astype('float32')

        # 读取掩码图像
        mask_img = cv2.imread(self.out_files[idx], 0)
        if mask_img is None:
            raise ValueError(f"无法读取掩码: {self.out_files[idx]}")
        
        mask_img = mask_img.astype('float32')
        
        # 归一化掩码到[0,1]
        if mask_img.max() > 1.0:
            mask_img = mask_img / 255.0

        # 应用玻璃感知数据增强
        if self.augment_data:
            inp_img, mask_img = apply_glass_augmentation(
                inp_img, mask_img, self.augmentation_config
            )

        # 填充和调整尺寸
        inp_img, mask_img = pad_resize_image(inp_img, mask_img, self.target_size)
        
        # 图像预处理
        inp_img = inp_img / 255.0
        inp_img = np.transpose(inp_img, axes=(2, 0, 1))
        inp_img = torch.from_numpy(inp_img).float()
        inp_img = self.normalize(inp_img)

        # 掩码预处理
        mask_img = np.expand_dims(mask_img, axis=0)
        mask_img = torch.from_numpy(mask_img).float()

        # 生成距离变换图
        dt_map = distance_transform_edt(mask_img.numpy().squeeze())
        dt_map = torch.from_numpy(dt_map).float().unsqueeze(0)
        
        return inp_img, mask_img, dt_map

    def __len__(self):
        return len(self.inp_files)


def create_glass_dataloaders(
    mode='train',
    batch_size=8,
    num_workers=4,
    target_size=416,
    augment_data=True,
    glass_augmentation='moderate',
    use_cross_validation=False,
    fold=0,
    split_ratio=0.8,
    random_seed=42,
    n_folds=3
):
    """
    创建玻璃检测数据加载器的便捷函数
    
    Args:
        mode: 'train' 或 'test' 或 'train_gdd_split_train' 或 'train_gdd_split_valid'
        batch_size: 批次大小
        num_workers: 数据加载进程数
        target_size: 目标图像尺寸
        augment_data: 是否使用数据增强
        glass_augmentation: 玻璃增强配置 ('conservative', 'moderate', 'aggressive')
        use_cross_validation: 是否使用交叉验证
        fold: 交叉验证的折数
        split_ratio: 数据拆分比例，训练集占比 (默认0.8)
        random_seed: 随机种子，确保拆分结果可重复 (默认42)
    
    Returns:
        DataLoader对象
    """
    if use_cross_validation:
        dataset = GlassCrossValidationLoader(
            mode=mode,
            fold=fold,
            augment_data=augment_data,
            target_size=target_size,
            glass_augmentation=glass_augmentation,
            n_folds=n_folds
        )
    else:
        dataset = GlassDataLoader(
            mode=mode,
            augment_data=augment_data,
            target_size=target_size,
            glass_augmentation=glass_augmentation,
            split_ratio=split_ratio,
            random_seed=random_seed
        )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=(mode == 'train'),
        num_workers=num_workers,
        pin_memory=True,
        drop_last=(mode == 'train')
    )
    
    return dataloader