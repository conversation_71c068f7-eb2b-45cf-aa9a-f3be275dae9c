#!/usr/bin/env python3
"""
🧪 简单IoU计算验证脚本
创建合成数据验证IoU计算的正确性
"""

import torch
import numpy as np

def create_synthetic_data(batch_size=4, height=64, width=64):
    """创建合成测试数据"""
    # 创建一些简单的几何形状作为测试数据
    gt_masks = torch.zeros(batch_size, 1, height, width)
    predictions = torch.zeros(batch_size, 1, height, width)
    
    # 样本1: 完全匹配的正方形
    gt_masks[0, 0, 20:40, 20:40] = 1.0
    predictions[0, 0, 20:40, 20:40] = 0.8  # 高置信度预测
    
    # 样本2: 部分重叠的矩形
    gt_masks[1, 0, 15:35, 15:35] = 1.0
    predictions[1, 0, 20:40, 20:40] = 0.7  # 部分重叠
    
    # 样本3: 完全不重叠
    gt_masks[2, 0, 10:30, 10:30] = 1.0
    predictions[2, 0, 35:55, 35:55] = 0.9  # 完全不重叠
    
    # 样本4: 小目标
    gt_masks[3, 0, 25:35, 25:35] = 1.0
    predictions[3, 0, 24:36, 24:36] = 0.6  # 稍大的预测
    
    return predictions, gt_masks

def calculate_iou_method1(pred, gt, threshold=0.5):
    """方法1: 批次内平均 (原始错误方法)"""
    pred_bin = (pred > threshold).float()
    gt_bin = (gt > threshold).float()
    
    batch_ious = []
    for i in range(pred_bin.shape[0]):
        intersection = (pred_bin[i] * gt_bin[i]).sum()
        union = pred_bin[i].sum() + gt_bin[i].sum() - intersection
        iou = intersection / (union + 1e-7)
        batch_ious.append(iou.item())
    
    return np.mean(batch_ious), batch_ious

def calculate_iou_method2(pred, gt, threshold=0.5):
    """方法2: 全局累积 (正确方法)"""
    pred_bin = (pred > threshold).float()
    gt_bin = (gt > threshold).float()
    
    total_intersection = (pred_bin * gt_bin).sum()
    total_union = pred_bin.sum() + gt_bin.sum() - total_intersection
    global_iou = (total_intersection / (total_union + 1e-7)).item()
    
    return global_iou

def calculate_true_iou_per_sample(pred, gt, threshold=0.5):
    """计算每个样本的真实IoU"""
    pred_bin = (pred > threshold).float()
    gt_bin = (gt > threshold).float()
    
    true_ious = []
    for i in range(pred_bin.shape[0]):
        intersection = (pred_bin[i] * gt_bin[i]).sum()
        union = pred_bin[i].sum() + gt_bin[i].sum() - intersection
        iou = intersection / (union + 1e-7)
        true_ious.append(iou.item())
    
    return true_ious

def test_iou_calculation():
    """测试IoU计算方法"""
    print("🧪 IoU计算方法验证")
    print("=" * 50)
    
    # 创建测试数据
    predictions, gt_masks = create_synthetic_data()
    
    print("📊 测试数据信息:")
    print(f"  - 批次大小: {predictions.shape[0]}")
    print(f"  - 图像尺寸: {predictions.shape[2]}x{predictions.shape[3]}")
    
    # 计算每个样本的真实IoU
    true_ious = calculate_true_iou_per_sample(predictions, gt_masks)
    print(f"\n📈 每个样本的真实IoU:")
    for i, iou in enumerate(true_ious):
        print(f"  - 样本{i+1}: {iou:.6f}")
    
    # 理论上的正确平均IoU
    theoretical_mean = np.mean(true_ious)
    print(f"\n🎯 理论平均IoU: {theoretical_mean:.6f}")
    
    # 方法1: 批次内平均
    method1_iou, batch_ious = calculate_iou_method1(predictions, gt_masks)
    print(f"\n📊 方法1 (批次内平均):")
    print(f"  - 结果: {method1_iou:.6f}")
    print(f"  - 与理论值差异: {abs(method1_iou - theoretical_mean):.6f}")
    
    # 方法2: 全局累积
    method2_iou = calculate_iou_method2(predictions, gt_masks)
    print(f"\n📊 方法2 (全局累积):")
    print(f"  - 结果: {method2_iou:.6f}")
    print(f"  - 与理论值差异: {abs(method2_iou - theoretical_mean):.6f}")
    
    # 分析差异
    print(f"\n🔍 方法差异分析:")
    print(f"  - 方法1 vs 方法2: {abs(method1_iou - method2_iou):.6f}")
    
    if abs(method1_iou - theoretical_mean) < 1e-6:
        print(f"  ✅ 方法1与理论值一致")
    else:
        print(f"  ⚠️ 方法1与理论值存在差异")
    
    if abs(method2_iou - theoretical_mean) < 1e-6:
        print(f"  ✅ 方法2与理论值一致")
    else:
        print(f"  ⚠️ 方法2与理论值存在差异")
    
    return {
        'theoretical_mean': theoretical_mean,
        'method1_result': method1_iou,
        'method2_result': method2_iou,
        'true_ious': true_ious
    }

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🧪 边界情况测试")
    print("=" * 50)
    
    # 情况1: 全部为空
    pred_empty = torch.zeros(2, 1, 32, 32)
    gt_empty = torch.zeros(2, 1, 32, 32)
    
    iou1 = calculate_iou_method1(pred_empty, gt_empty)[0]
    iou2 = calculate_iou_method2(pred_empty, gt_empty)
    print(f"全空情况 - 方法1: {iou1:.6f}, 方法2: {iou2:.6f}")
    
    # 情况2: 全部为满
    pred_full = torch.ones(2, 1, 32, 32)
    gt_full = torch.ones(2, 1, 32, 32)
    
    iou1 = calculate_iou_method1(pred_full, gt_full)[0]
    iou2 = calculate_iou_method2(pred_full, gt_full)
    print(f"全满情况 - 方法1: {iou1:.6f}, 方法2: {iou2:.6f}")
    
    # 情况3: 预测为空，真值非空
    pred_empty = torch.zeros(2, 1, 32, 32)
    gt_partial = torch.zeros(2, 1, 32, 32)
    gt_partial[0, 0, 10:20, 10:20] = 1.0
    gt_partial[1, 0, 15:25, 15:25] = 1.0
    
    iou1 = calculate_iou_method1(pred_empty, gt_partial)[0]
    iou2 = calculate_iou_method2(pred_empty, gt_partial)
    print(f"预测空真值非空 - 方法1: {iou1:.6f}, 方法2: {iou2:.6f}")

def main():
    """主函数"""
    print("🎯 RTGlassNet IoU计算验证")
    print("=" * 60)
    
    # 基础测试
    results = test_iou_calculation()
    
    # 边界情况测试
    test_edge_cases()
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"=" * 50)
    
    method1_error = abs(results['method1_result'] - results['theoretical_mean'])
    method2_error = abs(results['method2_result'] - results['theoretical_mean'])
    
    if method1_error < method2_error:
        print(f"✅ 方法1 (批次内平均) 更准确")
        print(f"   推荐在训练代码中使用方法1")
    elif method2_error < method1_error:
        print(f"✅ 方法2 (全局累积) 更准确")
        print(f"   推荐在训练代码中使用方法2")
    else:
        print(f"✅ 两种方法结果一致")
    
    print(f"\n🔧 修正建议:")
    if method2_error <= method1_error:
        print(f"   建议使用全局累积方法计算IoU:")
        print(f"   ```python")
        print(f"   total_intersection += (pred_bin * gt_masks).sum()")
        print(f"   total_union += pred_bin.sum() + gt_masks.sum() - intersection")
        print(f"   final_iou = total_intersection / (total_union + 1e-7)")
        print(f"   ```")
    
    print(f"\n🎯 当前训练代码已修正为使用全局累积方法！")

if __name__ == '__main__':
    main()
