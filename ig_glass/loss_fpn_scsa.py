"""
@Time    : 2025
<AUTHOR> Glennine & Gemini

@Project : IG_SLAM
@File    : loss_fpn_scsa.py
@Function: Loss function for the modernized GDNetFPNSCSA model.

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

def structure_loss(pred, mask):
    """BCE and IoU loss for segmentation"""
    weit = 1 + 5 * torch.abs(F.avg_pool2d(mask, kernel_size=31, stride=1, padding=15) - mask)
    wbce = F.binary_cross_entropy_with_logits(pred, mask, reduction='none')
    wbce = (weit * wbce).sum(dim=(2, 3)) / weit.sum(dim=(2, 3))

    pred = torch.sigmoid(pred)
    inter = ((pred * mask) * weit).sum(dim=(2, 3))
    union = ((pred + mask) * weit).sum(dim=(2, 3))
    wiou = 1 - (inter + 1) / (union - inter + 1)

    return (wbce + wiou).mean()


class GDNetFPNSCSALoss(nn.Module):
    """
    Computes the total loss for GDNetFPNSCSA.
    The total loss is a weighted sum of the losses for the main prediction
    and the CRF-refined prediction.
    """
    def __init__(self, main_weight=1.0, refined_weight=0.5):
        super(GDNetFPNSCSALoss, self).__init__()
        self.main_weight = main_weight
        self.refined_weight = refined_weight
        print(f"Loss configured with main_weight={main_weight}, refined_weight={refined_weight}")

    def forward(self, predictions, gts):
        """
        Args:
            predictions (dict): A dict of predictions from the model.
                                Expected keys: 'main_logits', 'refined_pred'
            gts (dict): A dict of ground truths.
                        Expected keys: 'gt'
        """
        main_logits = predictions.get('main_logits')
        refined_pred = predictions.get('refined_pred')
        gt_mask = gts.get('gt')

        if main_logits is None or refined_pred is None or gt_mask is None:
            raise ValueError("'main_logits', 'refined_pred', and 'gt' must be provided in predictions and gts dicts")

        # --- Loss for the main prediction (before CRF) ---
        loss_main = structure_loss(main_logits, gt_mask)

        # --- Loss for the refined prediction (after CRF) ---
        # refined_pred is already a probability, so we need to convert it to logits
        # for the binary_cross_entropy_with_logits function inside structure_loss.
        # Clamp to avoid log(0) issues.
        refined_pred_clamped = torch.clamp(refined_pred, 1e-7, 1.0 - 1e-7)
        refined_logits = torch.log(refined_pred_clamped / (1 - refined_pred_clamped))
        loss_refined = structure_loss(refined_logits, gt_mask)

        # --- Total Weighted Loss ---
        total_loss = self.main_weight * loss_main + self.refined_weight * loss_refined

        return {
            'total_loss': total_loss,
            'loss_main': loss_main,
            'loss_refined': loss_refined
        }
