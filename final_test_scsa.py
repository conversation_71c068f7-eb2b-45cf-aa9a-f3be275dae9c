#!/usr/bin/env python3
"""
SCSA模型最终独立测试脚本
🔒 严格避免数据泄露：仅在训练完全结束后使用
"""

import os
import sys
import argparse
import torch
import numpy as np
from torch.utils.data import DataLoader
from tqdm import tqdm

# 添加项目路径
sys.path.append('/home/<USER>/ws/IG_SLAM')

from ig_glass.glass_dataloader import GlassDataLoader
from ig_glass.gdnet_scsa import GDNetSCSA
from ig_glass.loss_scsa import SCSACompositeLoss

def parse_arguments():
    parser = argparse.ArgumentParser(description='SCSA模型最终独立测试')
    parser.add_argument('--model_path', type=str, required=True, help='训练好的模型权重路径')
    parser.add_argument('--bs', default=6, help='批次大小', type=int)
    parser.add_argument('--img_size', default=416, help='图像尺寸', type=int)
    parser.add_argument('--n_worker', default=4, help='数据加载线程数', type=int)
    parser.add_argument('--crf_iter', default=3, help='CRF迭代次数', type=int)
    parser.add_argument('--device', default='cuda', help='设备', type=str)
    
    return parser.parse_args()

def load_model(model_path, device, crf_iter=3):
    """加载训练好的模型"""
    print(f"🔍 加载模型: {model_path}")
    
    # 创建模型
    backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth'
    model = GDNetSCSA(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=True
    ).to(device)
    
    # 加载权重
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=device)
        
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
            epoch = checkpoint.get('epoch', 'unknown')
            train_iou = checkpoint.get('iou', 'unknown')
            print(f"✅ 成功加载模型检查点")
            print(f"   训练轮次: {epoch}")
            print(f"   内部验证IoU: {train_iou}")
        else:
            model.load_state_dict(checkpoint)
            print("✅ 成功加载模型权重")
    else:
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    model.eval()
    return model

def final_test(model, device, bs=6, img_size=416, n_worker=4):
    """
    🔒 最终独立测试 - 严格避免数据泄露
    仅使用独立的test_gdd数据集，训练过程中从未使用过
    """
    print("\n" + "="*60)
    print("🔒 开始最终独立测试 (test_gdd数据集)")
    print("📊 此数据集在训练过程中从未被使用，确保无数据泄露")
    print("="*60)
    
    # 创建独立测试数据集
    test_dataset = GlassDataLoader(
        mode='test_gdd',
        augment_data=False,  # 测试时不使用任何增强
        target_size=img_size
    )
    
    test_dataloader = DataLoader(
        test_dataset, 
        batch_size=bs, 
        shuffle=False,
        num_workers=n_worker
    )
    
    print(f"📂 独立测试集大小: {len(test_dataset)}张图像")
    
    # 测试指标
    running_loss = 0.0
    tp_fp = 0
    tp = 0
    pred_true = 0
    gt_true = 0
    mae_list = []
    iou_list = []
    total_pixels = 0
    acc_list = []
    
    # 损失函数（仅用于计算损失，不用于训练）
    criterion = SCSACompositeLoss(
        bce_weight=0.6,
        iou_weight=0.4,
        edge_weight=0.2
    )
    
    with torch.no_grad():
        test_tqdm = tqdm(test_dataloader, desc="🔍 独立测试中")
        for batch_idx, (inp_imgs, gt_masks) in enumerate(test_tqdm):
            inp_imgs = inp_imgs.to(device)
            gt_masks = gt_masks.to(device)
            
            # 前向传播
            h_pred, l_pred, final_pred, refined_pred = model(inp_imgs)
            
            # 计算损失
            loss = criterion((h_pred, l_pred, final_pred, refined_pred), gt_masks)
            running_loss += loss.item()
            
            # 使用refined_pred进行评估
            pred_masks = refined_pred[:, 1:2] if refined_pred.size(1) == 2 else refined_pred
            
            # 转换为numpy进行评估
            pred_np = pred_masks.detach().cpu().numpy()
            gt_np = gt_masks.detach().cpu().numpy()
            
            # 计算每个批次中每张图像的指标
            for b in range(pred_np.shape[0]):
                pred_single = pred_np[b, 0]
                gt_single = gt_np[b, 0]
                
                # 二值化预测
                pred_binary = (pred_single > 0.5).astype(np.float32)
                
                # 计算TP, TN
                TP = np.sum(np.logical_and(pred_binary, gt_single))
                TN = np.sum(np.logical_and(np.logical_not(pred_binary), np.logical_not(gt_single)))
                
                # 计算准确率
                N_p = np.sum(gt_single)
                N_n = np.sum(np.logical_not(gt_single))
                acc = (TP + TN) / (N_p + N_n)
                
                # 计算IoU
                intersection = TP
                union = np.sum(pred_binary) + np.sum(gt_single) - intersection
                iou = intersection / union if union > 0 else 0
                
                # 累积指标
                tp_fp += TP + TN
                tp += TP
                pred_true += np.sum(pred_binary)
                gt_true += N_p
                iou_list.append(iou)
                acc_list.append(acc)
                total_pixels += (N_p + N_n)
            
            # 记录绝对误差
            if gt_masks.dim() == 3:
                gt_masks = gt_masks.unsqueeze(1)
            ae = torch.mean(torch.abs(pred_masks - gt_masks), dim=(1, 2, 3)).cpu().numpy()
            mae_list.extend(ae)
            
            # 更新进度条
            test_tqdm.set_postfix(
                loss=loss.item(), 
                avg_loss=running_loss/(batch_idx+1),
                avg_iou=np.mean(iou_list)
            )
    
    # 计算最终指标
    final_loss = running_loss / len(test_dataloader)
    accuracy = tp_fp / total_pixels if total_pixels > 0 else 0
    mae = np.mean(mae_list)
    iou = np.mean(iou_list)
    
    return {
        'loss': final_loss,
        'accuracy': accuracy,
        'mae': mae,
        'iou': iou,
        'total_images': len(test_dataset)
    }

def main():
    args = parse_arguments()
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  使用设备: {device}")
    
    # 加载模型
    model = load_model(args.model_path, device, args.crf_iter)
    
    # 最终独立测试
    results = final_test(
        model, 
        device, 
        bs=args.bs, 
        img_size=args.img_size, 
        n_worker=args.n_worker
    )
    
    # 打印最终结果
    print("\n" + "="*60)
    print("🎯 最终独立测试结果 (test_gdd数据集)")
    print("="*60)
    print(f"📊 测试图像数量: {results['total_images']}张")
    print(f"🎯 IoU:          {results['iou']:.4f} ({results['iou']*100:.2f}%)")
    print(f"📈 准确率:        {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
    print(f"📉 MAE:          {results['mae']:.4f}")
    print(f"📊 损失:         {results['loss']:.4f}")
    print("="*60)
    
    # 性能评估
    if results['iou'] >= 0.85:
        print("🎉 优秀! IoU >= 85%，模型泛化能力良好!")
    elif results['iou'] >= 0.80:
        print("👍 良好! IoU >= 80%，模型性能可接受!")
    elif results['iou'] >= 0.75:
        print("📈 一般，IoU >= 75%，还有改进空间!")
    else:
        print("📝 需要改进，IoU < 75%，建议优化模型!")
    
    print(f"\n💡 提醒: 这是在完全独立的测试集上的结果，")
    print(f"   训练过程中从未使用过此数据集，确保无数据泄露。")
    
    return results

if __name__ == '__main__':
    main() 