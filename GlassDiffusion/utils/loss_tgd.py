# utils/loss_tgd.py
# -----------------------------------------------------------------------------
# TransXGlassNet专用损失函数（支持边缘损失）
# -----------------------------------------------------------------------------
import torch
import torch.nn as nn
from .losses import FocalLoss, IoULoss, EdgeLoss, DiceBCELoss

def create_tgd_loss(focal_weight=0.5, iou_weight=0.4, edge_weight=0.2, dice_weight=0.7, bce_weight=0.3):
    return TGDCombinedLoss(focal_weight, iou_weight, edge_weight, dice_weight, bce_weight)

class TGDCombinedLoss(nn.Module):
    def __init__(self, focal_weight=0.5, iou_weight=0.4, edge_weight=0.2, dice_weight=0.7, bce_weight=0.3):
        super().__init__()
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
        self.iou_loss = IoULoss()
        self.edge_loss = EdgeLoss()
        self.dice_bce_loss = DiceBCELoss(dice_weight=dice_weight, bce_weight=bce_weight)
        self.total_weight = focal_weight + iou_weight + edge_weight
        print(f"📊 TGD损失权重: Focal({focal_weight}) + IoU({iou_weight}) + Edge({edge_weight}) + DiceBCE(0.1)")
    def forward(self, outputs, gt_mask):
        losses = {}
        main_pred_logits = outputs['main_pred']
        # Focal损失
        focal_loss = self.focal_loss(main_pred_logits, gt_mask)
        losses['focal_loss'] = focal_loss
        # IoU损失
        iou_loss = self.iou_loss(main_pred_logits, gt_mask)
        losses['iou_loss'] = iou_loss
        # 边缘损失
        if 'edge_pred' in outputs:
            edge_loss = self.edge_loss(outputs['edge_pred'], gt_mask)
        else:
            edge_loss = self.edge_loss(main_pred_logits, gt_mask)
        losses['edge_loss'] = edge_loss
        # Dice+BCE损失
        dice_bce_loss = self.dice_bce_loss(main_pred_logits, gt_mask)
        losses['dice_bce_loss'] = dice_bce_loss
        # 总损失
        total_loss = (self.focal_weight * focal_loss +
                      self.iou_weight * iou_loss +
                      self.edge_weight * edge_loss +
                      0.1 * dice_bce_loss)
        losses['total_loss'] = total_loss
        # 数值稳定性保护
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print(f"⚠️ 检测到数值异常！")
            total_loss = torch.tensor(1.0, device=total_loss.device, requires_grad=True)
        return total_loss, losses 