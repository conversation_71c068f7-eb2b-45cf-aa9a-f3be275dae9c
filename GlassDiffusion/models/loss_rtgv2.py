"""
 @Time    : 2025
 <AUTHOR> Glennine & Gemini

 @Project : IG_SLAM
 @File    : loss_rtgv2.py
 @Function: RTGlassNetv2的损失函数 V2. 全面深度监督+CRF优化.
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

#------------------------------------------------------------------------------
# 辅助损失模块 (这些模块设计得很好，我们保留它们)
#------------------------------------------------------------------------------

class EdgeAwareFocalLoss(nn.Module):
    """边缘感知Focal损失函数"""
    def __init__(self, alpha=0.25, gamma=2.0, edge_weight=1.3):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.edge_weight = edge_weight
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1., 8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))

    def forward(self, logits, target):
        target = torch.clamp(target, min=0.0, max=1.0)
        target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
        edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
        
        bce_loss = F.binary_cross_entropy_with_logits(logits, target, reduction='none')
        pred_prob = torch.sigmoid(logits)
        p_t = pred_prob * target + (1 - pred_prob) * (1 - target)
        loss = bce_loss * ((1 - p_t) ** self.gamma)

        if self.alpha >= 0:
            alpha_t = self.alpha * target + (1 - self.alpha) * (1 - target)
            loss = alpha_t * loss
        
        weighted_loss = loss * edge_weight_map
        return weighted_loss.mean()

class LightIOU(nn.Module):
    """轻量级IoU损失函数"""
    def __init__(self):
        super().__init__()
        
    def forward(self, logits, target, eps=1e-7):
        pred = torch.sigmoid(logits)
        target = torch.clamp(target, min=0.0, max=1.0)
        intersection = torch.sum(target * pred, dim=(1, 2, 3))
        union = torch.sum(target + pred - target * pred, dim=(1, 2, 3))
        iou = (intersection + eps) / (union + eps)
        return 1.0 - iou.mean()

class EdgeAwareBCELoss(nn.Module):
    """边缘感知BCE损失函数"""
    def __init__(self, edge_weight=1.3):
        super().__init__()
        self.edge_weight = edge_weight
        self.bce_loss = nn.BCEWithLogitsLoss(reduction='none')
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.], 
            [-1., 8., -1.], 
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))

    def forward(self, logits, target):
        target = torch.clamp(target, min=0.0, max=1.0)
        target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
        edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
        bce = self.bce_loss(logits, target)
        weighted_loss = bce * edge_weight_map
        return weighted_loss.mean()

class DistanceTransformLoss(nn.Module):
    """距离变换损失"""
    def __init__(self, l1_weight=0.7, mse_weight=0.3):
        super().__init__()
        self.l1_weight = l1_weight
        self.mse_weight = mse_weight
    
    def forward(self, dt_logits, dt_target):
        dt_pred = torch.sigmoid(dt_logits)
        dt_target = torch.clamp(dt_target, 0.0, 1.0)
        l1 = F.l1_loss(dt_pred, dt_target)
        mse = F.mse_loss(dt_pred, dt_target)
        return self.l1_weight * l1 + self.mse_weight * mse

#------------------------------------------------------------------------------
# 主损失函数 (全新重构)
#------------------------------------------------------------------------------

class RTGlassNetv2Loss(nn.Module):
    """
    RTGlassNetv2的V2版损失函数.
    - 适配模型输出的字典格式.
    - 实现对所有预测头的全面深度监督.
    - 对CRF精炼后的结果施加最强的损失权重.
    """
    def __init__(self, focal_weight=0.5, iou_weight=0.3, edge_weight=0.2, dt_weight=0.1, alpha=0.25, gamma=2.0):
        super().__init__()
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.edge_weight = edge_weight
        self.dt_weight = dt_weight

        # 初始化各种损失函数
        self.focal_loss = EdgeAwareFocalLoss(alpha=alpha, gamma=gamma)
        self.iou_loss = LightIOU()
        self.edge_loss = EdgeAwareBCELoss()
        self.dt_loss = DistanceTransformLoss()

    def _compute_composite_loss(self, logits, target):
        """为基于Logits的预测计算组合损失 (Focal + IoU + Edge)"""
        if logits is None:
            return 0.0
        
        focal = self.focal_loss(logits, target)
        iou = self.iou_loss(logits, target)
        edge = self.edge_loss(logits, target)
        
        return self.focal_weight * focal + self.iou_weight * iou + self.edge_weight * edge

    def _compute_prob_loss(self, pred, target):
        """为已经是概率(Probability)的预测计算损失 (例如CRF的输出)"""
        if pred is None:
            return 0.0
        
        # 将概率转回logits以复用现有损失函数，增加数值稳定性
        eps = 1e-7
        logits = torch.log(torch.clamp(pred, min=eps, max=1.0 - eps) / torch.clamp(1.0 - pred, min=eps, max=1.0 - eps))
        
        return self._compute_composite_loss(logits, target)

    def forward(self, predictions, target, dt_target=None):
        """
        计算总损失.
        Args:
            predictions (dict): 模型输出的字典.
            target (torch.Tensor): Ground truth mask [B, 1, H, W].
            dt_target (torch.Tensor, optional): 距离变换GT.
        """
        # --- 1. 从字典中安全地获取所有预测 ---
        main_logits = predictions.get('main_logits')
        dt_logits = predictions.get('dt_logits')
        refined_pred = predictions.get('refined_pred')
        
        # 深度监督的预测 (已经是概率值)
        sup_h_pred = predictions.get('sup_h_pred')
        sup_l_pred = predictions.get('sup_l_pred')
        sup_final_pred = predictions.get('sup_final_pred')

        # --- 2. 计算各部分的损失 ---
        # 对CRF精炼后的输出计算损失 (权重最高)
        loss_refined = self._compute_prob_loss(refined_pred, target)
        
        # 对主预测计算损失
        loss_main = self._compute_composite_loss(main_logits, target)
        
        # 对深度监督的预测计算损失
        loss_sup_h = self._compute_prob_loss(sup_h_pred, target)
        loss_sup_l = self._compute_prob_loss(sup_l_pred, target)
        loss_sup_final = self._compute_prob_loss(sup_final_pred, target)
        
        # 对距离变换预测计算损失
        loss_dt = 0.0
        if self.dt_weight > 0 and dt_logits is not None and dt_target is not None:
            loss_dt = self.dt_loss(dt_logits, dt_target.to(dt_logits.device))

        # --- 3. 深度监督加权求和 ---
        # 权重分配: refined > main > final_sup > (h_sup, l_sup)
        total_loss = (
            1.0 * loss_refined +
            0.8 * loss_main +
            0.4 * loss_sup_final +
            0.2 * loss_sup_h +
            0.2 * loss_sup_l +
            self.dt_weight * loss_dt
        )
        
        # --- 4. 返回总损失和损失字典，方便调试 ---
        loss_dict = {
            'total_loss': total_loss.item(),
            'loss_refined': loss_refined.item() if isinstance(loss_refined, torch.Tensor) else loss_refined,
            'loss_main': loss_main.item() if isinstance(loss_main, torch.Tensor) else loss_main,
            'loss_sup_final': loss_sup_final.item() if isinstance(loss_sup_final, torch.Tensor) else loss_sup_final,
            'loss_sup_h': loss_sup_h.item() if isinstance(loss_sup_h, torch.Tensor) else loss_sup_h,
            'loss_sup_l': loss_sup_l.item() if isinstance(loss_sup_l, torch.Tensor) else loss_sup_l,
            'loss_dt': loss_dt.item() if isinstance(loss_dt, torch.Tensor) else loss_dt,
        }
        
        return total_loss, loss_dict