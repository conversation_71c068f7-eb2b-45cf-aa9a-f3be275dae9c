2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_setup.py:_flush():81] Configure stats pid to 46899
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/.config/wandb/settings
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/ws/IG_SLAM/ProteusGlassDiffusion/wandb/settings
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/ws/IG_SLAM/ProteusGlassDiffusion/wandb/offline-run-20250630_193231-u9m6m5dz/logs/debug.log
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/ws/IG_SLAM/ProteusGlassDiffusion/wandb/offline-run-20250630_193231-u9m6m5dz/logs/debug-internal.log
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_init.py:init():831] calling init triggers
2025-06-30 19:32:31,169 INFO    MainThread:46899 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'stage': 1, 'model': 'ProteusGlassNet_HierarchicalSupervision', 'loss_function': 'HierarchicalSupervisionLoss', 'supervision_strategy': 'P2_Edge+Deep_Transparency+Main_Prediction', 'epochs': 200, 'batch_size': 8, 'learning_rate': 0.0005, 'image_size': (420, 420), 'vit_model': 'vit_small', 'fpn_layers': 4, 'hierarchical_weights': '[0.7, 0.5, 0.3, 0.1]', 'target_iou': '90%+', '_wandb': {}}
2025-06-30 19:32:31,170 INFO    MainThread:46899 [wandb_init.py:init():872] starting backend
2025-06-30 19:32:31,381 INFO    MainThread:46899 [wandb_init.py:init():875] sending inform_init request
2025-06-30 19:32:31,414 INFO    MainThread:46899 [wandb_init.py:init():883] backend started and connected
2025-06-30 19:32:31,416 INFO    MainThread:46899 [wandb_init.py:init():956] updated telemetry
2025-06-30 19:32:31,427 INFO    MainThread:46899 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-30 19:32:31,521 INFO    MainThread:46899 [wandb_init.py:init():1032] starting run threads in backend
2025-06-30 19:32:31,605 INFO    MainThread:46899 [wandb_run.py:_console_start():2453] atexit reg
2025-06-30 19:32:31,605 INFO    MainThread:46899 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-30 19:32:31,606 INFO    MainThread:46899 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-30 19:32:31,606 INFO    MainThread:46899 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-30 19:32:31,606 INFO    MainThread:46899 [wandb_init.py:init():1078] run started, returning control to user process
2025-06-30 19:32:35,526 INFO    MsgRouterThr:46899 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 0 handles.
