#!/usr/bin/env python3
"""
🔧 修复后的test_gdd.py - 正确使用refined_pred
解决CRF输出通道数问题
"""

import os
import warnings
import cv2
import numpy as np
import torch
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',
    'scale': 384,
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理
img_transform = <PERSON><PERSON>([
    <PERSON><PERSON>(height=args['scale'], width=args['scale']),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ToTensorV2()
])

def detect_glass_and_evaluate_refined(image_folder, output_folder, gt_folder, model, glass_threshold):
    """使用修复后的refined_pred进行检测和评估"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files.sort()
    
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0
    count = 0
    debug_count = 0
    
    print(f"🎯 使用修复后的refined_pred（CRF后处理）")
    
    for image_file in tqdm(image_files, desc="Processing with refined_pred"):
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        
        # 读取图像
        img_rgb = cv2.imread(image_path)
        if img_rgb is None:
            continue
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
        
        # 读取GT
        if not os.path.exists(gt_path):
            continue
        gt_mask_u8 = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        if gt_mask_u8 is None:
            continue
        
        # 预处理
        transformed = img_transform(image=img_rgb)
        img_var = transformed['image'].unsqueeze(0).cuda(device_ids[0])
        
        # 模型推理
        with torch.no_grad():
            outputs = model(img_var)
            
            # 🎯 使用修复后的refined_pred
            refined_pred = outputs['refined_pred']
            
            # 调试前几张图像
            if debug_count < 3:
                print(f"\n🔍 调试 {image_file}:")
                print(f"  refined_pred shape: {refined_pred.shape}")
                print(f"  refined_pred range: [{refined_pred.min():.6f}, {refined_pred.max():.6f}]")
                print(f"  refined_pred mean: {refined_pred.mean():.6f}")
                
                # 检查是否有异常值
                if torch.isnan(refined_pred).any():
                    print(f"  ⚠️ refined_pred包含NaN")
                if torch.isinf(refined_pred).any():
                    print(f"  ⚠️ refined_pred包含Inf")
                if refined_pred.max() - refined_pred.min() < 1e-6:
                    print(f"  ⚠️ refined_pred几乎为常数")
                
                debug_count += 1
            
            # 数值稳定性检查
            if torch.isnan(refined_pred).any() or torch.isinf(refined_pred).any():
                print(f"⚠️ {image_file}: refined_pred包含异常值，使用main_pred")
                refined_pred = outputs['main_pred']
            
            # 确保在合理范围内
            refined_pred = torch.clamp(refined_pred, 0.0, 1.0)
            
            # 提取预测概率图
            if refined_pred.dim() == 4:
                pred_prob_map = refined_pred.squeeze(0).squeeze(0)
            else:
                pred_prob_map = refined_pred.squeeze()
        
        # 二值化
        binary_pred = (pred_prob_map > glass_threshold).float()
        prediction_float = binary_pred.cpu().numpy()
        
        # 处理GT（确保尺寸匹配）
        if gt_mask_u8.shape != prediction_float.shape:
            gt_resized = cv2.resize(gt_mask_u8, (prediction_float.shape[1], prediction_float.shape[0]))
        else:
            gt_resized = gt_mask_u8
        
        gt_float = (gt_resized / 255.0).astype(np.float32)
        
        # 保存预测结果
        pred_to_save = (prediction_float * 255).astype(np.uint8)
        output_path = os.path.join(output_folder, os.path.splitext(image_file)[0] + '.png')
        cv2.imwrite(output_path, pred_to_save)
        
        # 保存调试信息（前几张图像）
        if debug_count <= 3:
            # 保存原始预测概率图
            prob_np = pred_prob_map.cpu().numpy()
            prob_to_save = (prob_np * 255).astype(np.uint8)
            prob_path = os.path.join(output_folder, os.path.splitext(image_file)[0] + '_refined_prob.png')
            cv2.imwrite(prob_path, prob_to_save)
        
        # 计算指标
        try:
            tiou = compute_iou(prediction_float, gt_float)
            tacc = compute_acc(prediction_float, gt_float)
            precision, recall = compute_precision_recall(prediction_float, gt_float)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(prediction_float, gt_float)
            tber = compute_ber(prediction_float, gt_float)
            taber = compute_aber(prediction_float, gt_float)
            
            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber += taber
            
        except Exception as e:
            print(f"⚠️ 计算指标时出错 {image_file}: {e}")
            continue
    
    if count > 0:
        return iou/count, acc/count, fm/count, mae/count, ber/count, aber/count
    else:
        return 0, 0, 0, 0, 0, 0

def compare_main_vs_refined(image_folder, gt_folder, model, num_samples=5):
    """对比main_pred和refined_pred的性能"""
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files = image_files[:num_samples]
    
    print(f"\n📊 对比main_pred vs refined_pred (前{num_samples}张图像):")
    
    main_ious = []
    refined_ious = []
    
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        
        if not os.path.exists(gt_path):
            continue
            
        # 读取图像和GT
        img_rgb = cv2.imread(image_path)
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
        gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        
        # 预处理
        transformed = img_transform(image=img_rgb)
        img_tensor = transformed['image'].unsqueeze(0).cuda()
        gt_resized = cv2.resize(gt_mask, (args['scale'], args['scale']))
        gt_float = (gt_resized / 255.0).astype(np.float32)
        
        # 模型推理
        with torch.no_grad():
            outputs = model(img_tensor)
            
            # main_pred IoU
            main_pred = outputs['main_pred'].squeeze().cpu().numpy()
            main_binary = (main_pred > args['glass_threshold']).astype(np.float32)
            main_iou = compute_iou(main_binary, gt_float)
            main_ious.append(main_iou)
            
            # refined_pred IoU
            refined_pred = outputs['refined_pred'].squeeze().cpu().numpy()
            refined_binary = (refined_pred > args['glass_threshold']).astype(np.float32)
            refined_iou = compute_iou(refined_binary, gt_float)
            refined_ious.append(refined_iou)
            
            print(f"  {image_file}: main={main_iou:.4f}, refined={refined_iou:.4f}, diff={refined_iou-main_iou:+.4f}")
    
    if main_ious and refined_ious:
        main_avg = np.mean(main_ious)
        refined_avg = np.mean(refined_ious)
        print(f"\n  平均IoU: main={main_avg:.4f}, refined={refined_avg:.4f}, diff={refined_avg-main_avg:+.4f}")
        
        if refined_avg > main_avg:
            print(f"  ✅ CRF提升了性能，应该使用refined_pred")
        else:
            print(f"  ⚠️ CRF降低了性能，可能需要调优参数")

def main():
    print("🎯 使用修复后refined_pred的测试脚本")
    print("=" * 60)
    
    # 加载模型
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"✅ 加载模型: {model_path}")
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    model = RTGlassNet(
        backbone_type=backbone_type, 
        crf_iter=args['crf_iter'], 
        crf_bilateral_weight=args['crf_bilateral_weight']
    )
    
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    model.cuda(device_ids[0])
    model.eval()
    
    # 数据路径
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    image_folder = os.path.join(data_path, "image")
    output_folder = os.path.join(data_path, "glass_mask_gdd_refined_fixed")
    gt_folder = os.path.join(data_path, "mask")
    
    # 先对比main_pred和refined_pred
    compare_main_vs_refined(image_folder, gt_folder, model)
    
    print(f"\n🚀 开始完整测试（使用修复后的refined_pred）...")
    
    # 运行完整测试
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate_refined(
        image_folder, output_folder, gt_folder, model, args['glass_threshold']
    )
    
    print(f"\n" + "="*60)
    print(f"      使用修复后refined_pred的测试结果")
    print(f"="*60)
    print(f"  IoU (Intersection over Union): {iou:.4f}")
    print(f"  Accuracy:                      {acc:.4f}")
    print(f"  F-measure:                     {fm:.4f}")
    print(f"  MAE (Mean Absolute Error):     {mae:.4f}")
    print(f"  BER (Balanced Error Rate):     {ber:.4f}")
    print(f"  ABER (Adaptive BER):           {aber:.4f}")
    print(f"="*60)
    
    print(f"\n💡 修复说明:")
    print(f"1. ✅ 修复了CRF输出通道数问题")
    print(f"2. ✅ 正确选择前景通道")
    print(f"3. ✅ 添加了数值稳定性检查")
    print(f"4. ✅ 提供了main_pred vs refined_pred对比")

if __name__ == '__main__':
    main()
