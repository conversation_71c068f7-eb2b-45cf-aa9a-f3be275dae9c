# models/tgd_net.py
# -----------------------------------------------------------------------------
# TransXNet骨干版玻璃分割网络（SOTA多尺度融合架构）
# -----------------------------------------------------------------------------
import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
from typing import List, Dict

# 添加TransXNet路径（假设TransXNet在项目根目录下）
TRANSXNET_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../TransXNet/models'))
if TRANSXNET_PATH not in sys.path:
    sys.path.append(TRANSXNET_PATH)
from transxnet import TransXNet

# 导入本地CRF实现
from .diff_crf import SimplifiedDiffCRF

# 轻量化边缘增强模块（直接复用pgd_net实现）
class LightEdgeEnhancer(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
    def forward(self, x):
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        enhanced = self.edge_conv(x)
        return x + enhanced * edge_features

# 多尺度TransXNet特征提取器
class MultiScaleTransXNetFeatureExtractor(nn.Module):
    """
    多尺度TransXNet特征提取器，输出4个stage特征，适配FPN
    """
    def __init__(self, arch='tiny', pretrained_path=None, out_dims=[256, 256, 256, 256]):
        super().__init__()
        self.backbone = TransXNet(arch=arch, fork_feat=True, num_classes=0)
        if pretrained_path and os.path.exists(pretrained_path):
            # 修正：如果路径是目录，则加载目录下的data.pkl文件
            if os.path.isdir(pretrained_path):
                weight_file = os.path.join(pretrained_path, 'data.pkl')
            else:
                weight_file = pretrained_path
            
            if not os.path.exists(weight_file):
                print(f"⚠️ 权重文件不存在: {weight_file}")
                return

            print(f"📦 加载TransXNet预训练权重: {weight_file}")
            state = torch.load(weight_file, map_location='cpu')
            self.backbone.load_state_dict(state, strict=False)
        else:
            print("⚠️ 未加载TransXNet预训练权重，使用随机初始化！")
        # 适配器，将各stage特征统一到FPN输入维度
        self.adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_dim, out_dim, kernel_size=1, bias=False),
                nn.BatchNorm2d(out_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_dim, out_dim, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(out_dim),
                nn.ReLU(inplace=True)
            ) for in_dim, out_dim in zip(self.backbone.arch_settings[arch]['embed_dims'], out_dims)
        ])
    def forward(self, x):
        feats = self.backbone(x)  # list: [stage1, stage2, stage3, stage4]
        assert isinstance(feats, list) and len(feats) == 4
        out = [adapter(f) for f, adapter in zip(feats, self.adapters)]
        return out

# FPN解码器、FeatureRefiner、GeometryHead、SCSA等直接复用pgd_net.py实现
from .pgd_net import FPNDecoder, FeatureRefiner, GeometryHead, SCSA

# 主网络
class TransXGlassNet(nn.Module):
    """
    TransXGlassNet - 基于TransXNet骨干的多尺度玻璃分割网络
    """
    def __init__(self, arch='tiny', backbone_path=None, crf_iter=3, trainable_crf=True):
        super().__init__()
        print("🚀 初始化 TransXGlassNet ...")
        # 1. 多尺度特征提取
        self.backbone = MultiScaleTransXNetFeatureExtractor(
            arch=arch,
            pretrained_path=backbone_path,
            out_dims=[256, 256, 256, 256]
        )
        # 2. FPN解码
        self.fpn_decoder = FPNDecoder(feature_dims=[256, 256, 256, 256], output_dim=256)
        # 3. 特征精炼
        self.feature_refiner = FeatureRefiner(in_channels=256, out_channels=256)
        self.edge_enhancer = LightEdgeEnhancer(256)
        # 4. 几何头
        self.geometry_head = GeometryHead(in_channels=256, geometry_channels=64)
        # 5. SCSA注意力
        scsa_dim = 256 + 64
        scsa_dim = ((scsa_dim + 3) // 4) * 4
        self.scsa_adapter = nn.Conv2d(256 + 64, scsa_dim, kernel_size=1)
        self.scsa_attention = SCSA(dim=scsa_dim, head_num=8, window_size=7)
        # 6. 预测头
        self.main_predict = nn.Sequential(
            nn.Conv2d(scsa_dim, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 1, kernel_size=1)
        )
        self.transparency_predict = nn.Conv2d(256, 1, kernel_size=1)
        # 7. CRF
        self.crf = SimplifiedDiffCRF(n_iter=crf_iter, trainable=trainable_crf, bilateral_weight=5.0)
        print("✅ TransXGlassNet 初始化完成!")
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        input_size = x.shape[2:]
        # 1. 特征提取
        multi_scale_features = self.backbone(x)
        # 2. FPN解码
        fpn_features = self.fpn_decoder(multi_scale_features)
        # 3. 特征精炼
        refined_features = self.feature_refiner(fpn_features['p2'])
        enhanced_features = self.edge_enhancer(refined_features)
        # 4. 几何头
        geometry_outputs = self.geometry_head(enhanced_features)
        # 5. SCSA
        scsa_input = torch.cat([enhanced_features, geometry_outputs['main_logits']], dim=1)
        scsa_feat = self.scsa_adapter(scsa_input)
        scsa_feat = self.scsa_attention(scsa_feat)
        # 6. 主预测
        main_logits = self.main_predict(scsa_feat)
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        dt_logits = F.interpolate(geometry_outputs['dt_logits'], size=input_size, mode='bilinear', align_corners=True)
        transparency_logits = F.interpolate(self.transparency_predict(fpn_features['p0']), size=input_size, mode='bilinear', align_corners=True)
        # 7. 激活
        main_pred = torch.sigmoid(main_logits)
        dt_pred = torch.sigmoid(dt_logits)
        transparency_pred = torch.sigmoid(transparency_logits)
        # 8. 输出
        outputs = {
            'main_pred': main_pred,
            'dt_pred': dt_pred,
            'transparency_pred': transparency_pred,
            'edge_pred': dt_pred,
            'final_pred': main_pred,
            'ensemble_pred': 0.7 * main_pred + 0.3 * dt_pred,
            'refined_pred': main_pred
        }
        return outputs

__all__ = [
    'MultiScaleTransXNetFeatureExtractor',
    'TransXGlassNet'
] 