#!/usr/bin/env python3
"""
🎯 RTGlassNet稳定性优化训练脚本
专门解决IoU起伏问题的终极修复版本
"""

import os
import warnings
import numpy as np
import random

# 抑制各种警告信息
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import math

def set_seed(seed=42):
    """设置随机种子，确保可复现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"🎲 随机种子设置为: {seed}")

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet稳定性优化训练 - 修复起伏问题')
    
    # 🔧 稳定性优化参数
    parser.add_argument('--epochs', default=200, type=int, help='训练轮数')
    parser.add_argument('--bs', default=12, type=int, help='🔧 批次大小增加：6→12，减少方差')
    parser.add_argument('--lr', default=0.0003, type=float, help='🔧 更保守学习率：0.0005→0.0003')
    parser.add_argument('--min_lr', default=0.00001, type=float, help='最小学习率')
    parser.add_argument('--warmup_epochs', default=10, type=int, help='🔧 更长预热：5→10轮')
    parser.add_argument('--wd', default=0.0001, type=float, help='🔧 减小权重衰减：0.0005→0.0001')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--aug', default=True, type=bool, help='数据增强')
    parser.add_argument('--n_worker', default=4, type=int, help='数据加载器工作进程数')
    
    # 训练控制参数
    parser.add_argument('--test_interval', default=3, type=int, help='🔧 更频繁验证：5→3轮')
    parser.add_argument('--save_interval', default=15, type=int, help='保存间隔')
    parser.add_argument('--log_interval', default=50, type=int, help='日志记录间隔')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--use_gpu', default=True, type=bool, help='使用GPU')
    
    # 🎯 稳定性优化的损失权重配置
    parser.add_argument('--focal_weight', default=0.25, type=float, help='🔧 稳定化：Focal损失权重 0.3→0.25')
    parser.add_argument('--iou_weight', default=0.65, type=float, help='🔧 稳定化：IoU损失权重 0.6→0.65（绝对主导）')
    parser.add_argument('--dt_weight', default=0.10, type=float, help='🔧 稳定化：DT损失权重 0.15→0.10（最小化）')
    parser.add_argument('--use_focal', default=True, type=bool, help='使用Focal损失')
    
    # 骨干网络参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    
    # 🔧 稳定性配置
    parser.add_argument('--ema_decay', default=0.9999, type=float, help='EMA衰减系数')
    parser.add_argument('--use_ema', default=True, type=bool, help='使用EMA稳定训练')
    parser.add_argument('--stable_lr', default=True, type=bool, help='使用稳定学习率调度')
    parser.add_argument('--validation_samples', default=5, type=int, help='验证时使用的样本数（减少随机性）')
    
    return parser.parse_args()

class StableLRScheduler:
    """稳定学习率调度器 - 避免余弦退火的陷阱"""
    def __init__(self, optimizer, warmup_epochs, max_epochs, base_lr, min_lr):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.base_lr = base_lr
        self.min_lr = min_lr
        
    def step(self, epoch):
        if epoch < self.warmup_epochs:
            # 线性预热
            lr = self.base_lr * (epoch + 1) / self.warmup_epochs
        else:
            # 🔧 使用更稳定的指数衰减而非余弦退火
            progress = (epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            # 指数衰减，避免突变
            lr = self.min_lr + (self.base_lr - self.min_lr) * (0.95 ** (progress * 50))
        
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr
        
        return lr

class EMA:
    """指数移动平均，稳定训练"""
    def __init__(self, model, decay=0.9999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        
    def register(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
                
    def update(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()
                
    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                self.backup[name] = param.data
                param.data = self.shadow[name]
                
    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.backup
                param.data = self.backup[name]
        self.backup = {}

class StableEngine:
    def __init__(self, args):
        # 设置随机种子
        set_seed(42)
        
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.min_lr = args.min_lr
        self.warmup_epochs = args.warmup_epochs
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.log_interval = args.log_interval
        self.model_path = args.base_save_path + '/RTGlassNet_Stable'
        self.use_gpu = args.use_gpu
        self.use_focal = args.use_focal
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        
        # 🎯 稳定性优化的损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        # 稳定性配置
        self.use_ema = args.use_ema
        self.ema_decay = args.ema_decay
        self.stable_lr = args.stable_lr
        self.validation_samples = args.validation_samples
        
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))
        self.device = torch.device('cuda' if torch.cuda.is_available() and self.use_gpu else 'cpu')
        
        # 初始化RTGlassNet双分支模型
        self.model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        self._load_backbone_weight()
        
        # 🔧 初始化EMA
        if self.use_ema:
            self.ema = EMA(self.model, decay=self.ema_decay)
            self.ema.register()
            print(f"✅ EMA已启用，衰减系数: {self.ema_decay}")
        
        # 🎯 稳定化损失函数
        self.criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=self.use_focal
        )
        
        # 🔧 稳定性优化的优化器配置
        self.optimizer = optim.AdamW(
            self.model.parameters(), 
            lr=self.lr, 
            weight_decay=self.wd,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 🔧 稳定学习率调度器
        if self.stable_lr:
            self.scheduler = StableLRScheduler(
                self.optimizer, 
                warmup_epochs=self.warmup_epochs,
                max_epochs=self.epochs,
                base_lr=self.lr,
                min_lr=self.min_lr
            )
        
        self.writer_train = SummaryWriter(log_dir='runs/rtglassnet_stable_train')
        self.writer_val = SummaryWriter(log_dir='runs/rtglassnet_stable_val')
        
        # 打印稳定性配置信息
        print(f"🎯 RTGlassNet稳定性优化训练配置:")
        print(f"   🔧 批次大小优化: {self.bs} (原6 → 优化{self.bs})")
        print(f"   🔧 学习率优化: {self.lr} (原0.0005 → 优化{self.lr})")
        print(f"   🔧 损失权重稳定化: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔧 IoU权重占比: {self.iou_weight/(self.focal_weight+self.iou_weight+self.dt_weight)*100:.1f}% - 绝对主导")
        print(f"   🔧 预热策略: {self.warmup_epochs}轮预热 → 稳定指数衰减")
        print(f"   🔧 EMA稳定: {'启用' if self.use_ema else '禁用'}")
        print(f"   🔧 权重衰减: {self.wd} (减小)")
        print(f"   🚀 骨干网络: {self.backbone_type}")
        
        # 打印模型参数统计
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"   📊 模型参数: {total_params/1e6:.1f}M总参数, {trainable_params/1e6:.1f}M可训练")

    def _load_backbone_weight(self):
        weight_path = self.backbone_weight
        if weight_path and os.path.exists(weight_path):
            print(f"加载InceptionNeXt权重: {weight_path}")
            state_dict = torch.load(weight_path, map_location=self.device)
            model_backbone = self.model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"已加载 {len(filtered_dict)} 个backbone参数.")
        else:
            print(f"未找到权重文件: {weight_path}，跳过加载。")

    def train(self):
        # 🔧 数据加载器配置 - 增强稳定性
        train_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='train', 
            target_size=(self.img_size, self.img_size), 
            split_ratio=0.85,  # 🔧 增加训练集：0.9→0.85
            random_seed=42, 
            glass_aug_config='light' if self.aug else None  # 🔧 减轻增强：moderate→light
        )
        val_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='valid', 
            target_size=(self.img_size, self.img_size), 
            split_ratio=0.85,  # 🔧 增加验证集：0.9→0.85
            random_seed=42, 
            glass_aug_config=None
        )
        
        # 🔧 稳定性数据加载器
        train_loader = DataLoader(
            train_data, 
            batch_size=self.bs, 
            shuffle=True, 
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True  # 🔧 丢弃最后一个不完整批次
        )
        val_loader = DataLoader(
            val_data, 
            batch_size=self.bs, 
            shuffle=False, 
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=False
        )
        
        best_iou = 0.0
        iou_history = []  # 🔧 IoU历史记录
        stability_threshold = 0.05  # 🔧 稳定性阈值
        
        print(f"\n🎯 开始稳定性优化训练 - 目标：消除IoU起伏")
        print(f"   训练样本: {len(train_data)}, 验证样本: {len(val_data)}")
        print(f"   批次数/轮: {len(train_loader)}")
        print(f"   🔧 稳定性优化: 批次大小{self.bs}, 轻量增强, EMA稳定")
        
        for epoch in range(1, self.epochs + 1):
            # 🔧 更新学习率
            if self.stable_lr:
                current_lr = self.scheduler.step(epoch - 1)
            else:
                current_lr = self.optimizer.param_groups[0]['lr']
            
            self.model.train()
            running_loss = 0.0
            train_tqdm = tqdm(train_loader, desc=f"Epoch {epoch}/{self.epochs} [LR:{current_lr:.6f}]")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                self.optimizer.zero_grad()
                outputs = self.model(inp_imgs)
                
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                # 🔧 数值稳定性检查
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失值，跳过此批次")
                    continue
                
                loss.backward()
                
                # 🔧 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
                
                self.optimizer.step()
                
                # 🔧 EMA更新
                if self.use_ema:
                    self.ema.update()
                
                running_loss += loss.item()
                
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}", 
                    avg_loss=f"{running_loss/(batch_idx+1):.4f}",
                    focal=f"{loss_dict['focal_loss']:.4f}",
                    iou=f"{loss_dict['iou_loss']:.4f}",
                    dt=f"{loss_dict['dt_loss']:.4f}",
                    lr=f"{current_lr:.6f}"
                )
                
                if batch_idx % self.log_interval == 0:
                    global_step = (epoch - 1) * len(train_loader) + batch_idx
                    self.writer_train.add_scalar('loss/total', loss.item(), global_step)
                    self.writer_train.add_scalar('loss/focal', loss_dict['focal_loss'], global_step)
                    self.writer_train.add_scalar('loss/iou', loss_dict['iou_loss'], global_step)
                    self.writer_train.add_scalar('loss/dt', loss_dict['dt_loss'], global_step)
                    self.writer_train.add_scalar('lr', current_lr, global_step)
                    
            epoch_loss = running_loss / len(train_loader)
            print(f"训练轮次: {epoch} 损失: {epoch_loss:.6f} 学习率: {current_lr:.6f}")
            
            # 验证
            if epoch % self.test_interval == 0:
                val_loss, val_iou = self.validate(val_loader, epoch)
                iou_history.append(val_iou)
                
                # 🔧 稳定性分析
                if len(iou_history) >= 3:
                    recent_ious = iou_history[-3:]
                    iou_std = np.std(recent_ious)
                    print(f"📊 最近3轮IoU标准差: {iou_std:.4f} (稳定阈值: {stability_threshold})")
                    
                    if iou_std < stability_threshold:
                        print("✅ 训练已稳定！")
                    else:
                        print("⚠️ 训练仍有波动")
                
                # 🏆 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    self.save_model(epoch, is_best=True, val_iou=val_iou)
                    
                    if val_iou > 0.8:  # 达到80%目标
                        print(f"🎉 达到目标IoU 80%+! 当前IoU: {val_iou:.4f}")
                
                # 记录验证指标
                self.writer_val.add_scalar('loss/total', val_loss, epoch)
                self.writer_val.add_scalar('metrics/iou', val_iou, epoch)
                self.writer_val.add_scalar('metrics/best_iou', best_iou, epoch)
                
                # 🔧 保存IoU历史
                if len(iou_history) >= 5:
                    iou_history = iou_history[-5:]  # 只保留最近5轮
                    
            if self.save_interval and epoch % self.save_interval == 0:
                self.save_model(epoch, is_best=False, val_iou=best_iou)
                
        self.writer_train.close()
        self.writer_val.close()
        
        print(f"\n🎯 稳定性优化训练完成！")
        print(f"   最佳IoU: {best_iou:.4f}")
        if len(iou_history) >= 3:
            final_std = np.std(iou_history[-3:])
            print(f"   最终稳定性: {final_std:.4f} (目标: <{stability_threshold})")

    def validate(self, val_loader, epoch):
        self.model.eval()
        
        # 🔧 使用EMA进行验证
        if self.use_ema:
            self.ema.apply_shadow()
            
        running_loss = 0.0
        iou_list = []
        dt_iou_list = []
        
        with torch.no_grad():
            val_tqdm = tqdm(val_loader, desc="验证")
            
            # 🔧 限制验证样本数量，减少随机性
            total_batches = min(len(val_loader), 30)  # 最多30个批次
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(val_tqdm):
                if batch_idx >= total_batches:
                    break
                    
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                outputs = self.model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                running_loss += loss.item()
                
                # 主预测IoU计算
                main_pred = outputs['main_pred']
                pred_bin = (main_pred > 0.5).float()
                intersection = (pred_bin * gt_masks).sum(dim=(1,2,3))
                union = pred_bin.sum(dim=(1,2,3)) + gt_masks.sum(dim=(1,2,3)) - intersection
                iou = (intersection / (union + 1e-7)).mean().item()
                iou_list.append(iou)
                
                # DT预测质量评估
                dt_pred = outputs['dt_pred']
                dt_mae = F.l1_loss(dt_pred, dt_maps).item()
                dt_iou_list.append(1.0 - dt_mae)
                
        val_loss = running_loss / total_batches
        mean_iou = sum(iou_list) / len(iou_list)
        mean_dt_quality = sum(dt_iou_list) / len(dt_iou_list)
        
        # 🔧 恢复EMA
        if self.use_ema:
            self.ema.restore()
        
        print(f'🎯 验证结果 :: IoU: {mean_iou:.4f} | DT质量: {mean_dt_quality:.4f} | 损失: {val_loss:.4f}')
        
        return val_loss, mean_iou

    def save_model(self, epoch, is_best=False, val_iou=0.0):
        """保存模型 - 支持EMA"""
        
        # 如果使用EMA，保存EMA权重
        if self.use_ema:
            self.ema.apply_shadow()
            
        state = {
            'model': self.model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'epoch': epoch,
            'val_iou': val_iou,
            'backbone_type': self.backbone_type,
            'ema_shadow': self.ema.shadow if self.use_ema else None,
            'model_info': {
                'backbone': self.backbone_type,
                'architecture': 'RTGlassNet_Stable',
                'features': ['InceptionNeXt', 'FPN', 'LightEdgeEnhancer', 'SCSA', 'DT_Prediction', 'EMA']
            }
        }
        
        if is_best:
            save_path = os.path.join(self.model_path, 'weights', f'🎯STABLE_BEST_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"🏆 🎯稳定性优化最佳模型🎯 已保存: epoch {epoch}, IoU {val_iou:.4f}")
            print(f"   优化特征: 大批次+轻增强+EMA稳定+指数衰减LR")
        else:
            save_path = os.path.join(self.model_path, 'weights', f'stable_checkpoint_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"📦 稳定性检查点已保存: epoch {epoch}, IoU {val_iou:.4f}")
            
        # 恢复EMA
        if self.use_ema:
            self.ema.restore()

def main():
    args = parse_arguments()
    engine = StableEngine(args)
    engine.train()

if __name__ == '__main__':
    main() 