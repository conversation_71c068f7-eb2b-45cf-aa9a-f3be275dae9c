#!/usr/bin/env python3
"""
🌟 RTGlassNet王牌组合测试脚本
验证 LightEdgeEnhancer + Distance Transform Loss 的协同工作
"""

import torch
import torch.nn.functional as F
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss

def test_rtglassnet_combo():
    """测试RTGlassNet王牌组合"""
    print("🌟 测试RTGlassNet王牌组合...")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"   设备: {device}")
    
    # 1. 初始化模型
    model = RTGlassNet(backbone_type='inceptionnext_base').to(device)
    print(f"   ✅ 模型初始化成功")
    
    # 2. 初始化损失函数
    criterion = CompositeLoss(
        focal_weight=0.4,
        iou_weight=0.4, 
        dt_weight=0.2,
        use_focal=True
    )
    print(f"   ✅ 王牌损失函数初始化成功")
    
    # 3. 创建测试数据
    batch_size = 2
    img_size = 416
    
    # 输入图像 [B, 3, H, W]
    images = torch.randn(batch_size, 3, img_size, img_size).to(device)
    
    # 真值掩码 [B, 1, H, W] 
    gt_masks = torch.randint(0, 2, (batch_size, 1, img_size, img_size)).float().to(device)
    
    # 距离变换图 [B, 1, H, W]
    dt_maps = torch.rand(batch_size, 1, img_size, img_size).to(device)
    
    print(f"   ✅ 测试数据创建成功")
    print(f"      - 图像: {images.shape}")
    print(f"      - 掩码: {gt_masks.shape} 范围[{gt_masks.min():.3f}, {gt_masks.max():.3f}]")
    print(f"      - DT图: {dt_maps.shape} 范围[{dt_maps.min():.3f}, {dt_maps.max():.3f}]")
    
    # 4. 前向传播测试
    model.eval()
    with torch.no_grad():
        outputs = model(images)
        
        # 检查输出格式
        expected_keys = ['main_pred', 'dt_pred', 'main_logits', 'dt_logits', 'refined_pred']
        for key in expected_keys:
            if key not in outputs:
                print(f"   ❌ 缺少输出键: {key}")
                return False
        
        print(f"   ✅ 模型前向传播成功")
        for key, value in outputs.items():
            print(f"      - {key}: {value.shape} 范围[{value.min():.3f}, {value.max():.3f}]")
    
    # 5. 损失计算测试
    model.train()
    
    # 前向传播
    outputs = model(images)
    
    # 计算损失
    loss, loss_dict = criterion(outputs, gt_masks, dt_maps)
    
    print(f"   ✅ 损失计算成功")
    print(f"      - 总损失: {loss.item():.6f}")
    for key, value in loss_dict.items():
        print(f"      - {key}: {value:.6f}")
    
    # 6. 反向传播测试
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    print(f"   ✅ 梯度反传成功")
    
    # 7. 检查梯度流
    has_grad = False
    for name, param in model.named_parameters():
        if param.grad is not None:
            has_grad = True
            if 'edge_enhancer' in name:
                print(f"      - LightEdgeEnhancer梯度: {param.grad.abs().mean().item():.6f}")
            elif 'dt_branch' in name:
                print(f"      - DT分支梯度: {param.grad.abs().mean().item():.6f}")
    
    if has_grad:
        print(f"   ✅ 王牌组合梯度流正常")
    else:
        print(f"   ❌ 梯度流异常")
        return False
    
    print(f"\n🎉 RTGlassNet王牌组合测试通过!")
    print(f"   🌟 LightEdgeEnhancer + Distance Transform Loss 协同工作正常")
    return True

if __name__ == '__main__':
    success = test_rtglassnet_combo()
    if success:
        print(f"\n✅ 可以开始训练王牌组合模型了!")
        print(f"💡 建议训练命令:")
        print(f"   python -m GlassDiffusion.train_rtglassnet \\")
        print(f"     --backbone_type inceptionnext_base \\")
        print(f"     --backbone_weight ./inceptionnext/inceptionnext_base.pth \\")
        print(f"     --epochs 100 --bs 4 --img_size 416 \\")
        print(f"     --focal_weight 0.4 --iou_weight 0.4 --dt_weight 0.2")
    else:
        print(f"\n❌ 测试失败，请检查实现!") 