"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : diff_crf.py
 @Function: Differentiable CRF implementation for end-to-end training

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

class DenseCRF(nn.Module):
    """
    Dense CRF implementation for end-to-end training
    Based on the mean-field approximation of CRF inference
    Optimized for memory efficiency with the parameters from grid search [1.5, 40, 3, 5, 10]
    """
    def __init__(self, iter_max=10, pos_w=5.0, bi_w=10.0, pos_xy_std=1.5,
                 bi_xy_std=40.0, bi_rgb_std=3.0, trainable=True):
        """
        Initialize the Dense CRF layer

        Args:
            iter_max: Number of mean-field iterations
            pos_w: Weight for the position-dependent kernel (gaussian_weight)
            bi_w: Weight for the bilateral kernel (bilateral_weight)
            pos_xy_std: Standard deviation for the position-dependent kernel (gaussian_sigma)
            bi_xy_std: Standard deviation for the bilateral kernel spatial component (bilateral_spatial_sigma)
            bi_rgb_std: Standard deviation for the bilateral kernel color component (bilateral_color_sigma)
            trainable: Whether to make the parameters trainable
        """
        super(DenseCRF, self).__init__()

        self.iter_max = iter_max

        # Initialize parameters
        if trainable:
            self.pos_w = nn.Parameter(torch.tensor(pos_w))
            self.bi_w = nn.Parameter(torch.tensor(bi_w))
            self.pos_xy_std = nn.Parameter(torch.tensor(pos_xy_std))
            self.bi_xy_std = nn.Parameter(torch.tensor(bi_xy_std))
            self.bi_rgb_std = nn.Parameter(torch.tensor(bi_rgb_std))
        else:
            self.register_buffer('pos_w', torch.tensor(pos_w))
            self.register_buffer('bi_w', torch.tensor(bi_w))
            self.register_buffer('pos_xy_std', torch.tensor(pos_xy_std))
            self.register_buffer('bi_xy_std', torch.tensor(bi_xy_std))
            self.register_buffer('bi_rgb_std', torch.tensor(bi_rgb_std))

    def _gaussian_filter(self, x, sigma):
        """
        Apply Gaussian filter

        Args:
            x: Input tensor (B, C, H, W)
            sigma: Standard deviation of the Gaussian filter

        Returns:
            filtered: Filtered tensor
        """
        # Determine kernel size based on sigma (2*sigma + 1 is a good rule of thumb)
        kernel_size = int(2 * sigma.item() + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1  # Ensure odd kernel size

        # Create Gaussian kernel
        padding = kernel_size // 2

        # Create a 1D Gaussian kernel
        grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
        gaussian_1d = torch.exp(-0.5 * (grid / sigma).pow(2))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()

        # Create 2D Gaussian kernel
        gaussian_2d = gaussian_1d.view(1, 1, -1, 1) * gaussian_1d.view(1, 1, 1, -1)
        gaussian_2d = gaussian_2d.expand(x.size(1), 1, kernel_size, kernel_size)

        # Apply Gaussian filter
        x_padded = F.pad(x, (padding, padding, padding, padding), mode='reflect')
        return F.conv2d(x_padded, gaussian_2d.to(x.device), groups=x.size(1))

    def _bilateral_filter(self, x, guide, spatial_sigma, color_sigma):
        """
        Apply bilateral filter approximation

        Args:
            x: Input tensor (B, C, H, W)
            guide: Guidance image (B, 3, H, W)
            spatial_sigma: Spatial sigma for the bilateral filter
            color_sigma: Color sigma for the bilateral filter

        Returns:
            filtered: Filtered tensor
        """
        # Apply Gaussian blur to the input
        blurred = self._gaussian_filter(x, spatial_sigma)

        # Compute color-based weights
        guide_mean = guide.mean(dim=(2, 3), keepdim=True)
        color_diff = guide - guide_mean
        color_weight = torch.exp(-color_diff.pow(2).sum(dim=1, keepdim=True) / (2 * color_sigma ** 2))

        # Apply color weights
        return blurred * color_weight

    def forward(self, img, prob):
        """
        Forward pass of the Dense CRF layer

        Args:
            img: Input image (B, 3, H, W) in range [0, 1]
            prob: Initial probability map (B, 1, H, W) in range [0, 1]

        Returns:
            refined: Refined probability map after CRF (B, 1, H, W)
        """
        # Convert single-channel probability to two-channel logits
        # [prob, 1-prob] -> log([prob, 1-prob])
        prob_fg = prob
        prob_bg = 1.0 - prob

        # Small epsilon to avoid log(0)
        eps = 1e-6
        prob_fg = torch.clamp(prob_fg, eps, 1.0 - eps)
        prob_bg = torch.clamp(prob_bg, eps, 1.0 - eps)

        # Stack foreground and background probabilities
        Q = torch.cat([prob_bg, prob_fg], dim=1)

        # Convert to logits
        logits = torch.log(Q)

        # Mean-field iterations
        for _ in range(self.iter_max):
            # Apply Gaussian filter (appearance kernel)
            gaussian_term = self._gaussian_filter(Q, self.pos_xy_std)

            # Apply bilateral filter (smoothness kernel)
            bilateral_term = self._bilateral_filter(Q, img, self.bi_xy_std, self.bi_rgb_std)

            # Combine filtered results
            message = self.pos_w * gaussian_term + self.bi_w * bilateral_term

            # Update Q with the compatibility transform (implicit in the message passing)
            Q = F.softmax(logits + message, dim=1)

        # Return the probability of foreground
        return Q[:, 1:2, :, :]

class DiffCRF(nn.Module):
    """
    Differentiable CRF layer for end-to-end training
    Based on the mean-field approximation of CRF inference
    """
    def __init__(self, n_iter=5, pos_weight=1.0, pos_xy_std=1.0, bi_xy_std=67.0, bi_rgb_std=3.0,
                 compatibility_matrix=None, trainable=True):
        """
        Initialize the differentiable CRF layer

        Args:
            n_iter: Number of mean-field iterations
            pos_weight: Weight for the position-dependent kernel
            pos_xy_std: Standard deviation for the position-dependent kernel
            bi_xy_std: Standard deviation for the bilateral kernel (spatial)
            bi_rgb_std: Standard deviation for the bilateral kernel (color)
            compatibility_matrix: Compatibility matrix for the CRF
            trainable: Whether to make the parameters trainable
        """
        super(DiffCRF, self).__init__()

        self.n_iter = n_iter

        # Initialize parameters
        if trainable:
            self.pos_weight = nn.Parameter(torch.tensor(pos_weight))
            self.pos_xy_std = nn.Parameter(torch.tensor(pos_xy_std))
            self.bi_xy_std = nn.Parameter(torch.tensor(bi_xy_std))
            self.bi_rgb_std = nn.Parameter(torch.tensor(bi_rgb_std))
        else:
            self.register_buffer('pos_weight', torch.tensor(pos_weight))
            self.register_buffer('pos_xy_std', torch.tensor(pos_xy_std))
            self.register_buffer('bi_xy_std', torch.tensor(bi_xy_std))
            self.register_buffer('bi_rgb_std', torch.tensor(bi_rgb_std))

        # Initialize compatibility matrix (for binary segmentation, this is usually [[-1, 1], [1, -1]])
        if compatibility_matrix is None:
            compatibility_matrix = torch.tensor([[-1.0, 1.0], [1.0, -1.0]])

        if trainable:
            self.compatibility_matrix = nn.Parameter(compatibility_matrix)
        else:
            self.register_buffer('compatibility_matrix', compatibility_matrix)

    def _initialize_Q(self, unary):
        """
        Initialize Q (softmax of negative unary potentials)

        Args:
            unary: Unary potentials (B, C, H, W)

        Returns:
            Q: Initial Q distribution (B, C, H, W)
        """
        return F.softmax(-unary, dim=1)

    def _compute_spatial_kernel(self, h, w):
        """
        Compute the spatial kernel

        Args:
            h: Height of the image
            w: Width of the image

        Returns:
            spatial_kernel: Spatial kernel
        """
        # Create coordinate grid
        x_grid = torch.arange(w).float().cuda()
        y_grid = torch.arange(h).float().cuda()

        x_grid = x_grid.view(1, 1, 1, w).expand(1, 1, h, w)
        y_grid = y_grid.view(1, 1, h, 1).expand(1, 1, h, w)

        # Compute pairwise spatial potentials
        x_diff = x_grid - x_grid.permute(0, 1, 3, 2).contiguous()
        y_diff = y_grid - y_grid.permute(0, 1, 2, 3).contiguous()

        spatial_sq_diff = x_diff ** 2 + y_diff ** 2
        spatial_kernel = torch.exp(-spatial_sq_diff / (2 * self.pos_xy_std ** 2))

        return spatial_kernel

    def _compute_bilateral_kernel(self, img, h, w):
        """
        Compute the bilateral kernel

        Args:
            img: Input image (B, 3, H, W)
            h: Height of the image
            w: Width of the image

        Returns:
            bilateral_kernel: Bilateral kernel
        """
        # Create coordinate grid
        x_grid = torch.arange(w).float().cuda()
        y_grid = torch.arange(h).float().cuda()

        x_grid = x_grid.view(1, 1, 1, w).expand(1, 1, h, w)
        y_grid = y_grid.view(1, 1, h, 1).expand(1, 1, h, w)

        # Compute pairwise spatial potentials
        x_diff = x_grid - x_grid.permute(0, 1, 3, 2).contiguous()
        y_diff = y_grid - y_grid.permute(0, 1, 2, 3).contiguous()

        spatial_sq_diff = x_diff ** 2 + y_diff ** 2
        spatial_exp = torch.exp(-spatial_sq_diff / (2 * self.bi_xy_std ** 2))

        # Compute pairwise color potentials
        img_flat = img.view(img.size(0), 3, -1)
        img_flat_t = img_flat.permute(0, 2, 1).contiguous()

        color_sq_diff = torch.sum((img_flat.unsqueeze(2) - img_flat_t.unsqueeze(1)) ** 2, dim=3)
        color_sq_diff = color_sq_diff.view(img.size(0), h * w, h, w)
        color_exp = torch.exp(-color_sq_diff / (2 * self.bi_rgb_std ** 2))

        # Combine spatial and color potentials
        bilateral_kernel = spatial_exp * color_exp

        return bilateral_kernel

    def _message_passing(self, Q, spatial_kernel, bilateral_kernel):
        """
        Perform message passing

        Args:
            Q: Current Q distribution (B, C, H, W)
            spatial_kernel: Spatial kernel
            bilateral_kernel: Bilateral kernel

        Returns:
            message: Message after passing
        """
        h, w = Q.size(2), Q.size(3)
        Q_flat = Q.view(Q.size(0), Q.size(1), -1)

        # Message passing with spatial kernel
        spatial_message = torch.matmul(Q_flat, spatial_kernel.view(1, h * w, h * w))
        spatial_message = spatial_message.view(Q.size(0), Q.size(1), h, w)

        # Message passing with bilateral kernel
        bilateral_message = torch.matmul(Q_flat, bilateral_kernel.view(Q.size(0), h * w, h * w))
        bilateral_message = bilateral_message.view(Q.size(0), Q.size(1), h, w)

        # Combine messages
        message = self.pos_weight * spatial_message + bilateral_message

        return message

    def _compatibility_transform(self, message):
        """
        Apply compatibility transform

        Args:
            message: Message after passing

        Returns:
            transformed_message: Message after compatibility transform
        """
        c = message.size(1)
        message_flat = message.view(message.size(0), c, -1)

        transformed_message = torch.matmul(self.compatibility_matrix.unsqueeze(0), message_flat)
        transformed_message = transformed_message.view(message.size(0), c, message.size(2), message.size(3))

        return transformed_message

    def forward(self, unary, img):
        """
        Forward pass of the CRF layer

        Args:
            unary: Unary potentials (B, C, H, W)
            img: Input image (B, 3, H, W)

        Returns:
            refined: Refined segmentation after CRF (B, C, H, W)
        """
        # Initialize Q
        Q = self._initialize_Q(unary)

        h, w = unary.size(2), unary.size(3)

        # Compute kernels
        spatial_kernel = self._compute_spatial_kernel(h, w)
        bilateral_kernel = self._compute_bilateral_kernel(img, h, w)

        # Mean-field iterations
        for _ in range(self.n_iter):
            # Message passing
            message = self._message_passing(Q, spatial_kernel, bilateral_kernel)

            # Compatibility transform
            transformed_message = self._compatibility_transform(message)

            # Update Q
            Q = F.softmax(-unary - transformed_message, dim=1)

        # Return the refined segmentation (probability of foreground)
        return Q[:, 1:2, :, :]

class SimplifiedDiffCRF(nn.Module):
    """
    Simplified differentiable CRF layer for end-to-end training
    Uses Gaussian filters to approximate the pairwise potentials
    """
    def __init__(self, n_iter=5, bilateral_weight=5.0, gaussian_weight=3.0,
                 bilateral_spatial_sigma=49.0, bilateral_color_sigma=5.0,
                 gaussian_sigma=3.0, trainable=True):
        """
        Initialize the simplified differentiable CRF layer

        Args:
            n_iter: Number of mean-field iterations
            bilateral_weight: Weight for the bilateral filter
            gaussian_weight: Weight for the Gaussian filter
            bilateral_spatial_sigma: Spatial sigma for the bilateral filter
            bilateral_color_sigma: Color sigma for the bilateral filter
            gaussian_sigma: Sigma for the Gaussian filter
            trainable: Whether to make the parameters trainable
        """
        super(SimplifiedDiffCRF, self).__init__()

        self.n_iter = n_iter

        # Initialize parameters
        if trainable:
            self.bilateral_weight = nn.Parameter(torch.tensor(bilateral_weight))
            self.gaussian_weight = nn.Parameter(torch.tensor(gaussian_weight))
            self.bilateral_spatial_sigma = nn.Parameter(torch.tensor(bilateral_spatial_sigma))
            self.bilateral_color_sigma = nn.Parameter(torch.tensor(bilateral_color_sigma))
            self.gaussian_sigma = nn.Parameter(torch.tensor(gaussian_sigma))
        else:
            self.register_buffer('bilateral_weight', torch.tensor(bilateral_weight))
            self.register_buffer('gaussian_weight', torch.tensor(gaussian_weight))
            self.register_buffer('bilateral_spatial_sigma', torch.tensor(bilateral_spatial_sigma))
            self.register_buffer('bilateral_color_sigma', torch.tensor(bilateral_color_sigma))
            self.register_buffer('gaussian_sigma', torch.tensor(gaussian_sigma))

    def _gaussian_filter(self, x, sigma):
        """
        Apply Gaussian filter

        Args:
            x: Input tensor (B, C, H, W)
            sigma: Standard deviation of the Gaussian filter

        Returns:
            filtered: Filtered tensor
        """
        # 数值稳定性检查
        if torch.isnan(sigma) or torch.isinf(sigma):
            sigma = torch.tensor(3.0, device=sigma.device)
        sigma = torch.clamp(sigma, min=0.5, max=10.0)  # 限制sigma范围
        
        # Determine kernel size based on sigma (2*sigma + 1 is a good rule of thumb)
        kernel_size = int(2 * sigma.item() + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1  # Ensure odd kernel size
        
        # 确保kernel_size在合理范围内
        kernel_size = max(3, min(kernel_size, 15))

        # Create Gaussian kernel
        padding = kernel_size // 2

        # Create a 1D Gaussian kernel
        grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
        gaussian_1d = torch.exp(-0.5 * (grid / sigma).pow(2))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()

        # Create 2D Gaussian kernel
        gaussian_2d = gaussian_1d.view(1, 1, -1, 1) * gaussian_1d.view(1, 1, 1, -1)
        gaussian_2d = gaussian_2d.expand(x.size(1), 1, kernel_size, kernel_size)

        # Apply Gaussian filter
        x_padded = F.pad(x, (padding, padding, padding, padding), mode='reflect')
        return F.conv2d(x_padded, gaussian_2d.to(x.device), groups=x.size(1))

    def _bilateral_filter(self, x, guide, spatial_sigma, color_sigma):
        """
        Apply bilateral filter approximation

        Args:
            x: Input tensor (B, C, H, W)
            guide: Guidance image (B, 3, H, W)
            spatial_sigma: Spatial sigma for the bilateral filter
            color_sigma: Color sigma for the bilateral filter

        Returns:
            filtered: Filtered tensor
        """
        # 数值稳定性检查
        if torch.isnan(spatial_sigma) or torch.isinf(spatial_sigma):
            spatial_sigma = torch.tensor(49.0, device=spatial_sigma.device)
        if torch.isnan(color_sigma) or torch.isinf(color_sigma):
            color_sigma = torch.tensor(5.0, device=color_sigma.device)
        
        spatial_sigma = torch.clamp(spatial_sigma, min=1.0, max=100.0)
        color_sigma = torch.clamp(color_sigma, min=0.1, max=20.0)
        
        # This is a simplified approximation of bilateral filtering
        # For a true bilateral filter, we would need a custom CUDA implementation

        # Apply Gaussian blur to the input
        blurred = self._gaussian_filter(x, spatial_sigma)

        # Compute color-based weights
        color_weight = torch.exp(-(guide - guide.mean(dim=(2, 3), keepdim=True)).pow(2).sum(dim=1, keepdim=True) / (2 * color_sigma ** 2))

        # Apply color weights
        return blurred * color_weight

    def forward(self, logits, img):
        """
        Forward pass of the simplified CRF layer

        Args:
            logits: Logits from the network (B, C, H, W)
            img: Input image (B, 3, H, W)

        Returns:
            refined: Refined segmentation after CRF (B, C, H, W)
        """
        # 数值稳定性检查
        logits = torch.clamp(logits, min=-10.0, max=10.0)
        
        # 检查并修复参数中的NaN/Inf
        if torch.isnan(self.gaussian_sigma) or torch.isinf(self.gaussian_sigma):
            self.gaussian_sigma.data = torch.tensor(3.0, device=self.gaussian_sigma.device)
        if torch.isnan(self.bilateral_spatial_sigma) or torch.isinf(self.bilateral_spatial_sigma):
            self.bilateral_spatial_sigma.data = torch.tensor(49.0, device=self.bilateral_spatial_sigma.device)
        if torch.isnan(self.bilateral_color_sigma) or torch.isinf(self.bilateral_color_sigma):
            self.bilateral_color_sigma.data = torch.tensor(5.0, device=self.bilateral_color_sigma.device)
        if torch.isnan(self.gaussian_weight) or torch.isinf(self.gaussian_weight):
            self.gaussian_weight.data = torch.tensor(3.0, device=self.gaussian_weight.device)
        if torch.isnan(self.bilateral_weight) or torch.isinf(self.bilateral_weight):
            self.bilateral_weight.data = torch.tensor(5.0, device=self.bilateral_weight.device)
        
        # Initialize with softmax of logits
        Q = F.softmax(logits, dim=1)

        # Mean-field iterations
        for _ in range(self.n_iter):
            # Apply Gaussian filter (appearance kernel)
            gaussian_term = self._gaussian_filter(Q, self.gaussian_sigma)

            # Apply bilateral filter (smoothness kernel)
            bilateral_term = self._bilateral_filter(Q, img, self.bilateral_spatial_sigma, self.bilateral_color_sigma)

            # Combine filtered results
            message = self.gaussian_weight * gaussian_term + self.bilateral_weight * bilateral_term

            # Update Q
            Q = F.softmax(logits + message, dim=1)

        # For binary segmentation, return the probability of foreground and background
        if Q.size(1) == 2:
            return Q  # Return both channels (bg, fg)
        else:
            # If not 2 channels, create a 2-channel output (bg, fg)
            # Use the first channel as foreground if available, otherwise use a default value
            if Q.size(1) >= 1:
                fg_prob = Q[:, 0:1, :, :]
                bg_prob = 1.0 - fg_prob
            else:
                # Create default probabilities
                fg_prob = torch.ones_like(logits[:, 0:1, :, :]) * 0.5
                bg_prob = torch.ones_like(logits[:, 0:1, :, :]) * 0.5

            # Ensure probabilities sum to 1
            total = fg_prob + bg_prob
            fg_prob = fg_prob / total
            bg_prob = bg_prob / total

            return torch.cat([bg_prob, fg_prob], dim=1)  # Return (bg, fg)
