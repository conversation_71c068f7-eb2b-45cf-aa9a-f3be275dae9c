"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : gdnet_hybrid_scsa.py
 @Function: Hybrid SCSA model - 回退到87.95% IoU成功架构

🔥 重大修改：回退到87.95% IoU的成功配置
主要策略：
1. 回退到原始SCSA (896维特征融合)
2. 回退到LightLCFI (512+256+128=896维输出)
3. 保留轻量化物理特性增强 (深度学习边缘检测)
4. 移除复杂的EnhancedSCSA和OptimizedLCFI

架构对比：
- 失败版本：EnhancedSCSA + OptimizedLCFI → 58% IoU ❌
- 成功版本：原始SCSA + LightLCFI → 87.95% IoU ✅
- 当前版本：原始SCSA + LightLCFI + 轻量物理增强 → 目标90% IoU

目标：在87.95% IoU基础上，通过轻量化物理增强突破90% IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import SCSA


class LightweightPhysicsEnhancer(nn.Module):
    """
    轻量化物理特性增强器 - 深度学习边缘检测版本
    只保留最有效的物理特性，避免复杂度过高
    主要改进：用深度学习边缘检测器替代传统Sobel算子
    """
    def __init__(self, in_channels):
        super(LightweightPhysicsEnhancer, self).__init__()
        
        # 🔸 深度学习边缘检测器 - 替代传统Sobel算子
        self.deep_edge_detector = nn.Sequential(
            # 多尺度边缘特征提取
            nn.Conv2d(in_channels, in_channels // 2, 3, 1, 1),
            nn.BatchNorm2d(in_channels // 2),
            nn.ReLU(inplace=True),
            
            # 分组卷积专注边缘模式 (参考EBLNet)
            nn.Conv2d(in_channels // 2, in_channels // 4, 3, 1, 1, groups=8),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            
            # 边缘增强分支
            nn.Conv2d(in_channels // 4, 32, 3, 1, 1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, 1, 1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1),
            nn.Sigmoid()
        )
        
        # 透明度感知模块 - 保持不变
        self.transparency_detector = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 1),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, 1, 1),
            nn.Sigmoid()
        )
        
        # 特征融合 - 保持不变
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(in_channels + 2, in_channels, 3, 1, 1),  # +2 for edge and transparency
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x, rgb_input=None):
        """
        x: 特征图
        rgb_input: 原始RGB输入（现在不再需要，但保持接口兼容性）
        """
        # 🔸 深度学习边缘检测 - 直接从特征图中学习边缘
        edge_weight = self.deep_edge_detector(x)
        
        # 透明度检测 - 保持不变
        transparency_weight = self.transparency_detector(x)
        
        # 特征融合 - 保持不变
        enhanced_features = torch.cat([x, edge_weight, transparency_weight], dim=1)
        output = self.feature_fusion(enhanced_features)
        
        return output, edge_weight, transparency_weight


class LightLCFI(nn.Module):
    """Lightweight Large Context Feature Integration module with depthwise separable convolutions"""
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(LightLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = int(input_channels / 4)
        self.channels_double = int(input_channels / 2)
        self.dr1 = dr1
        self.dr2 = dr2
        self.dr3 = dr3
        self.dr4 = dr4
        self.padding1 = 1 * dr1
        self.padding2 = 2 * dr2
        self.padding3 = 3 * dr3
        self.padding4 = 4 * dr4

        # Channel reduction with 1x1 convolutions
        self.p1_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p1
        self.p1_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (3, 1), 1, padding=(self.padding1, 0),
                      dilation=(self.dr1, 1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (1, 3), 1, padding=(0, self.padding1),
                      dilation=(1, self.dr1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p2
        self.p2_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (5, 1), 1, padding=(self.padding2, 0),
                      dilation=(self.dr2, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 5), 1, padding=(0, self.padding2),
                      dilation=(1, self.dr2), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p3
        self.p3_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (7, 1), 1, padding=(self.padding3, 0),
                      dilation=(self.dr3, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 7), 1, padding=(0, self.padding3),
                      dilation=(1, self.dr3), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p4
        self.p4_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (9, 1), 1, padding=(self.padding4, 0),
                      dilation=(self.dr4, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 9), 1, padding=(0, self.padding4),
                      dilation=(1, self.dr4), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Use SCSA for attention instead of CBAM
        self.scsa = SCSA(
            dim=self.input_channels,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )

        # Final channel reduction
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU())

    def forward(self, x):
        p1_input = self.p1_channel_reduction(x)
        p1 = self.p1_fusion(torch.cat((self.p1_d1(p1_input), self.p1_d2(p1_input)), 1))

        p2_input = torch.cat((self.p2_channel_reduction(x), p1), 1)
        p2 = self.p2_fusion(torch.cat((self.p2_d1(p2_input), self.p2_d2(p2_input)), 1))

        p3_input = torch.cat((self.p3_channel_reduction(x), p2), 1)
        p3 = self.p3_fusion(torch.cat((self.p3_d1(p3_input), self.p3_d2(p3_input)), 1))

        p4_input = torch.cat((self.p4_channel_reduction(x), p3), 1)
        p4 = self.p4_fusion(torch.cat((self.p4_d1(p4_input), self.p4_d2(p4_input)), 1))

        # 使用SCSA处理特征
        combined_features = torch.cat((p1, p2, p3, p4), 1)
        attended_features = self.scsa(combined_features)
        channel_reduction = self.channel_reduction(attended_features)

        return channel_reduction


class GDNetHybridSCSA(nn.Module):
    """
    混合SCSA模型 - 回退到87.95% IoU成功架构
    
    🔥 架构回退说明：
    - SCSA: EnhancedSCSA → 原始SCSA (896维)
    - LCFI: OptimizedLCFI → LightLCFI (512+256+128=896维)
    - 物理增强: 保留深度学习边缘检测器
    
    目标：87.95% IoU + 轻量物理增强 → 90% IoU
    """
    def __init__(self, backbone_path=None, crf_iter=5, trainable_crf=True):
        super(GDNetHybridSCSA, self).__init__()
        
        # 骨干网络
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0
        self.layer1 = resnext.layer1
        self.layer2 = resnext.layer2
        self.layer3 = resnext.layer3
        self.layer4 = resnext.layer4

        # 轻量化LCFI模块 - 回归87.95% IoU成功架构
        self.h5_conv = LightLCFI(2048, 1, 2, 3, 4)
        self.h4_conv = LightLCFI(1024, 1, 2, 3, 4)
        self.h3_conv = LightLCFI(512, 1, 2, 3, 4)
        self.l2_conv = LightLCFI(256, 1, 2, 3, 4)

        # 物理特性增强器 - 保持轻量化
        self.physics_enhancer_h = LightweightPhysicsEnhancer(512)   # 调整到LightLCFI输出维度
        self.physics_enhancer_l = LightweightPhysicsEnhancer(64)    # 调整到LightLCFI输出维度

        # 多尺度融合 - 回归87.95% IoU成功架构
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 🔥 关键修改：回归到87.95% IoU的成功融合维度
        # LightLCFI输出：512 + 256 + 128 = 896维
        fusion_dim = 896
        
        # 🔥 回退到原始SCSA - 87.95% IoU的成功配置
        self.h_fusion = SCSA(
            dim=fusion_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        # 特征融合网络 - 回归87.95% IoU成功架构
        self.h_fusion_conv = nn.Sequential(
            # 使用深度可分离卷积，与原始SCSA保持一致
            nn.Conv2d(896, 896, 3, 1, 1, groups=896),
            nn.Conv2d(896, 896, 1, 1, 0),
            nn.BatchNorm2d(896), 
            nn.ReLU()
        )

        # 低层特征处理 - 回归87.95% IoU成功架构
        self.l_fusion_conv = nn.Sequential(
            # 使用深度可分离卷积，与原始SCSA保持一致
            nn.Conv2d(64, 64, 3, 1, 1, groups=64),
            nn.Conv2d(64, 64, 1, 1, 0),
            nn.BatchNorm2d(64), 
            nn.ReLU()
        )

        # 预测头 - 保持与87.95% IoU架构一致
        self.predict_h = nn.Conv2d(896, 1, 3, 1, 1)  # 896维输入
        self.predict_l = nn.Conv2d(64, 1, 3, 1, 1)

        # 优化的CRF
        # self.crf = SimplifiedDiffCRF(
        #     n_iter=crf_iter,
        #     bilateral_weight=12.0,
        #     gaussian_weight=6.0,
        #     bilateral_spatial_sigma=45.0,
        #     bilateral_color_sigma=3.5,
        #     gaussian_sigma=1.8,
        #     trainable=trainable_crf
        # )

        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,  # From compat=10
            gaussian_weight=5.0,     # From compat=5
            bilateral_spatial_sigma=40.0,  # From sxy=40
            bilateral_color_sigma=3.0,     # From srgb=3
            gaussian_sigma=1.5,            # From sxy=1.5
            trainable=trainable_crf
        )

        # 自适应权重
        self.adaptive_weights = nn.Parameter(torch.tensor([0.75, 0.25]), requires_grad=True)
        
        # 最终增强模块
        self.final_enhancer = nn.Sequential(
            nn.Conv2d(1, 8, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 3, 1, 1),
            nn.Sigmoid()
        )

        self._init_weights()

    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # 提取多层特征
        layer0 = self.layer0(x)
        layer1 = self.layer1(layer0)
        layer2 = self.layer2(layer1)
        layer3 = self.layer3(layer2)
        layer4 = self.layer4(layer3)

        # LCFI特征提取 - 回归87.95% IoU成功架构
        h5_conv = self.h5_conv(layer4)  # 512维
        h4_conv = self.h4_conv(layer3)  # 256维  
        h3_conv = self.h3_conv(layer2)  # 128维
        l2_conv = self.l2_conv(layer1)  # 64维

        # 物理特性增强 - 调整到正确维度
        h5_enhanced, h_edge_weight, h_transparency_weight = self.physics_enhancer_h(h5_conv, x)
        l2_enhanced, l_edge_weight, l_transparency_weight = self.physics_enhancer_l(l2_conv, x)

        # 🔥 高层特征融合 - 回归87.95% IoU成功配置
        h5_up = self.h5_up(h5_enhanced)  # 上采样到h4尺寸
        h3_down = self.h3_down(h3_conv)  # 下采样到h4尺寸
        
        # 融合：512 + 256 + 128 = 896维 (与87.95% IoU架构一致)
        h_fused = torch.cat([h5_up, h4_conv, h3_down], dim=1)
        
        # 应用原始SCSA注意力
        h_attended = self.h_fusion(h_fused)
        h_final = self.h_fusion_conv(h_attended)

        # 低层特征处理
        l_final = self.l_fusion_conv(l2_enhanced)

        # 预测
        pred_h = self.predict_h(h_final)
        pred_l = self.predict_l(l_final)

        # 上采样
        pred_h = F.interpolate(pred_h, size=x.size()[2:], mode='bilinear', align_corners=True)
        pred_l = F.interpolate(pred_l, size=x.size()[2:], mode='bilinear', align_corners=True)

        # Sigmoid激活 - 确保数值稳定性
        pred_h_prob = torch.clamp(torch.sigmoid(pred_h), min=1e-7, max=1.0 - 1e-7)
        pred_l_prob = torch.clamp(torch.sigmoid(pred_l), min=1e-7, max=1.0 - 1e-7)

        # 自适应权重融合
        weights = F.softmax(self.adaptive_weights, dim=0)
        ensemble_pred = weights[0] * pred_h_prob + weights[1] * pred_l_prob
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0 - 1e-7)

        # 移除增强模块，避免数值不稳定
        final_pred = ensemble_pred

        # CRF后处理
        bg_logits = torch.log(1 - final_pred + 1e-7)
        fg_logits = torch.log(final_pred + 1e-7)
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        normalized_img = self._normalize_image(x)

        try:
            refined_predict = self.crf(combined_logits, normalized_img)
        except Exception as e:
            print(f"CRF错误: {e}")
            refined_predict = torch.cat([1 - final_pred, final_pred], dim=1)

        return {
            'pred_h': pred_h_prob,
            'pred_l': pred_l_prob,
            'ensemble_pred': final_pred,
            'refined_pred': refined_predict,
            'physics_maps': {
                'h_edge': F.interpolate(h_edge_weight, size=x.size()[2:], mode='bilinear', align_corners=True),
                'h_transparency': F.interpolate(h_transparency_weight, size=x.size()[2:], mode='bilinear', align_corners=True),
                'l_edge': F.interpolate(l_edge_weight, size=x.size()[2:], mode='bilinear', align_corners=True),
                'l_transparency': F.interpolate(l_transparency_weight, size=x.size()[2:], mode='bilinear', align_corners=True)
            }
        }

    def _normalize_image(self, x):
        """图像归一化"""
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        return normalized_img


def create_hybrid_scsa_model(backbone_path=None, crf_iter=5, trainable_crf=True):
    """创建混合SCSA模型"""
    return GDNetHybridSCSA(
        backbone_path=backbone_path,
        crf_iter=crf_iter,
        trainable_crf=trainable_crf
    )
