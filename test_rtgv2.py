import torch
import torch.nn as nn
import sys
import os

# 添加路径
sys.path.append('GlassDiffusion/models')
sys.path.append('ig_glass')

from RTGlassNetv2 import RTGlassNetv2
from loss_rtgv2 import RTGlassNetv2Loss, RTGlassNetv2LossWithDT

def test_rtgv2_model():
    """测试RTGlassNetv2模型"""
    print("🧪 测试RTGlassNetv2模型...")
    
    # 创建模型
    model = RTGlassNetv2(backbone_type='inceptionnext_base')
    model.eval()
    
    # 创建测试输入
    batch_size = 2
    height, width = 224, 224
    x = torch.randn(batch_size, 3, height, width)
    
    print(f"📊 输入尺寸: {x.shape}")
    print(f"🔢 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 前向传播
    try:
        with torch.no_grad():
            h_pred, l_pred, final_pred, refined_pred = model(x)
        
        print("✅ 前向传播成功!")
        print(f"📊 输出尺寸:")
        print(f"   h_pred: {h_pred.shape}")
        print(f"   l_pred: {l_pred.shape}")
        print(f"   final_pred: {final_pred.shape}")
        print(f"   refined_pred: {refined_pred.shape}")
        
        # 检查输出范围
        print(f"📈 输出范围检查:")
        print(f"   h_pred: [{h_pred.min().item():.4f}, {h_pred.max().item():.4f}]")
        print(f"   l_pred: [{l_pred.min().item():.4f}, {l_pred.max().item():.4f}]")
        print(f"   final_pred: [{final_pred.min().item():.4f}, {final_pred.max().item():.4f}]")
        print(f"   refined_pred: [{refined_pred.min().item():.4f}, {refined_pred.max().item():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        return False

def test_rtgv2_loss():
    """测试RTGlassNetv2损失函数"""
    print("\n🧪 测试RTGlassNetv2损失函数...")
    
    # 创建模型和损失函数
    model = RTGlassNetv2(backbone_type='inceptionnext_base')
    criterion = RTGlassNetv2Loss(
        focal_weight=0.6,
        iou_weight=0.4,
        edge_weight=0.2,
        crf_weight=0.1,
        consistency_weight=0.1
    )
    
    # 创建测试数据
    batch_size = 2
    height, width = 224, 224
    x = torch.randn(batch_size, 3, height, width)
    target = torch.randint(0, 2, (batch_size, 1, height, width)).float()
    
    print(f"📊 输入尺寸: {x.shape}")
    print(f"📊 目标尺寸: {target.shape}")
    
    # 前向传播
    try:
        model.train()
        predictions = model(x)
        
        # 计算损失
        total_loss, loss_dict = criterion(predictions, target)
        
        print("✅ 损失计算成功!")
        print(f"📊 损失详情:")
        for key, value in loss_dict.items():
            print(f"   {key}: {value:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rtgv2_loss_with_dt():
    """测试支持DT的损失函数"""
    print("\n🧪 测试RTGlassNetv2损失函数（支持DT）...")
    
    # 创建模型和损失函数
    model = RTGlassNetv2(backbone_type='inceptionnext_base')
    criterion = RTGlassNetv2LossWithDT(
        focal_weight=0.6,
        iou_weight=0.4,
        edge_weight=0.2,
        crf_weight=0.1,
        consistency_weight=0.1,
        dt_weight=0.1
    )
    
    # 创建测试数据
    batch_size = 2
    height, width = 224, 224
    x = torch.randn(batch_size, 3, height, width)
    target = torch.randint(0, 2, (batch_size, 1, height, width)).float()
    
    # 创建教师模型预测（模拟）
    teacher_model = RTGlassNetv2(backbone_type='inceptionnext_base')
    teacher_model.eval()
    
    try:
        model.train()
        with torch.no_grad():
            teacher_predictions = teacher_model(x)
        
        predictions = model(x)
        
        # 计算损失（带DT）
        total_loss, loss_dict = criterion(predictions, target, teacher_predictions)
        
        print("✅ DT损失计算成功!")
        print(f"📊 损失详情:")
        for key, value in loss_dict.items():
            print(f"   {key}: {value:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ DT损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_complexity():
    """测试模型复杂度"""
    print("\n🧪 测试模型复杂度...")
    
    model = RTGlassNetv2(backbone_type='inceptionnext_base')
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"📊 模型复杂度:")
    print(f"   总参数数量: {total_params:,}")
    print(f"   可训练参数: {trainable_params:,}")
    print(f"   模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # 测试推理速度
    import time
    batch_size = 1
    height, width = 224, 224
    x = torch.randn(batch_size, 3, height, width)
    
    model.eval()
    with torch.no_grad():
        # 预热
        for _ in range(5):
            _ = model(x)
        
        # 测试推理时间
        start_time = time.time()
        for _ in range(10):
            _ = model(x)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        fps = 1.0 / avg_time
        
        print(f"🚀 推理性能:")
        print(f"   平均推理时间: {avg_time*1000:.2f} ms")
        print(f"   推理FPS: {fps:.2f}")

if __name__ == "__main__":
    print("🚀 开始测试RTGlassNetv2...")
    
    # 测试模型
    model_success = test_rtgv2_model()
    
    # 测试损失函数
    loss_success = test_rtgv2_loss()
    
    # 测试DT损失函数
    dt_loss_success = test_rtgv2_loss_with_dt()
    
    # 测试模型复杂度
    test_model_complexity()
    
    print("\n📋 测试总结:")
    print(f"   模型测试: {'✅ 通过' if model_success else '❌ 失败'}")
    print(f"   损失函数测试: {'✅ 通过' if loss_success else '❌ 失败'}")
    print(f"   DT损失函数测试: {'✅ 通过' if dt_loss_success else '❌ 失败'}")
    
    if model_success and loss_success and dt_loss_success:
        print("\n🎉 所有测试通过！RTGlassNetv2准备就绪！")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息。") 