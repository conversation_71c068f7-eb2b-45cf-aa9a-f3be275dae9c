# models/pgd_net.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 核心网络模块 - SOTA 多尺度融合架构
# 融合Proteus-ViT的强大特征与扩散模型的精准精炼，目标IoU > 90%
# -----------------------------------------------------------------------------

import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
import math
import numpy as np
from typing import Dict, Optional, List, Tuple
from einops import rearrange

# 添加必要的路径
import config
import os

# 确保路径存在并且是绝对路径
PROTEUS_PATH = os.path.abspath(config.PROTEUS_PATH)
IG_GLASS_PATH = os.path.abspath(config.IG_GLASS_PATH)

if PROTEUS_PATH not in sys.path:
    sys.path.append(PROTEUS_PATH)
if IG_GLASS_PATH not in sys.path:
    sys.path.append(IG_GLASS_PATH)

print(f"INFO: 添加路径到sys.path:")
print(f"  - Proteus路径: {PROTEUS_PATH}")
print(f"  - IG_GLASS路径: {IG_GLASS_PATH}")

# 新增Proteus骨干导入
sys.path.append('/home/<USER>/ws/IG_SLAM/Proteus-pytorch/pretrain')  # 路径按实际情况调整
import models_dinov2
models_dinov2.XFORMERS_AVAILABLE = False
from models_proteus_dinov2 import MetaArch

# 导入本地CRF实现
from .diff_crf import SimplifiedDiffCRF

###################################################################
# #################### SCSA 注意力机制 ############################
###################################################################
class SCSA(nn.Module):
    """
    空间-通道自注意力（Spatial-Channel Self Attention）模块
    基于用户现有代码的优化版本
    """
    def __init__(
            self,
            dim: int,
            head_num: int,
            window_size: int = 7,
            group_kernel_sizes: List[int] = [3, 5, 7, 9],
            qkv_bias: bool = False,
            attn_drop_ratio: float = 0.,
            gate_layer: str = 'sigmoid',
            channel_weight: float = 1.0,
    ):
        super(SCSA, self).__init__()
        self.dim = dim
        self.head_num = head_num
        self.head_dim = dim // head_num
        self.scaler = self.head_dim ** -0.5
        self.group_kernel_sizes = group_kernel_sizes
        self.window_size = window_size
        self.channel_weight = channel_weight

        assert self.dim % 4 == 0, '输入特征的维度应该被4整除'
        self.group_chans = group_chans = self.dim // 4

        # 局部和全局深度卷积
        self.local_dwc = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[0],
                                   padding=group_kernel_sizes[0] // 2, groups=group_chans)
        self.global_dwc_s = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[1],
                                      padding=group_kernel_sizes[1] // 2, groups=group_chans)
        self.global_dwc_m = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[2],
                                      padding=group_kernel_sizes[2] // 2, groups=group_chans)
        self.global_dwc_l = nn.Conv1d(group_chans, group_chans, kernel_size=group_kernel_sizes[3],
                                      padding=group_kernel_sizes[3] // 2, groups=group_chans)
        
        # 空间注意力门控
        self.sa_gate = nn.Softmax(dim=2) if gate_layer == 'softmax' else nn.Sigmoid()
        self.norm_h = nn.GroupNorm(4, dim)
        self.norm_w = nn.GroupNorm(4, dim)

        # 通道注意力组件
        self.conv_d = nn.Identity()
        self.norm = nn.GroupNorm(1, dim)
        self.q = nn.Conv2d(in_channels=dim, out_channels=dim, kernel_size=1, bias=qkv_bias, groups=dim)
        self.k = nn.Conv2d(in_channels=dim, out_channels=dim, kernel_size=1, bias=qkv_bias, groups=dim)
        self.v = nn.Conv2d(in_channels=dim, out_channels=dim, kernel_size=1, bias=qkv_bias, groups=dim)
        self.attn_drop = nn.Dropout(attn_drop_ratio)
        self.ca_gate = nn.Softmax(dim=1) if gate_layer == 'softmax' else nn.Sigmoid()

        # 下采样方法
        if window_size == -1:
            self.down_func = nn.AdaptiveAvgPool2d((1, 1))
        else:
            self.down_func = nn.AvgPool2d(kernel_size=(window_size, window_size), stride=window_size)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        x的维度是(B, C, H, W)
        """
        # 空间注意力优先计算
        b, c, h_, w_ = x.size()
        # (B, C, H)
        x_h = x.mean(dim=3)
        l_x_h, g_x_h_s, g_x_h_m, g_x_h_l = torch.split(x_h, self.group_chans, dim=1)
        # (B, C, W)
        x_w = x.mean(dim=2)
        l_x_w, g_x_w_s, g_x_w_m, g_x_w_l = torch.split(x_w, self.group_chans, dim=1)

        # 应用水平方向的注意力
        x_h_attn = self.sa_gate(self.norm_h(torch.cat((
            self.local_dwc(l_x_h),
            self.global_dwc_s(g_x_h_s),
            self.global_dwc_m(g_x_h_m),
            self.global_dwc_l(g_x_h_l),
        ), dim=1)))
        x_h_attn = x_h_attn.view(b, c, h_, 1)

        # 应用垂直方向的注意力
        x_w_attn = self.sa_gate(self.norm_w(torch.cat((
            self.local_dwc(l_x_w),
            self.global_dwc_s(g_x_w_s),
            self.global_dwc_m(g_x_w_m),
            self.global_dwc_l(g_x_w_l)
        ), dim=1)))
        x_w_attn = x_w_attn.view(b, c, 1, w_)

        # 应用空间注意力
        x = x * x_h_attn * x_w_attn

        # 基于自注意力的通道注意力
        y = self.down_func(x)
        y = self.conv_d(y)
        _, _, h_, w_ = y.size()

        # 先归一化，然后reshape -> (B, H, W, C) -> (B, C, H * W)并生成q, k和v
        y = self.norm(y)
        q = self.q(y)
        k = self.k(y)
        v = self.v(y)
        # (B, C, H, W) -> (B, head_num, head_dim, N)
        q = rearrange(q, 'b (head_num head_dim) h w -> b head_num head_dim (h w)', head_num=int(self.head_num),
                      head_dim=int(self.head_dim))
        k = rearrange(k, 'b (head_num head_dim) h w -> b head_num head_dim (h w)', head_num=int(self.head_num),
                      head_dim=int(self.head_dim))
        v = rearrange(v, 'b (head_num head_dim) h w -> b head_num head_dim (h w)', head_num=int(self.head_num),
                      head_dim=int(self.head_dim))

        # (B, head_num, head_dim, head_dim)
        attn = q @ k.transpose(-2, -1) * self.scaler
        attn = self.attn_drop(attn.softmax(dim=-1))
        # (B, head_num, head_dim, N)
        attn = attn @ v
        # (B, C, H_, W_)
        attn = rearrange(attn, 'b head_num head_dim (h w) -> b (head_num head_dim) h w', h=int(h_), w=int(w_))
        # (B, C, 1, 1)
        attn = attn.mean((2, 3), keepdim=True)
        attn = self.ca_gate(attn)
        
        return attn * x * self.channel_weight

###################################################################
# ###################### LightEdgeEnhancer #######################
###################################################################
class LightEdgeEnhancer(nn.Module):
    """Lightweight module to enhance edge features"""
    def __init__(self, in_channels):
        super(LightEdgeEnhancer, self).__init__()
        # Depthwise separable convolution
        self.edge_conv = nn.Sequential(
            # Depthwise
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            # Pointwise
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # Laplacian kernel for edge detection
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
        
    def forward(self, x):
        # Apply edge-aware enhancement
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        
        # Apply edge-aware convolution
        enhanced = self.edge_conv(x)
        
        # Combine with original features with edge attention
        return x + enhanced * edge_features

###################################################################
# #################### 多尺度 ViT 特征提取器 #####################
###################################################################
class MultiScaleViTFeatureExtractor(nn.Module):
    """
    多尺度ViT特征提取器 - Proteus骨干集成版，默认vit_small
    """
    def __init__(self, pretrained_path=None, vit_model_name='vit_small', extract_layers=[3, 6, 9, 11]):
        super(MultiScaleViTFeatureExtractor, self).__init__()

        # Proteus专用cfg
        class SimpleCfg:
            def __init__(self):
                self.target_model = 'vit_small'
                self.patch_size = 14
                self.teacher_model = 'vit_small'
                self.batch_size = 8
                self.mask_probability = 0.0
        cfg = SimpleCfg()

        print(f"INFO: 正在初始化Proteus ViT骨干: {vit_model_name}")
        self.meta_arch = MetaArch(cfg)
        self.vit_backbone = self.meta_arch.student['backbone']
        self.embed_dim = self.vit_backbone.embed_dim
        self.patch_size = self.vit_backbone.patch_size
        self.extract_layers = extract_layers

        # 权重加载
        if pretrained_path and os.path.exists(pretrained_path):
            print(f"📦 Proteus骨干加载权重: {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            if 'model' in checkpoint:
                print("加载Proteus student权重 ...")
                student_state_dict = {k.replace('student.', ''): v for k, v in checkpoint['model'].items() if k.startswith('student.')}
                missing, unexpected = self.meta_arch.student.load_state_dict(student_state_dict, strict=False)
                print(f"缺失权重: {missing}")
                print(f"多余权重: {unexpected}")
            else:
                print("权重文件格式异常，未找到'model'字段！")
        else:
            print("⚠️ Proteus骨干未加载预训练权重，使用随机初始化！")

        # 其余adapter等保持不变
        self.feature_adapters = nn.ModuleList()
        adapter_dims = [256, 512, 768, 1024]
        for i, layer_idx in enumerate(self.extract_layers):
            adapter_dim = adapter_dims[min(i, len(adapter_dims)-1)]
            adapter = nn.Sequential(
                nn.Conv2d(self.embed_dim, adapter_dim, kernel_size=1, bias=False),
                nn.BatchNorm2d(adapter_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(adapter_dim, adapter_dim, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(adapter_dim),
                nn.ReLU(inplace=True)
            )
            self._init_adapter_weights(adapter)
            self.feature_adapters.append(adapter)

    def _init_adapter_weights(self, adapter):
        for module in adapter.modules():
            if isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.BatchNorm2d):
                nn.init.constant_(module.weight, 1.0)
                nn.init.constant_(module.bias, 0.0)

    def forward(self, x):
        # 正确调用 get_intermediate_layers 以提取真正的多尺度特征
        multi_scale_features_raw = self.vit_backbone.get_intermediate_layers(
            x, 
            n=self.extract_layers, # 使用 n 参数，因为Proteus的实现可能不同
            reshape=True, 
            return_class_token=False,
            norm=True
        )

        # 修复：增加对元组(tuple)输出的兼容性处理
        if isinstance(multi_scale_features_raw, tuple):
            multi_scale_features_raw = list(multi_scale_features_raw)

        if not isinstance(multi_scale_features_raw, list) or len(multi_scale_features_raw) != len(self.extract_layers):
            print(f"[ERROR] ViT骨干输出格式未知: type={type(multi_scale_features_raw)}, len={len(multi_scale_features_raw) if isinstance(multi_scale_features_raw, list) else 'N/A'}")
            raise RuntimeError("Proteus ViT骨干输出格式不支持！未能返回预期的多尺度特征列表。")

        # 将提取出的原始特征送入各自的适配器
        multi_scale_features = []
        for i, (raw_feature, adapter) in enumerate(zip(multi_scale_features_raw, self.feature_adapters)):
            adapted_features = adapter(raw_feature)
            multi_scale_features.append(adapted_features)
            
        return multi_scale_features

###################################################################
# ###################### FPN风格解码器 ###########################
###################################################################
class FPNDecoder(nn.Module):
    """
    特征金字塔网络解码器
    """
    def __init__(self, feature_dims=[256, 512, 768, 1024], output_dim=256):
        super(FPNDecoder, self).__init__()
        
        # 特征维度
        self.feature_dims = feature_dims
        self.output_dim = output_dim
        
        # 创建横向连接
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(dim, output_dim, kernel_size=1)
            for dim in feature_dims
        ])
        
        # 创建输出卷积
        self.output_convs = nn.ModuleList([
            nn.Conv2d(output_dim, output_dim, kernel_size=3, padding=1)
            for _ in range(len(feature_dims))
        ])
        
        # 初始化权重
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, multi_scale_features):
        """
        前向传播
        Args:
            multi_scale_features: 多尺度特征列表，从深到浅
        Returns:
            fpn_features: 特征金字塔字典
                - p0: 最深层特征
                - p1: 次深层特征
                - p2: 次浅层特征
                - p3: 最浅层特征
        """
        assert len(multi_scale_features) == len(self.feature_dims), \
            f"特征数量不匹配: {len(multi_scale_features)} vs {len(self.feature_dims)}"
        
        # 1. 横向连接
        laterals = [
            conv(feature)
            for feature, conv in zip(multi_scale_features, self.lateral_convs)
        ]
        
        # 2. 自顶向下路径
        fpn_features = [laterals[-1]]  # 从最深层开始
        for i in range(len(laterals)-2, -1, -1):
            # 上采样
            top_down = F.interpolate(
                fpn_features[-1], 
                size=laterals[i].shape[2:],
                mode='bilinear', 
                align_corners=True
            )
            # 特征融合
            fpn_features.append(laterals[i] + top_down)
        
        # 3. 输出卷积
        outputs = [
            conv(feature)
            for feature, conv in zip(fpn_features, self.output_convs)
        ]
        
        # 4. 构建输出字典
        fpn_features_dict = {
            f'p{i}': feature
            for i, feature in enumerate(outputs[::-1])  # 反转列表，使p0为最浅层
        }
        
        return fpn_features_dict

###################################################################
# ###################### 特征精炼模块 ############################
###################################################################
class FeatureRefiner(nn.Module):
    """
    简化的特征精炼模块，用于处理FPN的最高分辨率输出
    """
    def __init__(self, in_channels=256, out_channels=256):
        super(FeatureRefiner, self).__init__()
        
        self.refine_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        """简单的特征精炼"""
        return self.refine_conv(x)



class GeometryHead(nn.Module):
    """
    几何头部 - 处理边缘和距离变换预测
    """
    def __init__(self, in_channels, geometry_channels=64):
        super(GeometryHead, self).__init__()
        
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels, geometry_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(geometry_channels),
            nn.ReLU(inplace=True)
        )
        
        self.conv2 = nn.Sequential(
            nn.Conv2d(geometry_channels, geometry_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(geometry_channels),
            nn.ReLU(inplace=True)
        )
        
        # 主预测分支
        self.main_branch = nn.Conv2d(geometry_channels, 1, kernel_size=1)
        
        # 距离变换分支
        self.dt_branch = nn.Conv2d(geometry_channels, 1, kernel_size=1)
        
    def forward(self, x):
        """
        前向传播
        Args:
            x: [B, C, H, W] 输入特征
        Returns:
            outputs: 输出字典
                - main_logits: [B, 1, H, W] 主预测logits
                - dt_logits: [B, 1, H, W] 距离变换logits
        """
        # 特征提取
        feat = self.conv1(x)
        feat = self.conv2(feat)
        
        # 生成预测
        main_logits = self.main_branch(feat)
        dt_logits = self.dt_branch(feat)
        
        return {
            'main_logits': main_logits,
            'dt_logits': dt_logits
        }

###################################################################
# #################### 主网络模型 ################################
###################################################################
class ProteusGlassNet(nn.Module):
    """
    ProteusGlassNet - SOTA 多尺度融合玻璃检测网络（优化深度监督版）
    
    架构特点：
    1. 多尺度ViT特征提取（使用中间层）
    2. FPN风格跳跃连接解码器
    3. 分层深度监督（不同语义层次的监督）
    4. 多尺度几何预测模块
    5. SCSA注意力机制
    6. 简化CRF后处理
    
    深度监督优化：
    - 几何监督：作用于P2层（次高分辨率，保留低级细节）
    - 透明度监督：作用于深层特征（高级语义，需要全局信息）
    - 主预测：融合所有特征后的最终输出
    
    设计理念：在不同语义层次施加监督，让网络在多个层级都能得到有效的梯度指导，
    从而提升整体性能。
    """
    def __init__(self, vit_model_name='vit_small', backbone_path=None, 
                 extract_layers=[3, 6, 9, 11], crf_iter=5, trainable_crf=True):
        super(ProteusGlassNet, self).__init__()
        
        print("🚀 初始化 SOTA ProteusGlassNet（优化深度监督版）...")
        print("📌 深度监督策略：")
        print("   - 几何监督：P2层（次高分辨率，低级细节）")
        print("   - 透明度监督：深层特征（高级语义，全局信息）")
        print("   - 主预测：融合后最终输出")
        
        # 1. 多尺度ViT特征提取器
        self.backbone = MultiScaleViTFeatureExtractor(
            pretrained_path=backbone_path,
            vit_model_name=vit_model_name,
            extract_layers=extract_layers
        )
        
        # 特征维度配置
        feature_dims = [256, 512, 768, 1024]  # 对应extract_layers的特征维度
        unified_dim = 256
        
        # 2. FPN风格解码器
        self.fpn_decoder = FPNDecoder(
            feature_dims=feature_dims,
            output_dim=unified_dim
        )
        
        # 3. 特征精炼模块（处理FPN最高分辨率输出作为信息总线）
        self.feature_refiner = FeatureRefiner(
            in_channels=unified_dim,
            out_channels=unified_dim
        )
        # 3.5 边缘增强模块（插入在特征精炼后）
        self.edge_enhancer = LightEdgeEnhancer(unified_dim)
        
        # 4. 多尺度几何预测模块
        self.geometry_head = GeometryHead(
            in_channels=unified_dim,
            geometry_channels=64
        )
        
        # 5. SCSA注意力模块
        scsa_input_dim = unified_dim + 64  # 特征 + 几何特征
        # 确保维度能被4整除
        scsa_dim = ((scsa_input_dim + 3) // 4) * 4
        self.scsa_adapter = nn.Conv2d(scsa_input_dim, scsa_dim, kernel_size=1)
        
        self.scsa_attention = SCSA(
            dim=scsa_dim,
            head_num=8,
            window_size=7
        )
        
        # 6. 预测头
        self.main_predict = nn.Sequential(
            nn.Conv2d(scsa_dim, unified_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(unified_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(unified_dim, 1, kernel_size=1)
        )
        
        # 辅助预测头
        self.transparency_predict = nn.Conv2d(unified_dim, 1, kernel_size=1)
        
        # 7. CRF后处理 - 修复配置错误
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            trainable=trainable_crf,
            bilateral_weight=5.0  # 使用固定的安全值
        )
        
        print("✅ SOTA ProteusGlassNet 初始化完成!")
        self._print_model_info()
        
    def _print_model_info(self):
        """打印模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        print(f"📊 SOTA模型参数统计:")
        print(f"  - 总参数量: {total_params/1e6:.2f}M")
        print(f"  - 可训练参数: {trainable_params/1e6:.2f}M")
        
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播 - 增强数值稳定性版本
        Args:
            x: [B, 3, H, W] 输入图像
        Returns:
            outputs: 输出字典
                - main_pred: [B, 1, H, W] 主预测
                - dt_pred: [B, 1, H, W] 距离变换预测
                - transparency_pred: [B, 1, H, W] 透明度预测 (用于深度监督)
        """
        input_size = x.shape[2:]  # 保存输入尺寸
        
        # 🔧 紧急修复：输入数值范围检查
        if torch.isnan(x).any() or torch.isinf(x).any():
            print(f"⚠️ 输入包含NaN/Inf，使用安全值替换")
            x = torch.nan_to_num(x, nan=0.0, posinf=1.0, neginf=-1.0)
        
        try:
            # 1. 特征提取 - 增强数值稳定性检查
            try:
                multi_scale_features = self.backbone(x)
            except Exception as e:
                print(f"❌ Backbone前向传播失败: {e}")
                # 返回安全的默认输出
                batch_size = x.shape[0]
                return self._get_safe_outputs(batch_size, input_size, x.device)
            
            # 检查backbone输出 - 更严格的数值检查
            for i, feat in enumerate(multi_scale_features):
                if torch.isnan(feat).any() or torch.isinf(feat).any():
                    print(f"⚠️ Backbone特征{i}包含NaN/Inf，使用安全替换")
                    # 使用更安全的替换策略
                    safe_feat = torch.zeros_like(feat)
                    safe_feat.normal_(0.0, 0.01)  # 小方差正态分布
                    multi_scale_features[i] = safe_feat
                
                # 检查特征范围是否合理
                feat_std = feat.std()
                if feat_std > 10.0 or feat_std < 1e-6:
                    print(f"⚠️ Backbone特征{i}方差异常: {feat_std:.6f}，进行归一化")
                    multi_scale_features[i] = F.layer_norm(feat, feat.shape[1:])
            
            # 2. FPN解码 - 数值保护
            try:
                fpn_features = self.fpn_decoder(multi_scale_features)
            except Exception as e:
                print(f"❌ FPN解码失败: {e}")
                batch_size = x.shape[0]
                return self._get_safe_outputs(batch_size, input_size, x.device)
            
            # 检查FPN输出
            for key, feat in fpn_features.items():
                if torch.isnan(feat).any() or torch.isinf(feat).any():
                    print(f"⚠️ FPN特征{key}包含NaN/Inf，使用安全替换")
                    safe_feat = torch.zeros_like(feat)
                    safe_feat.normal_(0.0, 0.01)
                    fpn_features[key] = safe_feat
            
            # 3. 特征精炼 - 数值保护
            try:
                refined_features = self.feature_refiner(fpn_features['p2'])
            except Exception as e:
                print(f"❌ 特征精炼失败: {e}")
                batch_size = x.shape[0]
                return self._get_safe_outputs(batch_size, input_size, x.device)
            
            # 检查精炼后特征
            if torch.isnan(refined_features).any() or torch.isinf(refined_features).any():
                print("⚠️ 精炼特征包含NaN/Inf，使用安全替换")
                refined_features = torch.zeros_like(refined_features)
                refined_features.normal_(0.0, 0.01)
            # 3.5 边缘增强
            try:
                enhanced_features = self.edge_enhancer(refined_features)
            except Exception as e:
                print(f"❌ 边缘增强失败: {e}")
                enhanced_features = refined_features
            # 检查增强后特征
            if torch.isnan(enhanced_features).any() or torch.isinf(enhanced_features).any():
                print("⚠️ 边缘增强特征包含NaN/Inf，使用安全替换")
                enhanced_features = torch.zeros_like(enhanced_features)
                enhanced_features.normal_(0.0, 0.01)
            
            # 4. 几何头部预测 - 数值保护
            try:
                geometry_outputs = self.geometry_head(enhanced_features)
            except Exception as e:
                print(f"❌ 几何头部预测失败: {e}")
                batch_size = x.shape[0]
                return self._get_safe_outputs(batch_size, input_size, x.device)
            
            # 检查几何预测并限制logits范围
            for key, value in geometry_outputs.items():
                if torch.isnan(value).any() or torch.isinf(value).any():
                    print(f"⚠️ 几何预测{key}包含NaN/Inf，使用安全替换")
                    geometry_outputs[key] = torch.zeros_like(value)
                else:
                    # 严格限制logits范围，避免数值爆炸
                    geometry_outputs[key] = torch.clamp(value, min=-5.0, max=5.0)
            
            # 5. 透明度预测 - 数值保护
            try:
                transparency_logits = self.transparency_predict(fpn_features['p0'])
                transparency_logits = F.interpolate(
                    transparency_logits,
                    size=input_size,
                    mode='bilinear',
                    align_corners=True
                )
            except Exception as e:
                print(f"❌ 透明度预测失败: {e}")
                transparency_logits = torch.zeros((x.shape[0], 1, input_size[0], input_size[1]), device=x.device)
            
            # 检查透明度预测
            if torch.isnan(transparency_logits).any() or torch.isinf(transparency_logits).any():
                print("⚠️ transparency_logits包含NaN/Inf，使用零初始化")
                transparency_logits = torch.zeros_like(transparency_logits)
            else:
                transparency_logits = torch.clamp(transparency_logits, min=-5.0, max=5.0)
            
            # 6. 上采样主预测和距离变换预测到输入尺寸 - 数值保护
            try:
                main_logits = F.interpolate(
                    geometry_outputs['main_logits'], 
                    size=input_size, 
                    mode='bilinear', 
                    align_corners=True
                )
                dt_logits = F.interpolate(
                    geometry_outputs['dt_logits'], 
                    size=input_size, 
                    mode='bilinear', 
                    align_corners=True
                )
            except Exception as e:
                print(f"❌ 上采样失败: {e}")
                main_logits = torch.zeros((x.shape[0], 1, input_size[0], input_size[1]), device=x.device)
                dt_logits = torch.zeros((x.shape[0], 1, input_size[0], input_size[1]), device=x.device)
            
            # 7. 应用激活函数 - 严格数值保护
            main_logits = torch.clamp(main_logits, min=-5.0, max=5.0)
            dt_logits = torch.clamp(dt_logits, min=-5.0, max=5.0)
            transparency_logits = torch.clamp(transparency_logits, min=-5.0, max=5.0)
            
            # 使用数值稳定的sigmoid
            main_pred = torch.sigmoid(main_logits)
            dt_pred = torch.sigmoid(dt_logits)
            transparency_pred = torch.sigmoid(transparency_logits)
            
            # 最终安全检查和范围限制
            main_pred = torch.clamp(main_pred, min=1e-6, max=1.0-1e-6)
            dt_pred = torch.clamp(dt_pred, min=1e-6, max=1.0-1e-6)
            transparency_pred = torch.clamp(transparency_pred, min=1e-6, max=1.0-1e-6)
            
            # 8. 返回结果 - 最终NaN/Inf检查
            outputs = {
                'main_pred': main_pred,
                'dt_pred': dt_pred,
                'transparency_pred': transparency_pred,
                # 添加损失函数期望的额外输出
                'edge_pred': dt_pred,  # 边缘预测使用距离变换
                'final_pred': main_pred,  # 最终预测使用主预测
                'ensemble_pred': 0.7 * main_pred + 0.3 * dt_pred,  # 集成预测
                'refined_pred': main_pred  # 暂时使用主预测，后续可添加CRF
            }
            
            # 确保ensemble_pred在合理范围内
            outputs['ensemble_pred'] = torch.clamp(outputs['ensemble_pred'], min=1e-6, max=1.0-1e-6)
            
            # 最终验证所有输出
            for key, value in outputs.items():
                if torch.isnan(value).any() or torch.isinf(value).any():
                    print(f"⚠️ 最终输出{key}包含NaN/Inf，使用安全默认值")
                    if key in ['main_pred', 'final_pred', 'ensemble_pred', 'refined_pred']:
                        outputs[key] = torch.full_like(value, 0.5)
                    else:
                        outputs[key] = torch.full_like(value, 0.1)
            
            return outputs
            
        except Exception as e:
            print(f"⚠️ 前向传播出错: {e}")
            # 返回安全的默认输出
            batch_size = x.shape[0]
            return self._get_safe_outputs(batch_size, input_size, x.device)
    
    def _get_safe_outputs(self, batch_size: int, input_size: tuple, device: torch.device) -> Dict[str, torch.Tensor]:
        """返回安全的默认输出"""
        safe_pred = torch.full((batch_size, 1, input_size[0], input_size[1]), 0.5, device=device)
        safe_edge = torch.full((batch_size, 1, input_size[0], input_size[1]), 0.1, device=device)
        safe_transparency = torch.full((batch_size, 1, input_size[0], input_size[1]), 0.5, device=device)
        
        return {
            'main_pred': safe_pred,
            'dt_pred': safe_edge,
            'transparency_pred': safe_transparency,
            'edge_pred': safe_edge,
            'final_pred': safe_pred,
            'ensemble_pred': safe_pred,
            'refined_pred': safe_pred
        }

# 导出所有模型类
__all__ = [
    'MultiScaleViTFeatureExtractor',
    'FPNDecoder',
    'FeatureRefiner',
    'GeometryHead',
    'ProteusGlassNet', 
    'SCSA'
]

