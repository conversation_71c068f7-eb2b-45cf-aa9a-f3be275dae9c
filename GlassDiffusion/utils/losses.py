# utils/losses.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 损失函数
# 包含专门针对玻璃检测优化的各种损失函数
# -----------------------------------------------------------------------------

import torch
import torch.nn as nn
import torch.nn.functional as F
import config
from typing import Dict, Tuple, Optional

class FocalLoss(nn.Module):
    """
    Focal Loss - 专注于难分类样本
    """
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0):
        super(Focal<PERSON>oss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算Focal Loss
        Args:
            pred: [B, 1, H, W] 预测概率
            target: [B, 1, H, W] 二值目标
        """
        # 数值稳定性保护
        eps = 1e-7
        pred = torch.clamp(pred, eps, 1.0 - eps)
        
        # 计算二值交叉熵
        ce_loss = -(target * torch.log(pred) + (1 - target) * torch.log(1 - pred))
        
        # 计算focal权重
        p_t = target * pred + (1 - target) * (1 - pred)
        alpha_t = target * self.alpha + (1 - target) * (1 - self.alpha)
        focal_weight = alpha_t * (1 - p_t).pow(self.gamma)
        
        # 计算最终损失
        focal_loss = focal_weight * ce_loss
        
        return focal_loss.mean()

class DiceLoss(nn.Module):
    """
    Dice Loss for segmentation
    """
    def __init__(self, smooth=1e-5):
        super(DiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, inputs, targets):
        inputs = torch.sigmoid(inputs)
        
        # 展平张量
        inputs = inputs.view(-1)
        targets = targets.view(-1)
        
        # 计算交集
        intersection = (inputs * targets).sum()
        
        # 计算Dice系数
        dice = (2. * intersection + self.smooth) / (inputs.sum() + targets.sum() + self.smooth)
        
        return 1 - dice

class DiceBCELoss(nn.Module):
    """
    Dice + BCE损失的组合，平衡像素级精度和区域级完整性
    """
    def __init__(self, dice_weight=0.5, bce_weight=0.5, smooth=1e-5):
        super(DiceBCELoss, self).__init__()
        self.dice_weight = dice_weight
        self.bce_weight = bce_weight
        self.dice_loss = DiceLoss(smooth=smooth)
        self.bce_loss = nn.BCEWithLogitsLoss()

    def forward(self, inputs, targets):
        dice = self.dice_loss(inputs, targets)
        bce = self.bce_loss(inputs, targets)
        
        return self.dice_weight * dice + self.bce_weight * bce

class IoULoss(nn.Module):
    """
    IoU Loss - 直接优化IoU指标
    """
    def __init__(self):
        super(IoULoss, self).__init__()
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算IoU Loss
        Args:
            pred: [B, 1, H, W] 预测概率
            target: [B, 1, H, W] 二值目标
        """
        # 数值稳定性保护
        eps = 1e-7
        pred = torch.clamp(pred, eps, 1.0 - eps)
        
        # 计算交集和并集
        intersection = (pred * target).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) - intersection
        
        # 计算IoU
        iou = (intersection + eps) / (union + eps)
        
        # 转换为损失
        loss = 1 - iou
        
        return loss.mean()

class EdgeLoss(nn.Module):
    """
    边缘损失，专门用于玻璃边缘检测
    """
    def __init__(self, edge_weight=1.0):
        super(EdgeLoss, self).__init__()
        self.edge_weight = edge_weight
        self.bce_loss = nn.BCEWithLogitsLoss()
        
        # 定义边缘检测核
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32).view(1, 1, 3, 3)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32).view(1, 1, 3, 3)
        
        self.register_buffer('sobel_x', sobel_x)
        self.register_buffer('sobel_y', sobel_y)

    def extract_edges(self, mask):
        """提取边缘"""
        # 确保sobel核与输入在同一设备
        sobel_x = self.sobel_x.to(mask.device)
        sobel_y = self.sobel_y.to(mask.device)
        
        edge_x = F.conv2d(mask, sobel_x, padding=1)
        edge_y = F.conv2d(mask, sobel_y, padding=1)
        edges = torch.sqrt(edge_x**2 + edge_y**2 + 1e-8)
        return edges

    def forward(self, pred_logits, gt_mask):
        # 提取GT边缘
        gt_edges = self.extract_edges(gt_mask)
        
        # 计算边缘损失
        edge_loss = self.bce_loss(pred_logits, gt_edges)
        
        return self.edge_weight * edge_loss

class TransparencyLoss(nn.Module):
    """
    透明度损失，专门用于玻璃透明度预测 - 纯MSE版本，避免混合精度问题
    """
    def __init__(self, alpha=1.0):
        super(TransparencyLoss, self).__init__()
        self.alpha = alpha
        self.mse_loss = nn.MSELoss()

    def forward(self, transparency_pred, gt_mask):
        """
        Args:
            transparency_pred: [B, 1, H, W] 透明度预测 (已经过sigmoid)
            gt_mask: [B, 1, H, W] 真值掩码
        """
        # 🔧 彻底简化：只使用MSE损失，完全兼容混合精度
        transparency_safe = transparency_pred.clamp(0.0, 1.0)  # 创建新tensor
        gt_safe = gt_mask.clamp(0.0, 1.0)  # 创建新tensor
        
        # 只使用MSE损失，数值稳定且兼容混合精度
        return self.alpha * self.mse_loss(transparency_safe, gt_safe)

class DistanceTransformLoss(nn.Module):
    """
    距离变换损失 - 优化边界预测
    适用于[0,1]范围的距离变换图
    """
    def __init__(self, l1_weight: float = 0.7, mse_weight: float = 0.3):
        super(DistanceTransformLoss, self).__init__()
        self.l1_weight = l1_weight
        self.mse_weight = mse_weight
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算距离变换损失
        Args:
            pred: [B, 1, H, W] 预测的距离变换图 [0, 1] 范围
            target: [B, 1, H, W] 目标距离变换图 [0, 1] 范围
        """
        # 数值稳定性保护和范围检查
        eps = 1e-7
        pred = torch.clamp(pred, 0.0, 1.0)
        target = torch.clamp(target, 0.0, 1.0)
        
        # L1损失 - 适合梯度信息
        l1_loss = F.l1_loss(pred, target)
        
        # MSE损失 - 适合连续值回归
        mse_loss = F.mse_loss(pred, target)
        
        # 组合损失
        loss = self.l1_weight * l1_loss + self.mse_weight * mse_loss
        
        return loss

class ConsistencyLoss(nn.Module):
    """
    一致性损失 - 确保不同预测之间的一致性 (无in-place操作版本)
    """
    def __init__(self, weight: float = 0.1):
        super(ConsistencyLoss, self).__init__()
        self.weight = weight
        
    def forward(self, pred1: torch.Tensor, pred2: torch.Tensor) -> torch.Tensor:
        """
        计算一致性损失
        Args:
            pred1, pred2: [B, 1, H, W] 两个预测（都应该已经经过激活函数）
        """
        # 🔧 关键修复：完全避免in-place操作，创建新的tensor
        pred1_safe = pred1.clamp(1e-6, 1.0-1e-6)  # 创建新tensor，不修改原tensor
        pred2_safe = pred2.clamp(1e-6, 1.0-1e-6)  # 创建新tensor，不修改原tensor
        
        return self.weight * F.mse_loss(pred1_safe, pred2_safe)

class HierarchicalSupervisionLoss(nn.Module):
    """
    SOTA级分层监督损失 - 核心损失主导 + 辅助损失协调
    
    损失架构设计:
    🎯 核心损失(主导地位，权重大):
        - Focal Loss: 处理类别不平衡，专注难样本
        - IoU Loss: 直接优化评估指标
        - Distance Transform Loss: 边界几何监督
    
    🔧 辅助损失(协调作用，权重小):
        - Transparency Loss: 深度监督，利用高级语义
        - Consistency Loss: 内部协调，确保预测一致性
        
    权重配置原则:
    - 核心损失权重: 1.0-1.2 (主导训练方向)
    - 辅助损失权重: 0.05-0.15 (精细调节和协调)
    - 总权重控制: 避免梯度爆炸，保持数值稳定
    """
    def __init__(
        self,
        focal_weight: float = 0.410,
        iou_weight: float = 0.7,
        dt_weight: float = 0.137,
        transparency_weight: float = 0.068,
        consistency_weight: float = 0.034,
        total_weight: float = 1.35,
        enable_auxiliary: bool = True
    ):
        super(HierarchicalSupervisionLoss, self).__init__()
        
        # 核心损失函数
        self.focal_loss = FocalLoss()
        self.iou_loss = IoULoss()
        self.dt_loss = DistanceTransformLoss()
        
        # 辅助损失函数
        self.transparency_loss = TransparencyLoss(alpha=0.5)
        self.consistency_loss = ConsistencyLoss(weight=1.0)  # 内部已有权重
        
        # 权重配置
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.dt_weight = dt_weight
        self.transparency_weight = transparency_weight if enable_auxiliary else 0.0
        self.consistency_weight = consistency_weight if enable_auxiliary else 0.0
        self.total_weight = total_weight
        self.enable_auxiliary = enable_auxiliary
        
        # 计算权重分布
        core_weight_sum = focal_weight + iou_weight + dt_weight
        aux_weight_sum = self.transparency_weight + self.consistency_weight
        
        print(f"🎯 SOTA分层监督损失配置:")
        print(f"   📊 核心损失 (主导训练):")
        print(f"      - Focal权重: {focal_weight:.3f} ({focal_weight/core_weight_sum*100:.1f}%)")
        print(f"      - IoU权重: {iou_weight:.3f} ({iou_weight/core_weight_sum*100:.1f}%)")
        print(f"      - Distance Transform权重: {dt_weight:.3f} ({dt_weight/core_weight_sum*100:.1f}%)")
        print(f"      - 核心损失总权重: {core_weight_sum:.3f}")
        
        if enable_auxiliary and aux_weight_sum > 0:
            print(f"   🔧 辅助损失 (精细协调):")
            print(f"      - 透明度权重: {self.transparency_weight:.3f}")
            print(f"      - 一致性权重: {self.consistency_weight:.3f}")
            print(f"      - 辅助损失总权重: {aux_weight_sum:.3f}")
            print(f"   📈 核心/辅助权重比: {core_weight_sum/aux_weight_sum:.1f}:1")
        else:
            print(f"   ⚠️ 辅助损失已禁用 - 纯核心损失模式")
        
        print(f"   🎛️ 总权重: {total_weight:.3f} (数值稳定控制)")
        
    def forward(
        self,
        outputs: Dict[str, torch.Tensor],
        target: torch.Tensor,
        dt_target: torch.Tensor,
        transparency_target: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算SOTA分层监督损失 - 完全无in-place操作版本
        Args:
            outputs: 模型输出字典
                - main_pred: [B, 1, H, W] 主预测
                - dt_pred: [B, 1, H, W] 距离变换预测
                - transparency_pred: [B, 1, H, W] 透明度预测 (可选)
            target: [B, 1, H, W] 二值目标掩码
            dt_target: [B, 1, H, W] 目标距离变换图
            transparency_target: [B, 1, H, W] 透明度目标 (可选，默认使用target)
        Returns:
            total_loss: 总损失
            loss_dict: 损失明细字典
        """
        # 🔧 关键修复：创建新tensor，避免in-place操作
        target_safe = target.clamp(0.0, 1.0)  # 创建新tensor
        dt_target_safe = dt_target.clamp(0.0, 1.0)  # 创建新tensor
        
        # 验证所有输出都在合理范围内 - 创建新的输出字典
        outputs_safe = {}
        for key, value in outputs.items():
            if torch.isnan(value).any() or torch.isinf(value).any():
                print(f"⚠️ 损失计算：输出{key}包含NaN/Inf，使用安全替换")
                if key == 'main_pred':
                    outputs_safe[key] = torch.full_like(value, 0.5)
                else:
                    outputs_safe[key] = torch.full_like(value, 0.1)
            else:
                # 🔧 关键修复：创建新tensor，不修改原tensor
                outputs_safe[key] = value.clamp(1e-6, 1.0-1e-6)
        
        # 🎯 核心损失计算 (主导地位) - 数值安全版本
        try:
            focal_loss = self.focal_loss(outputs_safe['main_pred'], target_safe)
            if torch.isnan(focal_loss) or torch.isinf(focal_loss):
                print("⚠️ Focal损失包含NaN/Inf，使用安全值")
                focal_loss = torch.tensor(0.5, device=target.device, requires_grad=True)
        except Exception as e:
            print(f"❌ Focal损失计算失败: {e}")
            focal_loss = torch.tensor(0.5, device=target.device, requires_grad=True)
        
        try:
            iou_loss = self.iou_loss(outputs_safe['main_pred'], target_safe)
            if torch.isnan(iou_loss) or torch.isinf(iou_loss):
                print("⚠️ IoU损失包含NaN/Inf，使用安全值")
                iou_loss = torch.tensor(0.5, device=target.device, requires_grad=True)
        except Exception as e:
            print(f"❌ IoU损失计算失败: {e}")
            iou_loss = torch.tensor(0.5, device=target.device, requires_grad=True)
        
        try:
            dt_loss = self.dt_loss(outputs_safe['dt_pred'], dt_target_safe)
            if torch.isnan(dt_loss) or torch.isinf(dt_loss):
                print("⚠️ DT损失包含NaN/Inf，使用安全值")
                dt_loss = torch.tensor(0.1, device=target.device, requires_grad=True)
        except Exception as e:
            print(f"❌ DT损失计算失败: {e}")
            dt_loss = torch.tensor(0.1, device=target.device, requires_grad=True)
        
        # 核心损失汇总 - 数值稳定性检查
        core_loss = (
            self.focal_weight * focal_loss +
            self.iou_weight * iou_loss +
            self.dt_weight * dt_loss
        )
        
        # 检查核心损失
        if torch.isnan(core_loss) or torch.isinf(core_loss):
            print("⚠️ 核心损失包含NaN/Inf，使用安全值")
            core_loss = torch.tensor(0.4, device=target.device, requires_grad=True)
        
        # 🔧 辅助损失计算 (协调作用) - 完全无in-place操作版本
        aux_loss = torch.tensor(0.0, device=target.device, requires_grad=True)
        transparency_loss_val = torch.tensor(0.0, device=target.device)
        consistency_loss_val = torch.tensor(0.0, device=target.device)
        
        if self.enable_auxiliary:
            # 透明度深度监督 - 无in-place操作
            if 'transparency_pred' in outputs_safe and self.transparency_weight > 0:
                try:
                    trans_target = transparency_target if transparency_target is not None else target_safe
                    # 🔧 创建新tensor，避免修改原tensor
                    trans_target_safe = trans_target.clamp(0.0, 1.0)
                    transparency_loss_val = self.transparency_loss(outputs_safe['transparency_pred'], trans_target_safe)
                    
                    if torch.isnan(transparency_loss_val) or torch.isinf(transparency_loss_val):
                        print("⚠️ 透明度损失包含NaN/Inf，跳过")
                        transparency_loss_val = torch.tensor(0.0, device=target.device)
                    else:
                        aux_loss = aux_loss + self.transparency_weight * transparency_loss_val  # 避免+=的in-place操作
                except Exception as e:
                    print(f"❌ 透明度损失计算失败: {e}")
                    transparency_loss_val = torch.tensor(0.0, device=target.device)
            
            # 一致性损失 - 无in-place操作
            if self.consistency_weight > 0:
                try:
                    # 🔧 将距离变换预测转换为类似的分割预测进行一致性比较
                    dt_as_seg = (outputs_safe['dt_pred'] > 0.1).float()  # 创建新tensor
                    consistency_loss_val = self.consistency_loss(outputs_safe['main_pred'], dt_as_seg)
                    
                    if torch.isnan(consistency_loss_val) or torch.isinf(consistency_loss_val):
                        print("⚠️ 一致性损失包含NaN/Inf，跳过")
                        consistency_loss_val = torch.tensor(0.0, device=target.device)
                    else:
                        aux_loss = aux_loss + self.consistency_weight * consistency_loss_val  # 避免+=的in-place操作
                except Exception as e:
                    print(f"❌ 一致性损失计算失败: {e}")
                    consistency_loss_val = torch.tensor(0.0, device=target.device)
        
        # 检查辅助损失
        if torch.isnan(aux_loss) or torch.isinf(aux_loss):
            print("⚠️ 辅助损失包含NaN/Inf，重置为0")
            aux_loss = torch.tensor(0.0, device=target.device, requires_grad=True)
        
        # 🎛️ 总损失计算 - 数值稳定性保护
        total_loss = (core_loss + aux_loss) / self.total_weight
        
        # 最终数值稳定性检查
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print(f"⚠️ 总损失包含NaN/Inf！强制使用安全损失值")
            print(f"   核心损失: focal={focal_loss.item():.6f}, iou={iou_loss.item():.6f}, dt={dt_loss.item():.6f}")
            if self.enable_auxiliary:
                trans_val = transparency_loss_val.item() if hasattr(transparency_loss_val, 'item') else transparency_loss_val
                consist_val = consistency_loss_val.item() if hasattr(consistency_loss_val, 'item') else consistency_loss_val
                print(f"   辅助损失: transparency={trans_val:.6f}, consistency={consist_val:.6f}")
            
            # 使用安全的损失值，确保训练能够继续
            total_loss = torch.tensor(0.4, device=target.device, requires_grad=True)
            print(f"   🛡️ 使用安全损失值: {total_loss.item():.6f}")
        
        # 📊 损失监控字典 - 安全转换
        def safe_item(val):
            if hasattr(val, 'item'):
                try:
                    return val.item()
                except:
                    return 0.0
            return val if isinstance(val, (int, float)) else 0.0
        
        loss_dict = {
            # 核心损失
            'focal_loss': safe_item(focal_loss),
            'iou_loss': safe_item(iou_loss),
            'dt_loss': safe_item(dt_loss),
            'core_loss': safe_item(core_loss),
            
            # 辅助损失
            'transparency_loss': safe_item(transparency_loss_val),
            'consistency_loss': safe_item(consistency_loss_val),
            'aux_loss': safe_item(aux_loss),
            
            # 总损失
            'total_loss': safe_item(total_loss)
        }
        
        return total_loss, loss_dict

# 保留原来的Stage1CombinedLoss作为备用
class Stage1CombinedLoss(nn.Module):
    """
    第一阶段组合损失函数 (备用版本)
    基于IG_SLAM项目87.95% IoU成功经验优化
    采用Focal + IoU + Edge的权重配置
    """
    def __init__(self, 
                 focal_weight=0.6,    # 基于记忆的最优权重
                 iou_weight=0.4,      # 基于记忆：避免过高导致不稳定
                 edge_weight=0.2,     # 基于记忆的边缘权重
                 dice_weight=0.7,
                 bce_weight=0.3):
        super(Stage1CombinedLoss, self).__init__()
        
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight  
        self.edge_weight = edge_weight
        
        # 主损失函数 - 基于记忆优化
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
        self.iou_loss = IoULoss()
        self.edge_loss = EdgeLoss()
        self.dice_bce_loss = DiceBCELoss(dice_weight=dice_weight, bce_weight=bce_weight)
        
        # 总权重控制
        self.total_weight = focal_weight + iou_weight + edge_weight
        print(f"📊 第一阶段损失权重配置 (基于IG_SLAM优化经验):")
        print(f"  - Focal损失权重: {focal_weight}")
        print(f"  - IoU损失权重: {iou_weight}")  
        print(f"  - 边缘损失权重: {edge_weight}")
        print(f"  - 总权重: {self.total_weight} (避免过高导致数值不稳定)")
    
    def forward(self, outputs, gt_mask):
        """
        计算组合损失
        Args:
            outputs: 模型输出字典，包含main_pred_logits等
            gt_mask: 真值掩码 [B, 1, H, W]
        Returns:
            total_loss: 总损失
            loss_dict: 损失字典，用于监控
        """
        losses = {}
        
        # 获取主预测logits
        main_pred_logits = outputs['main_pred_logits']
        
        # Focal损失 - 基于记忆的核心损失
        focal_loss = self.focal_loss(main_pred_logits, gt_mask)
        losses['focal_loss'] = focal_loss
        
        # IoU损失 - 基于记忆的重要损失
        iou_loss = self.iou_loss(main_pred_logits, gt_mask)
        losses['iou_loss'] = iou_loss
        
        # 边缘损失 - 基于记忆优化玻璃边缘检测
        if 'edge_pred_logits' in outputs:
            edge_loss = self.edge_loss(outputs['edge_pred_logits'], gt_mask)
        else:
            edge_loss = self.edge_loss(main_pred_logits, gt_mask)
        losses['edge_loss'] = edge_loss
        
        # 辅助Dice+BCE损失 (权重较小)
        dice_bce_loss = self.dice_bce_loss(main_pred_logits, gt_mask)
        losses['dice_bce_loss'] = dice_bce_loss
        
        # 计算总损失 - 基于记忆的权重配置
        total_loss = (self.focal_weight * focal_loss + 
                     self.iou_weight * iou_loss + 
                     self.edge_weight * edge_loss +
                     0.1 * dice_bce_loss)  # Dice+BCE作为辅助损失
        
        losses['total_loss'] = total_loss
        
        # 数值稳定性检查 - 基于记忆的关键保护
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print(f"⚠️ 检测到数值异常！")
            print(f"  - Focal Loss: {focal_loss.item():.6f}")
            print(f"  - IoU Loss: {iou_loss.item():.6f}")
            print(f"  - Edge Loss: {edge_loss.item():.6f}")
            print(f"  - Total Loss: {total_loss.item():.6f}")
            # 返回安全的损失值
            total_loss = torch.tensor(1.0, device=total_loss.device, requires_grad=True)
        
        return total_loss, losses

class DiffusionLoss(nn.Module):
    """
    扩散模型损失函数
    主要用于第二阶段的噪声预测
    """
    def __init__(self, noise_weight=1.0, consistency_weight=0.1):
        super(DiffusionLoss, self).__init__()
        self.noise_weight = noise_weight
        self.consistency_weight = consistency_weight
        
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()
        
    def forward(self, predicted_noise, target_noise, 
                refined_mask=None, coarse_mask=None):
        """
        计算扩散损失
        Args:
            predicted_noise: 预测的噪声 [B, 1, H, W]
            target_noise: 目标噪声 [B, 1, H, W]
            refined_mask: 精炼后的掩码（可选）
            coarse_mask: 粗糙掩码（可选）
        """
        losses = {}
        
        # 主噪声预测损失
        noise_loss = self.mse_loss(predicted_noise, target_noise)
        losses['noise_loss'] = noise_loss
        
        total_loss = self.noise_weight * noise_loss
        
        # 一致性损失（如果提供了精炼和粗糙掩码）
        if refined_mask is not None and coarse_mask is not None:
            consistency_loss = self.l1_loss(refined_mask, coarse_mask)
            losses['consistency_loss'] = consistency_loss
            total_loss += self.consistency_weight * consistency_loss
        
        losses['total_loss'] = total_loss
        return total_loss, losses

class PerceptualLoss(nn.Module):
    """
    感知损失，用于保持语义特征
    """
    def __init__(self, feature_layers=[3, 8, 15, 22]):
        super(PerceptualLoss, self).__init__()
        
        # 使用预训练的VGG网络提取特征
        import torchvision.models as models
        vgg = models.vgg16(pretrained=True).features
        
        self.feature_extractors = nn.ModuleList()
        for i, layer_idx in enumerate(feature_layers):
            self.feature_extractors.append(vgg[:layer_idx+1])
            
        # 冻结VGG参数
        for param in self.parameters():
            param.requires_grad = False
            
        self.mse_loss = nn.MSELoss()
        
    def forward(self, pred, target):
        """
        计算感知损失
        Args:
            pred: 预测图像 [B, 3, H, W]
            target: 目标图像 [B, 3, H, W]
        """
        total_loss = 0
        
        for extractor in self.feature_extractors:
            pred_features = extractor(pred)
            target_features = extractor(target)
            total_loss += self.mse_loss(pred_features, target_features)
            
        return total_loss / len(self.feature_extractors)

class AdversarialLoss(nn.Module):
    """
    对抗损失，用于提高精炼质量
    """
    def __init__(self, loss_type='lsgan'):
        super(AdversarialLoss, self).__init__()
        self.loss_type = loss_type
        
        if loss_type == 'lsgan':
            self.criterion = nn.MSELoss()
        elif loss_type == 'vanilla':
            self.criterion = nn.BCEWithLogitsLoss()
        else:
            raise NotImplementedError(f'不支持的对抗损失类型: {loss_type}')
    
    def forward(self, discriminator_output, is_real):
        """
        计算对抗损失
        Args:
            discriminator_output: 判别器输出
            is_real: 是否为真实样本
        """
        if self.loss_type == 'lsgan':
            if is_real:
                target = torch.ones_like(discriminator_output)
            else:
                target = torch.zeros_like(discriminator_output)
            return self.criterion(discriminator_output, target)
        elif self.loss_type == 'vanilla':
            if is_real:
                return self.criterion(discriminator_output, torch.ones_like(discriminator_output))
            else:
                return self.criterion(discriminator_output, torch.zeros_like(discriminator_output))

class TotalVariationLoss(nn.Module):
    """
    总变分损失，用于平滑输出
    """
    def __init__(self, weight=1.0):
        super(TotalVariationLoss, self).__init__()
        self.weight = weight
    
    def forward(self, x):
        """
        计算总变分损失
        Args:
            x: 输入张量 [B, C, H, W]
        """
        batch_size, channels, height, width = x.size()
        
        # 计算水平和垂直方向的变分
        tv_h = torch.pow(x[:, :, 1:, :] - x[:, :, :-1, :], 2).sum()
        tv_w = torch.pow(x[:, :, :, 1:] - x[:, :, :, :-1], 2).sum()
        
        return self.weight * (tv_h + tv_w) / (batch_size * channels * height * width)

# 创建损失函数的工厂函数
def create_hierarchical_loss(simple_mode=False, debug=False, enable_auxiliary=False):
    """
    创建SOTA级分层深度监督损失函数 - 匹配优化后的ProteusGlassNet架构
    
    Args:
        simple_mode (bool): 简化模式，只保留核心损失，适用于：
                           - 训练初期稳定化
                           - 调试和问题定位  
                           - 快速验证基础架构
        debug (bool): 调试模式，打印详细的损失计算信息
        enable_auxiliary (bool): 是否启用辅助损失（透明度+一致性）
                                默认False，避免混合精度兼容性问题
    
    Returns:
        HierarchicalSupervisionLoss: 损失函数实例
    """
    if simple_mode:
        # 简化模式：仅核心损失，用于训练初期稳定化
        return HierarchicalSupervisionLoss(
            focal_weight=0.6,
            iou_weight=0.4, 
            dt_weight=0.2,
            transparency_weight=0.0,      # 完全禁用
            consistency_weight=0.0,       # 完全禁用
            total_weight=1.2,
            enable_auxiliary=False        # 强制禁用
        )
    elif enable_auxiliary:
        # 完整模式：核心损失 + 辅助损失 (SOTA性能)
        print("⚠️ 启用辅助损失模式，请确保混合精度已关闭")
        return HierarchicalSupervisionLoss(
            focal_weight=0.6,
            iou_weight=0.4,
            dt_weight=0.2,
            transparency_weight=0.1,      # 透明度深度监督
            consistency_weight=0.05,      # 一致性损失
            total_weight=1.35,
            enable_auxiliary=True
        )
    else:
        # 🔧 默认模式：仅启用核心损失，确保稳定性
        print("🔧 核心损失模式：focal + iou + dt，跳过辅助损失")
        return HierarchicalSupervisionLoss(
            focal_weight=0.6,
            iou_weight=0.4,
            dt_weight=0.2,
            transparency_weight=0.0,      # 暂时禁用
            consistency_weight=0.0,       # 暂时禁用  
            total_weight=1.2,
            enable_auxiliary=False        # 明确禁用
        )

def create_stage1_loss():
    """创建第一阶段的损失函数，IoU权重提升到0.7，其他按比例缩放"""
    return HierarchicalSupervisionLoss(
        focal_weight=0.410,   # 0.6*0.684
        iou_weight=0.7,       # 提升
        dt_weight=0.137,      # 0.2*0.684
        transparency_weight=0.068,  # 0.1*0.684
        consistency_weight=0.034,   # 0.05*0.684
        total_weight=1.35,
        enable_auxiliary=True
    )

def create_stage2_loss():
    """创建第二阶段损失函数"""
    return DiffusionLoss(
        noise_weight=config.STAGE2_NOISE_WEIGHT,
        consistency_weight=config.STAGE2_CONSISTENCY_WEIGHT
    )

# 导出所有损失函数
__all__ = [
    'FocalLoss',
    'DiceLoss', 
    'DiceBCELoss',
    'IoULoss',
    'EdgeLoss',
    'TransparencyLoss',
    'HierarchicalSupervisionLoss',  # 新增：分层深度监督损失
    'Stage1CombinedLoss',          # 备用版本
    'DiffusionLoss',
    'PerceptualLoss',
    'AdversarialLoss',
    'TotalVariationLoss',
    'create_hierarchical_loss',    # 新增：创建分层损失的工厂函数
    'create_stage1_loss',          # 备用版本
    'create_stage2_loss'
] 

"""
使用示例：分层深度监督损失函数

# ===============================
# 基本使用 - 完整分层监督
# ===============================
# 创建完整分层监督损失函数
criterion = create_hierarchical_loss()

# 模型前向传播（假设已有ProteusGlassNet模型）
outputs = model(images)
# outputs包含：
# - 'main_pred_logits': 主预测logits
# - 'edge_pred_logits': P2层边缘预测logits  
# - 'transparency_pred': 深层透明度预测
# - 其他辅助输出...

# 计算损失
total_loss, loss_dict = criterion(outputs, gt_masks)

# 监控各层损失
print(f"主预测监督: {loss_dict['focal_loss']:.4f}")
print(f"边缘监督: {loss_dict['dt_loss']:.4f}")
print(f"总损失: {loss_dict['total_loss']:.4f}")

# ===============================
# 简化模式 - 训练初期稳定化
# ===============================
# 创建简化模式损失函数（只保留主预测监督）
criterion_simple = create_hierarchical_loss(simple_mode=True)

# 计算简化损失（辅助损失权重全部为0）
total_loss, loss_dict = criterion_simple(outputs, gt_masks)
print(f"简化模式总损失: {loss_dict['total_loss']:.4f} (仅主预测监督)")

# ===============================
# 调试模式 - 详细诊断信息
# ===============================
# 创建调试模式损失函数
criterion_debug = create_hierarchical_loss(debug=True)
total_loss, loss_dict = criterion_debug(outputs, gt_masks)
# 会自动打印详细的损失计算信息

# ===============================
# 组合模式 - 简化+调试
# ===============================
# 训练初期使用简化+调试模式
criterion_safe = create_hierarchical_loss(simple_mode=True, debug=True)

# 反向传播
total_loss.backward()
optimizer.step()

分层监督的关键优势：
1. P2层边缘监督：专注于低级细节的精确检测
2. 深层透明度监督：利用高级语义信息
3. 层间一致性：确保不同层次预测的协调性
4. 权重平衡：避免某一层监督过度主导训练过程
5. 数值稳定：内置异常检测和保护机制
6. 简化模式：训练初期稳定化，避免辅助损失干扰主干学习
7. 调试模式：详细的损失计算信息，便于问题诊断

训练策略建议：
- 前5-10个epoch：使用simple_mode=True，确保主干网络稳定学习
- 中期训练：切换到完整分层监督，充分利用多层次指导
- 调试问题：启用debug=True，获取详细诊断信息

这种设计完美匹配了优化后的ProteusGlassNet分层深度监督架构！
"""