import torch
import torch.nn as nn

class FPNDecoder(nn.Module):
    """
    轻量级特征金字塔解码器（FPN），输入4个尺度特征，输出融合后的[P2, P3, P4, P5]。
    - 所有横向连接均为元素级相加
    - 每层可选1x1卷积调整通道数
    """
    def __init__(self, in_channels_list, out_channels=128):
        super().__init__()
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        # 1x1卷积调整通道数
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(in_ch, out_channels, kernel_size=1)
            for in_ch in in_channels_list
        ])
        # 3x3卷积平滑
        self.smooth_convs = nn.ModuleList([
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
            for _ in in_channels_list
        ])

    def forward(self, features):
        """
        features: [C2, C3, C4, C5]，分辨率从高到低
        返回: [P2, P3, P4, P5]，分辨率从高到低
        """
        # 横向1x1卷积
        feats = [l_conv(f) for l_conv, f in zip(self.lateral_convs, features)]
        # 自顶向下融合
        num_levels = len(feats)
        for i in range(num_levels-1, 0, -1):
            upsample = nn.functional.interpolate(feats[i], size=feats[i-1].shape[2:], mode='nearest')
            feats[i-1] = feats[i-1] + upsample
        # 3x3卷积平滑
        outs = [smooth(f) for smooth, f in zip(self.smooth_convs, feats)]
        return outs  # [P2, P3, P4, P5] 