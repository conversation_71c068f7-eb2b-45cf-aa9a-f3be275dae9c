import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 添加InceptionNeXt路径并导入
sys.path.insert(0, '/home/<USER>/ws/IG_SLAM/inceptionnext')
try:
    import models.inceptionnext as inception_models
    INCEPTIONNEXT_AVAILABLE = True
    print("✅ InceptionNeXt models imported successfully")
except ImportError as e:
    print(f"❌ Warning: InceptionNeXt not available: {e}")
    INCEPTIONNEXT_AVAILABLE = False

from ig_glass.attention_scsa import SCSA
from .diff_crf import SimplifiedDiffCRF

# 1. FeatureExtractor
class FeatureExtractor(nn.Module):
    """
    多尺度特征提取器，支持InceptionNeXt-T/Small/Base，预留Swin、MobileViT接口。
    forward输出4个尺度特征(C2, C3, C4, C5)。
    """
    def __init__(self, backbone_type='inceptionnext_tiny', pretrained=False):
        super().__init__()
        self.backbone_type = backbone_type.lower()
        
        if not INCEPTIONNEXT_AVAILABLE:
            raise ImportError("InceptionNeXt models not available!")
        
        if 'inceptionnext' in self.backbone_type:
            if 'tiny' in self.backbone_type:
                self.backbone = inception_models.inceptionnext_tiny(pretrained=pretrained)
            elif 'small' in self.backbone_type:
                self.backbone = inception_models.inceptionnext_small(pretrained=pretrained)
            elif 'base' in self.backbone_type:
                self.backbone = inception_models.inceptionnext_base(pretrained=pretrained)
            else:
                raise ValueError(f"Unknown InceptionNeXt backbone: {backbone_type}")
        else:
            raise ValueError(f"Unsupported backbone: {backbone_type}")

    def forward(self, x):
        feats = []
        x = self.backbone.stem(x)   # (B, C, H/4, W/4)
        x = self.backbone.stages[0](x)  # (B, C2, H/4, W/4)
        feats.append(x)
        x = self.backbone.stages[1](x)  # (B, C3, H/8, W/8)
        feats.append(x)
        x = self.backbone.stages[2](x)  # (B, C4, H/16, W/16)
        feats.append(x)
        x = self.backbone.stages[3](x)  # (B, C5, H/32, W/32)
        feats.append(x)
        return feats  # [C2, C3, C4, C5]

    @property
    def out_channels(self):
        if 'inceptionnext' in self.backbone_type:
            if 'tiny' in self.backbone_type or 'small' in self.backbone_type:
                return [96, 192, 384, 768]
            elif 'base' in self.backbone_type:
                return [128, 256, 512, 1024]
        else:
            raise ValueError(f"Unsupported backbone: {self.backbone_type}")

# 2. FPNDecoder
class FPNDecoder(nn.Module):
    """
    轻量级特征金字塔解码器（FPN），输入4个尺度特征，输出融合后的[P2, P3, P4, P5]。
    - 所有横向连接均为元素级相加
    - 每层可选1x1卷积调整通道数
    """
    def __init__(self, in_channels_list, out_channels=128):
        super().__init__()
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(in_ch, out_channels, kernel_size=1)
            for in_ch in in_channels_list
        ])
        self.smooth_convs = nn.ModuleList([
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
            for _ in in_channels_list
        ])

    def forward(self, features):
        feats = [l_conv(f) for l_conv, f in zip(self.lateral_convs, features)]
        num_levels = len(feats)
        for i in range(num_levels-1, 0, -1):
            upsample = nn.functional.interpolate(feats[i], size=feats[i-1].shape[2:], mode='nearest')
            feats[i-1] = feats[i-1] + upsample
        outs = [smooth(f) for smooth, f in zip(self.smooth_convs, feats)]
        return outs  # [P2, P3, P4, P5]

# 3. LightEdgeEnhancer（用您原本的高精度实现）
class LightEdgeEnhancer(nn.Module):
    """Lightweight module to enhance edge features (from ig_glass/gdnet_scsa.py)"""
    def __init__(self, in_channels):
        super(LightEdgeEnhancer, self).__init__()
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(inplace=True)
        )
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))

    def forward(self, x):
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        enhanced = self.edge_conv(x)
        return x + enhanced * edge_features

# 4. PredictionHead（LeakyReLU版本）
class PredictionHead(nn.Module):
    """
    双分支预测头：主预测 + DT预测
    """
    def __init__(self, in_channels):
        super().__init__()
        # 共享特征提取
        self.shared_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.LeakyReLU(inplace=True)
        )
        
        # 主预测分支
        self.main_branch = nn.Conv2d(in_channels, 1, 1)
        
        # DT预测分支
        self.dt_branch = nn.Conv2d(in_channels, 1, 1)
        
    def forward(self, x):
        shared_feat = self.shared_conv(x)
        main_logits = self.main_branch(shared_feat)
        dt_logits = self.dt_branch(shared_feat)
        return main_logits, dt_logits

# SCSA包装器，支持通道数调整
class SCSAWithProj(nn.Module):
    def __init__(self, in_channels, out_channels, **kwargs):
        super().__init__()
        self.scsa = SCSA(dim=in_channels, **kwargs)
        self.proj = nn.Conv2d(in_channels, out_channels, 1)
    def forward(self, x):
        x = self.scsa(x)
        x = self.proj(x)
        return x

# 5. RTGlassNet主干
class RTGlassNet(nn.Module):
    """
    实时玻璃分割终极架构：Backbone + FPN + EdgeEnhancer + SCSA + diffCRF
    支持主预测和DT预测的双分支输出
    """
    def __init__(self, backbone_type='inceptionnext_tiny', fpn_out=128, scsa_reduction=8, crf_iter=3, crf_bilateral_weight=5.0):
        super().__init__()
        self.feature_extractor = FeatureExtractor(backbone_type=backbone_type)
        in_channels_list = self.feature_extractor.out_channels
        self.fpn_decoder = FPNDecoder(in_channels_list, out_channels=fpn_out)
        self.main_conv = nn.Sequential(
            nn.Conv2d(fpn_out, fpn_out, 3, padding=1),
            nn.BatchNorm2d(fpn_out),
            nn.LeakyReLU(inplace=True)
        )
        self.edge_enhancer = LightEdgeEnhancer(fpn_out)
        # 用SCSAWithProj替换原SCSA，支持通道降维
        self.scsa = SCSAWithProj(in_channels=fpn_out*2, out_channels=fpn_out, head_num=8)
        self.pred_head = PredictionHead(fpn_out)
        self.diff_crf = SimplifiedDiffCRF(n_iter=crf_iter, bilateral_weight=crf_bilateral_weight)

    def forward(self, x):
        """
        前向传播
        Returns:
            dict: {
                'main_pred': 主预测结果 [B, 1, H, W] sigmoid后
                'dt_pred': DT预测结果 [B, 1, H, W] sigmoid后
                'main_logits': 主预测logits [B, 1, H, W] 
                'dt_logits': DT预测logits [B, 1, H, W]
                'refined_pred': CRF后处理结果 [B, 1, H, W]
            }
        """
        input_size = x.shape[2:]
        
        # 特征提取和融合
        c2, c3, c4, c5 = self.feature_extractor(x)
        p2, p3, p4, p5 = self.fpn_decoder([c2, c3, c4, c5])
        
        # 边缘增强的王牌流程
        main_features = self.main_conv(p2)
        edge_features = self.edge_enhancer(p2)  # 🌟 LightEdgeEnhancer登场
        
        # SCSA融合
        fused = self.scsa(torch.cat([main_features, edge_features], dim=1))
        
        # 双分支预测
        main_logits, dt_logits = self.pred_head(fused)
        
        # 上采样到原始尺寸
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        dt_logits = F.interpolate(dt_logits, size=input_size, mode='bilinear', align_corners=True)
        
        # 激活函数
        main_pred = torch.sigmoid(main_logits)
        dt_pred = torch.sigmoid(dt_logits)
        
        # CRF后处理（仅对主预测）
        refined_pred = self.diff_crf(main_logits, x)
        
        return {
            'main_pred': main_pred,
            'dt_pred': dt_pred,
            'main_logits': main_logits,
            'dt_logits': dt_logits,
            'refined_pred': refined_pred
        } 