# train_tgd.py
# -----------------------------------------------------------------------------
# TransXGlassNet 训练脚本
# -----------------------------------------------------------------------------
import os
import sys
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.cuda.amp import autocast, GradScaler
from tqdm import tqdm
import argparse

# 只保留当前目录，去除Proteus和ig_glass路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config_transxnet as config
from glass_dataloader import create_glass_dataloaders
from models import TransXGlassNet
from utils.loss_tgd import create_tgd_loss
from utils.metrics import SegmentationMetrics
from utils.utils import (
    create_optimizer, create_scheduler,
    adjust_learning_rate, count_parameters, setup_seed,
    format_time, create_experiment_dir, Timer
)

class TGDTrainer:
    def __init__(self, config, arch='tiny', backbone_path=None):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.train_loader, self.val_loader = create_glass_dataloaders(
            data_dir=config.PROJECT_ROOT,
            batch_size=config.STAGE1_BATCH_SIZE,
            target_size=config.STAGE1_IMG_SIZE,
            split_ratio=config.GLASS_DATALOADER['split_ratio'],
            random_seed=config.GLASS_DATALOADER['random_seed'],
            num_workers=config.NUM_WORKERS,
            glass_aug_config=config.GLASS_DATALOADER['glass_augmentation']
        )
        print("🔄 创建TransXGlassNet模型...")
        # 检查权重文件后缀，避免加载data.pkl等非PyTorch权重
        if backbone_path is not None and not backbone_path.endswith(('.pth', '.pt', '.bin')):
            raise ValueError(f"权重文件必须为.pth/.pt/.bin格式，当前为: {backbone_path}")
        self.model = TransXGlassNet(
            arch=arch,
            backbone_path=backbone_path,
            crf_iter=config.CRF_ITER,
            trainable_crf=config.CRF_TRAINABLE
        ).to(self.device)
        print("📊 模型参数统计:")
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"  - 总参数数量: {total_params:,} ({total_params/1e6:.2f}M)")
        print(f"  - 可训练参数: {trainable_params:,} ({trainable_params/1e6:.2f}M)")
        print("🎯 使用TGD损失函数...")
        self.criterion = create_tgd_loss()
        self.optimizer = create_optimizer(
            self.model,
            optimizer_type='ADAMW',
            lr=config.STAGE1_LR,
            weight_decay=config.STAGE1_WEIGHT_DECAY,
            vit_lr_ratio=0.1
        )
        self.scheduler = create_scheduler(
            self.optimizer,
            scheduler_type=config.SCHEDULER_TYPE,
            epochs=config.STAGE1_EPOCHS,
            step_size=config.STEP_SCHEDULER_STEP_SIZE,
            gamma=config.STEP_SCHEDULER_GAMMA
        )
        self.scaler = GradScaler(enabled=config.MIXED_PRECISION)
        self.metrics = SegmentationMetrics()
        self.exp_dir = create_experiment_dir(config.OUTPUT_DIR)
        self.checkpoint_dir = os.path.join(self.exp_dir, 'checkpoints')
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        self.start_epoch = 0
        self.best_iou = 0.0
        self.best_epoch = 0
    def train_epoch(self, epoch):
        self.model.train()
        total_loss = 0
        for batch in tqdm(self.train_loader, desc=f"Train Epoch {epoch}"):
            imgs, masks = batch['image'].to(self.device), batch['mask'].to(self.device)
            self.optimizer.zero_grad()
            with autocast(enabled=self.config.MIXED_PRECISION):
                outputs = self.model(imgs)
                loss, loss_dict = self.criterion(outputs, masks)
            self.scaler.scale(loss).backward()
            self.scaler.step(self.optimizer)
            self.scaler.update()
            total_loss += loss.item()
        avg_loss = total_loss / len(self.train_loader)
        print(f"Train Epoch {epoch} Loss: {avg_loss:.4f}")
        return avg_loss
    def validate_epoch(self, epoch):
        self.model.eval()
        total_iou = 0
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc=f"Val Epoch {epoch}"):
                imgs, masks = batch['image'].to(self.device), batch['mask'].to(self.device)
                outputs = self.model(imgs)
                preds = outputs['main_pred']
                iou = self.metrics.iou_score(preds, masks)
                total_iou += iou
        avg_iou = total_iou / len(self.val_loader)
        print(f"Val Epoch {epoch} IoU: {avg_iou:.4f}")
        return avg_iou
    def train(self):
        for epoch in range(self.start_epoch, self.config.STAGE1_EPOCHS):
            self.train_epoch(epoch)
            iou = self.validate_epoch(epoch)
            self.scheduler.step()
            if iou > self.best_iou:
                self.best_iou = iou
                self.best_epoch = epoch
                torch.save(self.model.state_dict(), os.path.join(self.checkpoint_dir, f"tgd_best_model.pth"))
                print(f"✅ 新最佳IoU: {iou:.4f}，已保存模型")
        print(f"训练结束，最佳IoU: {self.best_iou:.4f} (Epoch {self.best_epoch})")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--arch', type=str, default='tiny', help='TransXNet架构(tiny/small/base)')
    parser.add_argument('--backbone_path', type=str, default=config.TRANSXNET_PRETRAINED_PATH, help='TransXNet预训练权重路径')
    args = parser.parse_args()
    trainer = TGDTrainer(config, arch=args.arch, backbone_path=args.backbone_path)
    trainer.train()

if __name__ == '__main__':
    main() 