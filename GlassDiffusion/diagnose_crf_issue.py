#!/usr/bin/env python3
"""
🔍 深度诊断CRF问题
既然CRF是可学习的，那么问题可能在：
1. CRF参数学习不当
2. CRF输出格式问题
3. 数值稳定性问题
4. 预处理不一致
"""

import os
import warnings
import cv2
import numpy as np
import torch
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',
    'scale': 384,
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理
img_transform = A<PERSON>([
    <PERSON><PERSON>(height=args['scale'], width=args['scale']),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ToTensorV2()
])

def analyze_crf_parameters(model):
    """分析CRF参数"""
    print("🔧 分析CRF参数:")
    
    crf = model.diff_crf
    print(f"  CRF类型: {type(crf).__name__}")
    print(f"  迭代次数: {crf.n_iter}")
    
    # 检查参数值
    params = {
        'bilateral_weight': getattr(crf, 'bilateral_weight', None),
        'gaussian_weight': getattr(crf, 'gaussian_weight', None),
        'bilateral_spatial_sigma': getattr(crf, 'bilateral_spatial_sigma', None),
        'bilateral_color_sigma': getattr(crf, 'bilateral_color_sigma', None),
        'gaussian_sigma': getattr(crf, 'gaussian_sigma', None)
    }
    
    for name, param in params.items():
        if param is not None:
            if isinstance(param, torch.nn.Parameter):
                value = param.data.item()
                grad_info = f", grad={'Yes' if param.grad is not None else 'No'}"
            else:
                value = param.item() if hasattr(param, 'item') else param
                grad_info = ""
            
            print(f"  {name}: {value:.4f}{grad_info}")
            
            # 检查参数是否合理
            if name == 'bilateral_weight' and (value < 0.1 or value > 50):
                print(f"    ⚠️ bilateral_weight可能不合理: {value}")
            elif name == 'gaussian_weight' and (value < 0.1 or value > 20):
                print(f"    ⚠️ gaussian_weight可能不合理: {value}")
            elif 'sigma' in name and (value < 0.5 or value > 100):
                print(f"    ⚠️ {name}可能不合理: {value}")

def deep_analyze_outputs(outputs, image_file):
    """深度分析模型输出"""
    print(f"\n🔬 深度分析 {image_file}:")
    
    main_pred = outputs['main_pred']
    refined_pred = outputs.get('refined_pred')
    main_logits = outputs.get('main_logits')
    
    # 分析main_pred
    print(f"  main_pred:")
    print(f"    shape: {main_pred.shape}")
    print(f"    dtype: {main_pred.dtype}")
    print(f"    range: [{main_pred.min():.6f}, {main_pred.max():.6f}]")
    print(f"    mean: {main_pred.mean():.6f}")
    print(f"    std: {main_pred.std():.6f}")
    
    # 分析refined_pred
    if refined_pred is not None:
        print(f"  refined_pred:")
        print(f"    shape: {refined_pred.shape}")
        print(f"    dtype: {refined_pred.dtype}")
        print(f"    range: [{refined_pred.min():.6f}, {refined_pred.max():.6f}]")
        print(f"    mean: {refined_pred.mean():.6f}")
        print(f"    std: {refined_pred.std():.6f}")
        
        # 检查refined_pred的通道数
        if refined_pred.shape[1] == 2:
            print(f"    ⚠️ refined_pred有2个通道，可能需要选择前景通道")
            fg_channel = refined_pred[:, 1:2, :, :]  # 前景通道
            print(f"    前景通道 range: [{fg_channel.min():.6f}, {fg_channel.max():.6f}]")
            print(f"    前景通道 mean: {fg_channel.mean():.6f}")
        elif refined_pred.shape[1] == 1:
            print(f"    ✅ refined_pred是单通道")
        else:
            print(f"    ❌ refined_pred通道数异常: {refined_pred.shape[1]}")
        
        # 检查数值异常
        if torch.isnan(refined_pred).any():
            print(f"    ❌ refined_pred包含NaN")
        if torch.isinf(refined_pred).any():
            print(f"    ❌ refined_pred包含Inf")
        
        # 检查是否为常数
        if refined_pred.max() - refined_pred.min() < 1e-6:
            print(f"    ❌ refined_pred几乎为常数")
        
        # 对比main_pred和refined_pred
        if refined_pred.shape == main_pred.shape:
            diff = torch.abs(refined_pred - main_pred).mean()
            print(f"    与main_pred的平均差异: {diff:.6f}")
            
            if diff < 1e-5:
                print(f"    ⚠️ refined_pred与main_pred几乎相同，CRF可能没有起作用")
        else:
            print(f"    ⚠️ refined_pred与main_pred形状不匹配")
    else:
        print(f"  ❌ refined_pred为None")

def test_crf_with_different_inputs(model):
    """测试CRF对不同输入的响应"""
    print(f"\n🧪 测试CRF响应:")
    
    # 创建测试输入
    test_logits = torch.randn(1, 1, 64, 64).cuda()
    test_image = torch.rand(1, 3, 64, 64).cuda()
    
    print(f"  测试输入 logits: {test_logits.shape}, range=[{test_logits.min():.3f}, {test_logits.max():.3f}]")
    print(f"  测试输入 image: {test_image.shape}, range=[{test_image.min():.3f}, {test_image.max():.3f}]")
    
    # 测试CRF
    with torch.no_grad():
        crf_output = model.diff_crf(test_logits, test_image)
        
    print(f"  CRF输出: {crf_output.shape}, range=[{crf_output.min():.6f}, {crf_output.max():.6f}]")
    print(f"  CRF输出 mean: {crf_output.mean():.6f}")
    
    # 检查CRF是否改变了输入
    input_sigmoid = torch.sigmoid(test_logits)
    diff = torch.abs(crf_output - input_sigmoid).mean()
    print(f"  与sigmoid(logits)的差异: {diff:.6f}")
    
    if diff < 1e-5:
        print(f"    ⚠️ CRF输出与sigmoid(logits)几乎相同，CRF可能失效")

def compare_training_vs_testing_pipeline(model, image_path, gt_path):
    """对比训练和测试管道"""
    print(f"\n🔄 对比训练vs测试管道:")
    
    # 读取图像和GT
    img_rgb = cv2.imread(image_path)
    img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
    gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
    
    # 预处理
    transformed = img_transform(image=img_rgb)
    img_tensor = transformed['image'].unsqueeze(0).cuda()
    
    # 处理GT（模拟训练时的处理）
    gt_resized = cv2.resize(gt_mask, (args['scale'], args['scale']))
    gt_tensor = torch.from_numpy(gt_resized / 255.0).float().unsqueeze(0).unsqueeze(0).cuda()
    
    print(f"  输入图像: {img_tensor.shape}, range=[{img_tensor.min():.3f}, {img_tensor.max():.3f}]")
    print(f"  GT: {gt_tensor.shape}, range=[{gt_tensor.min():.3f}, {gt_tensor.max():.3f}]")
    
    # 模型推理
    with torch.no_grad():
        outputs = model(img_tensor)
    
    # 模拟训练时的IoU计算（使用main_pred）
    main_pred = outputs['main_pred']
    main_binary = (main_pred > 0.5).float()
    main_intersection = (main_binary * gt_tensor).sum()
    main_union = main_binary.sum() + gt_tensor.sum() - main_intersection
    main_iou = (main_intersection / (main_union + 1e-7)).item()
    
    print(f"  训练时IoU计算 (main_pred): {main_iou:.4f}")
    
    # 模拟测试时的IoU计算（使用refined_pred）
    refined_pred = outputs.get('refined_pred')
    if refined_pred is not None:
        # 处理多通道情况
        if refined_pred.shape[1] == 2:
            refined_pred = refined_pred[:, 1:2, :, :]  # 取前景通道
        
        refined_binary = (refined_pred > 0.5).float()
        refined_intersection = (refined_binary * gt_tensor).sum()
        refined_union = refined_binary.sum() + gt_tensor.sum() - refined_intersection
        refined_iou = (refined_intersection / (refined_union + 1e-7)).item()
        
        print(f"  测试时IoU计算 (refined_pred): {refined_iou:.4f}")
        print(f"  IoU差异: {refined_iou - main_iou:.4f}")
        
        if abs(refined_iou - main_iou) > 0.1:
            print(f"    ⚠️ 训练和测试IoU差异较大")
    else:
        print(f"  ❌ 无法计算测试时IoU，refined_pred为None")

def main():
    print("🔍 深度诊断CRF问题")
    print("=" * 60)
    
    # 加载模型
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"✅ 加载模型: {model_path}")
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    model = RTGlassNet(
        backbone_type=backbone_type, 
        crf_iter=args['crf_iter'], 
        crf_bilateral_weight=args['crf_bilateral_weight']
    )
    
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    model.cuda(device_ids[0])
    model.eval()
    
    # 分析CRF参数
    analyze_crf_parameters(model)
    
    # 测试CRF响应
    test_crf_with_different_inputs(model)
    
    # 找一张测试图像
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    image_folder = os.path.join(data_path, "image")
    gt_folder = os.path.join(data_path, "mask")
    
    if os.path.exists(image_folder) and os.path.exists(gt_folder):
        image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        if image_files:
            test_file = image_files[0]
            image_path = os.path.join(image_folder, test_file)
            gt_path = os.path.join(gt_folder, os.path.splitext(test_file)[0] + '.png')
            
            if os.path.exists(gt_path):
                # 深度分析输出
                img_rgb = cv2.imread(image_path)
                img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
                transformed = img_transform(image=img_rgb)
                img_tensor = transformed['image'].unsqueeze(0).cuda()
                
                with torch.no_grad():
                    outputs = model(img_tensor)
                
                deep_analyze_outputs(outputs, test_file)
                
                # 对比训练vs测试管道
                compare_training_vs_testing_pipeline(model, image_path, gt_path)
    
    print(f"\n💡 可能的问题和解决方案:")
    print(f"1. 如果refined_pred有2个通道，需要选择前景通道")
    print(f"2. 如果CRF参数不合理，需要重新调优")
    print(f"3. 如果CRF没有改变输入，检查CRF实现")
    print(f"4. 如果训练和测试IoU差异大，统一使用refined_pred")

if __name__ == '__main__':
    main()
