import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import sys
import os

# 添加路径
sys.path.append('GlassDiffusion/models')
sys.path.append('ig_glass')

from GlassDiffusion.models.RTGlassNetv2 import RTGlassNetv2
from GlassDiffusion.models.loss_rtgv2 import RTGlassNetv2Loss
from GlassDiffusion.glass_dataloader import GlassDataset

def train_rtgv2():
    """RTGlassNetv2训练示例"""
    print("🚀 开始RTGlassNetv2训练...")
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"📱 使用设备: {device}")
    
    # 模型配置
    model = RTGlassNetv2(
        backbone_type='inceptionnext_tiny',
        crf_iter=3,
        crf_bilateral_weight=10.0
    ).to(device)
    
    # 损失函数配置
    criterion = RTGlassNetv2Loss(
        focal_weight=0.6,
        iou_weight=0.4,
        edge_weight=0.2,
        crf_weight=0.1,
        consistency_weight=0.1
    ).to(device)
    
    # 优化器配置
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100)
    
    # 数据加载器配置（示例）
    # 注意：这里需要根据实际的数据路径进行调整
    try:
        train_dataset = GlassDataset(
            data_dir='dataset/train_gdd',
            mask_dir='dataset/train_gdd/glass_mask_rtglassnet',
            transform=None
        )
        train_loader = DataLoader(
            train_dataset, 
            batch_size=8, 
            shuffle=True, 
            num_workers=4,
            pin_memory=True
        )
        print(f"📊 训练数据: {len(train_dataset)} 样本")
    except Exception as e:
        print(f"⚠️ 数据加载失败: {e}")
        print("📝 请根据实际数据路径调整配置")
        return
    
    # 训练循环
    num_epochs = 100
    model.train()
    
    for epoch in range(num_epochs):
        epoch_loss = 0.0
        epoch_focal_loss = 0.0
        epoch_iou_loss = 0.0
        epoch_edge_loss = 0.0
        
        for batch_idx, (images, masks) in enumerate(train_loader):
            images = images.to(device)
            masks = masks.to(device)
            
            # 前向传播
            predictions = model(images)
            
            # 计算损失
            total_loss, loss_dict = criterion(predictions, masks)
            
            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
            
            # 记录损失
            epoch_loss += total_loss.item()
            epoch_focal_loss += loss_dict['focal_loss']
            epoch_iou_loss += loss_dict['iou_loss']
            epoch_edge_loss += loss_dict['edge_loss']
            
            # 打印进度
            if batch_idx % 10 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, "
                      f"Loss: {total_loss.item():.4f}")
        
        # 更新学习率
        scheduler.step()
        
        # 打印epoch总结
        avg_loss = epoch_loss / len(train_loader)
        avg_focal = epoch_focal_loss / len(train_loader)
        avg_iou = epoch_iou_loss / len(train_loader)
        avg_edge = epoch_edge_loss / len(train_loader)
        
        print(f"📊 Epoch {epoch+1} 总结:")
        print(f"   总损失: {avg_loss:.4f}")
        print(f"   Focal损失: {avg_focal:.4f}")
        print(f"   IoU损失: {avg_iou:.4f}")
        print(f"   边缘损失: {avg_edge:.4f}")
        print(f"   学习率: {scheduler.get_last_lr()[0]:.6f}")
        print("-" * 50)
        
        # 保存检查点
        if (epoch + 1) % 10 == 0:
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'loss': avg_loss,
            }
            torch.save(checkpoint, f'ckpt/rtgv2_epoch_{epoch+1}.pth')
            print(f"💾 检查点已保存: ckpt/rtgv2_epoch_{epoch+1}.pth")
    
    print("🎉 RTGlassNetv2训练完成！")

def evaluate_rtgv2():
    """RTGlassNetv2评估示例"""
    print("🔍 开始RTGlassNetv2评估...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载模型
    model = RTGlassNetv2(backbone_type='inceptionnext_tiny').to(device)
    
    # 加载检查点（如果有）
    checkpoint_path = 'ckpt/rtgv2_epoch_100.pth'
    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"📂 加载检查点: {checkpoint_path}")
    else:
        print("⚠️ 未找到检查点，使用随机初始化的模型")
    
    model.eval()
    
    # 创建测试输入
    test_input = torch.randn(1, 3, 224, 224).to(device)
    
    with torch.no_grad():
        h_pred, l_pred, final_pred, refined_pred = model(test_input)
        
        print("📊 模型输出:")
        print(f"   h_pred shape: {h_pred.shape}")
        print(f"   l_pred shape: {l_pred.shape}")
        print(f"   final_pred shape: {final_pred.shape}")
        print(f"   refined_pred shape: {refined_pred.shape}")
        
        print(f"📈 输出范围:")
        print(f"   h_pred: [{h_pred.min().item():.4f}, {h_pred.max().item():.4f}]")
        print(f"   l_pred: [{l_pred.min().item():.4f}, {l_pred.max().item():.4f}]")
        print(f"   final_pred: [{final_pred.min().item():.4f}, {final_pred.max().item():.4f}]")
        print(f"   refined_pred: [{refined_pred.min().item():.4f}, {refined_pred.max().item():.4f}]")
    
    print("✅ RTGlassNetv2评估完成！")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='RTGlassNetv2训练和评估')
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'eval'],
                       help='运行模式: train 或 eval')
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        train_rtgv2()
    elif args.mode == 'eval':
        evaluate_rtgv2()
    else:
        print("❌ 无效的模式，请选择 'train' 或 'eval'") 