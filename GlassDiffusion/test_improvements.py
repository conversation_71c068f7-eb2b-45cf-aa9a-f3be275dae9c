#!/usr/bin/env python3
"""
🧪 测试改进效果的脚本
对比原始训练和改进训练的差异
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import json
from datetime import datetime

def test_differential_lr():
    """测试差分学习率设置"""
    print("🔧 测试差分学习率设置...")
    
    # 模拟模型参数
    class MockModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.backbone = nn.Sequential(
                nn.Conv2d(3, 64, 3),
                nn.Conv2d(64, 128, 3)
            )
            self.decoder = nn.Sequential(
                nn.Conv2d(128, 64, 3),
                nn.Conv2d(64, 1, 1)
            )
    
    model = MockModel()
    
    # 创建差分学习率优化器
    backbone_params = []
    decoder_params = []
    
    for name, param in model.named_parameters():
        if 'backbone' in name:
            backbone_params.append(param)
        else:
            decoder_params.append(param)
    
    param_groups = [
        {'params': backbone_params, 'lr': 1e-5, 'weight_decay': 1e-4},
        {'params': decoder_params, 'lr': 3e-4, 'weight_decay': 1e-4}
    ]
    
    optimizer = torch.optim.AdamW(param_groups)
    
    print(f"✅ 差分学习率设置成功:")
    print(f"   - Backbone参数组: {len(backbone_params)} 参数, LR: {optimizer.param_groups[0]['lr']}")
    print(f"   - Decoder参数组: {len(decoder_params)} 参数, LR: {optimizer.param_groups[1]['lr']}")
    
    return True

def test_loss_weights():
    """测试损失权重平衡"""
    print("\n⚖️ 测试损失权重平衡...")
    
    # 原始权重
    original_weights = {
        'focal': 0.25,
        'iou': 0.65,
        'dt': 0.10
    }
    
    # 改进权重
    improved_weights = {
        'focal': 0.3,
        'iou': 0.5,
        'dt': 0.2
    }
    
    print(f"原始权重: {original_weights}")
    print(f"改进权重: {improved_weights}")
    
    # 分析权重变化
    changes = {}
    for key in original_weights:
        change = improved_weights[key] - original_weights[key]
        changes[key] = change
        direction = "增加" if change > 0 else "减少"
        print(f"   - {key}损失: {direction} {abs(change):.2f}")
    
    print("✅ 权重平衡分析完成")
    return changes

def test_gradient_clipping():
    """测试梯度裁剪效果"""
    print("\n✂️ 测试梯度裁剪...")
    
    # 创建测试张量
    x = torch.randn(10, requires_grad=True)
    
    # 模拟大梯度
    loss = (x ** 2).sum() * 1000
    loss.backward()
    
    print(f"裁剪前梯度范数: {x.grad.norm().item():.4f}")
    
    # 应用梯度裁剪
    torch.nn.utils.clip_grad_norm_([x], max_norm=0.5)
    
    print(f"裁剪后梯度范数: {x.grad.norm().item():.4f}")
    print("✅ 梯度裁剪测试完成")
    
    return True

def test_numerical_stability():
    """测试数值稳定性"""
    print("\n🔢 测试数值稳定性...")
    
    # 测试异常值检测
    normal_loss = torch.tensor(0.5)
    nan_loss = torch.tensor(float('nan'))
    inf_loss = torch.tensor(float('inf'))
    
    def check_loss_stability(loss, name):
        if torch.isnan(loss) or torch.isinf(loss):
            print(f"   ⚠️ 检测到异常{name}损失: {loss}")
            return False
        else:
            print(f"   ✅ {name}损失正常: {loss:.4f}")
            return True
    
    check_loss_stability(normal_loss, "正常")
    check_loss_stability(nan_loss, "NaN")
    check_loss_stability(inf_loss, "Inf")
    
    print("✅ 数值稳定性测试完成")
    return True

def compare_configurations():
    """对比配置差异"""
    print("\n📊 对比训练配置...")
    
    original_config = {
        'learning_rate': 0.0003,
        'lr_strategy': 'single',
        'focal_weight': 0.25,
        'iou_weight': 0.65,
        'dt_weight': 0.10,
        'augmentation': 'light',
        'validation_interval': 5,
        'gradient_clipping': False,
        'warmup': False,
        'early_stopping': False
    }
    
    improved_config = {
        'backbone_lr': 1e-5,
        'decoder_lr': 3e-4,
        'lr_strategy': 'differential',
        'focal_weight': 0.3,
        'iou_weight': 0.5,
        'dt_weight': 0.2,
        'augmentation': 'medium',
        'validation_interval': 2,
        'gradient_clipping': True,
        'warmup': True,
        'early_stopping': True
    }
    
    print("原始配置 vs 改进配置:")
    print("-" * 50)
    
    improvements = [
        "✅ 单一学习率 → 差分学习率 (backbone: 1e-5, decoder: 3e-4)",
        "✅ IoU权重过高 → 平衡权重分配 (IoU: 0.65→0.5, DT: 0.1→0.2)",
        "✅ 轻量增强 → 中等强度增强",
        "✅ 验证间隔5轮 → 2轮 (更早发现问题)",
        "✅ 无梯度裁剪 → 添加梯度裁剪 (max_norm=0.5)",
        "✅ 无warmup → 添加10轮warmup",
        "✅ 无早停 → 添加基于IoU的早停"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    return original_config, improved_config

def generate_improvement_report():
    """生成改进报告"""
    print("\n📋 生成改进报告...")
    
    report = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'improvements': {
            'learning_rate': {
                'original': 'Single LR: 0.0003',
                'improved': 'Differential LR: backbone=1e-5, decoder=3e-4',
                'expected_benefit': 'Better backbone fine-tuning, faster decoder learning'
            },
            'loss_weights': {
                'original': 'Focal:0.25, IoU:0.65, DT:0.10',
                'improved': 'Focal:0.30, IoU:0.50, DT:0.20',
                'expected_benefit': 'Balanced supervision, better DT learning'
            },
            'augmentation': {
                'original': 'Light augmentation',
                'improved': 'Medium augmentation',
                'expected_benefit': 'Better generalization'
            },
            'training_stability': {
                'original': 'No gradient clipping, no warmup',
                'improved': 'Gradient clipping + warmup + early stopping',
                'expected_benefit': 'More stable training, prevent overfitting'
            }
        },
        'expected_improvements': {
            'iou_gain': '2-5 percentage points',
            'training_stability': 'Significantly improved',
            'convergence_speed': '20-30% faster',
            'overfitting_risk': 'Reduced'
        }
    }
    
    # 保存报告
    with open('improvement_report.json', 'w') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("✅ 改进报告已保存到 improvement_report.json")
    return report

def main():
    """主测试函数"""
    print("🎯 RTGlassNet 训练改进测试")
    print("=" * 60)
    
    # 运行各项测试
    test_differential_lr()
    test_loss_weights()
    test_gradient_clipping()
    test_numerical_stability()
    compare_configurations()
    report = generate_improvement_report()
    
    print("\n🎉 所有测试完成!")
    print("\n📈 预期改进效果:")
    for key, value in report['expected_improvements'].items():
        print(f"   - {key}: {value}")
    
    print("\n🚀 建议下一步:")
    print("   1. 使用改进的训练脚本 train_kfold_improved.py")
    print("   2. 监控训练过程中的指标变化")
    print("   3. 对比原始训练和改进训练的结果")
    print("   4. 根据结果进一步调优参数")

if __name__ == '__main__':
    main()
