import os
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import *
from ig_glass.gdnet_scsa import GDNetSCSA

# Note: For FLOPs calculation, install thop library: pip install thop

# Device setup
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# Paths
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext101_32x8.pth'

# Parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM'
exp_name = 'IG-SLAM-SCSA'
args = {
    'snapshot': 'scsan-110-40',  # Use 'best_model' to automatically find the best model scsan-110-40 scsa50-100-110
    'scale': 416,
    'crf': False,  # No need for CRF post-processing as it's already integrated
    'glass_threshold': 0.5,  # Threshold for glass region detection
    'crf_iter': 3,  # Number of CRF iterations
    'bilateral_weight': 10.0,  # From compat=10
    'gaussian_weight': 5.0,  # From compat=5
    'bilateral_spatial_sigma': 40.0,  # From sxy=40
    'bilateral_color_sigma': 3.0,  # From srgb=3
    'gaussian_sigma': 1.5,  # From sxy=1.5
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()


def path_set(tempdata_path, new_path):
    results_root = os.path.join(tempdata_path, new_path)
    return results_root


def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, glass_threshold, log_interval=100):
    """检测玻璃区域并评估结果，并按指定间隔输出中间结果。"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    image_files.sort()

    count = 0
    total_files = len(image_files)
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0

    inference_times = []
    total_times = []

    # 关闭性能优化以确保精度
    torch.cuda.empty_cache()
    torch.backends.cudnn.benchmark = False  # 禁用cuDNN自动调优
    torch.backends.cudnn.deterministic = True  # 使用确定性算法
    
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)

        total_start = torch.cuda.Event(enable_timing=True)
        total_end = torch.cuda.Event(enable_timing=True)
        total_start.record()

        # 优化的图像读取和预处理
        img = cv2.imread(image_path)
        if img is None:
            print(f"Warning: Could not read image {image_path}")
            continue
            
        orig_h, orig_w = img.shape[:2]  # 保存原始尺寸
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = cv2.resize(img, (args['scale'], args['scale']))
        img = img.transpose((2, 0, 1))  # HWC to CHW
        img = torch.from_numpy(img).float().div(255.0)
        # 标准化图像
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        img = (img - mean) / std
        img_var = img.unsqueeze(0).cuda(device_ids[0])

        start_time = torch.cuda.Event(enable_timing=True)
        end_time = torch.cuda.Event(enable_timing=True)

        start_time.record()
        with torch.no_grad():
            _, _, _, refined_pred = model(img_var)
            
            # 确保预测值在有效范围内
            refined_pred = torch.clamp(refined_pred, 0, 1)
            
        end_time.record()

        torch.cuda.synchronize()
        inference_time = start_time.elapsed_time(end_time) / 1000.0
        inference_times.append(inference_time)

        # 优化的后处理
        if refined_pred.size(1) == 2:
            pred = refined_pred[:, 1:2]
        else:
            pred = refined_pred

        # 在GPU上完成所有操作，最后才转到CPU
        pred = torch.nn.functional.interpolate(pred, size=(orig_h, orig_w), mode='bilinear', align_corners=False)
        pred = (pred.squeeze(0).squeeze(0) > glass_threshold).float()
        pred = pred.cpu().numpy()  # 最后一步才转到CPU

        total_end.record()
        torch.cuda.synchronize()
        total_time = total_start.elapsed_time(total_end) / 1000.0
        total_times.append(total_time)

        # 保存结果（原始尺寸）
        pred_save = (pred * 255).astype(np.uint8)
        temp_name = os.path.splitext(image_file)[0]
        result_name = temp_name + '.png'
        cv2.imwrite(os.path.join(output_folder, result_name), pred_save)

        # 评估结果
        gt_name = result_name
        gt_path = os.path.join(gt_folder, gt_name)

        if os.path.exists(gt_path):
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            # 确保真值图像尺寸与预测一致
            if gt_mask.shape != (orig_h, orig_w):
                print(f"Warning: GT mask size mismatch for {image_file}")
                continue

            # 计算评估指标（在原始尺寸上）
            gt = gt_mask.astype(np.float32) / 255.0

            tiou = compute_iou(pred, gt)
            tacc = compute_acc(pred, gt)
            precision, recall = compute_precision_recall(pred, gt)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(pred, gt)
            tber = compute_ber(pred, gt)
            taber = compute_aber(pred, gt)

            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber += taber

            if count > 0 and count % log_interval == 0 and count < total_files:
                print(f"\n--- Intermediate results after {count}/{total_files} images ---")
                print(f"IoU: {iou / count:.4f}, Acc: {acc / count:.4f}, F-measure: {fm / count:.4f}, MAE: {mae / count:.4f}")
                
                if len(inference_times) > 5:
                    current_avg_inference = np.mean(inference_times[5:])
                    current_avg_total = np.mean(total_times[5:])
                    current_fps = 1.0 / current_avg_inference
                    current_total_fps = 1.0 / current_avg_total
                    print(f"Current Avg Pure Inference FPS: {current_fps:.2f}")
                    print(f"Current Avg Total FPS: {current_total_fps:.2f}")

        # 定期清理GPU缓存
        if count % 100 == 0:
            torch.cuda.empty_cache()

    if count > 0:
        iou = iou / count
        acc = acc / count
        fm = fm / count
        mae = mae / count
        ber = ber / count
        aber = aber / count

    if len(inference_times) > 5:
        avg_inference_time = np.mean(inference_times[5:])
        avg_total_time = np.mean(total_times[5:])
        pure_fps = 1.0 / avg_inference_time
        total_fps = 1.0 / avg_total_time
        print(f"\n==================================================")
        print(f"Performance Metrics:")
        print(f"Pure Inference Time: {avg_inference_time*1000:.2f}ms")
        print(f"Total Pipeline Time: {avg_total_time*1000:.2f}ms")
        print(f"Pure Inference FPS: {pure_fps:.1f}")
        print(f"Total Pipeline FPS: {total_fps:.1f}")
        print(f"==================================================")

    return iou, acc, fm, mae, ber, aber


def main():
    # 记录硬件信息
    device_name = torch.cuda.get_device_name(device_ids[0])
    print(f"--- Running on: {device_name} ---")
    
    # 加载模型
    model = GDNetSCSA(backbone='resnext101', backbone_path=None, crf_iter=args['crf_iter'], trainable_crf=False) #resnet50,resnext101

    # 设置CRF参数
    if hasattr(model, 'crf') and hasattr(model.crf, 'bilateral_weight'):
        model.crf.bilateral_weight = torch.nn.Parameter(torch.tensor(args['bilateral_weight']))
        model.crf.gaussian_weight = torch.nn.Parameter(torch.tensor(args['gaussian_weight']))
        model.crf.bilateral_spatial_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_spatial_sigma']))
        model.crf.bilateral_color_sigma = torch.nn.Parameter(torch.tensor(args['bilateral_color_sigma']))
        model.crf.gaussian_sigma = torch.nn.Parameter(torch.tensor(args['gaussian_sigma']))

    # 加载模型权重
    if args['snapshot'].startswith('best_model'):
        # 查找最新的best_model文件
        model_files = [f for f in os.listdir(ckpt_path) if f.startswith('best_model')]
        if model_files:
            model_path = os.path.join(ckpt_path, sorted(model_files)[-1])  # 获取最新的
            print(f"Loading best model from {model_path}")
            checkpoint = torch.load(model_path)
            model.load_state_dict(checkpoint['model'])
        else:
            print("No best_model found. Please specify a model path.")
            return
    else:
        # 加载指定的模型
        print(f'Load snapshot {args["snapshot"]} for testing')
        model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
        checkpoint = torch.load(model_path)
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
        else:
            model.load_state_dict(checkpoint)
        print(f'Load {model_path} succeed!')

    model.cuda(device_ids[0])
    model.eval()

    # 计算模型复杂度指标
    print("\n=== Model Complexity Analysis ===")
    
    # 1. 参数量统计
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total Parameters: {total_params/1e6:.2f} M")
    print(f"Trainable Parameters: {trainable_params/1e6:.2f} M")
    
    # 2. FLOPs计算
    try:
        from thop import profile
        dummy_input = torch.randn(1, 3, args['scale'], args['scale']).cuda(device_ids[0])
        flops, params_thop = profile(model, inputs=(dummy_input, ))
        print(f"FLOPs: {flops/1e9:.2f} GFLOPs")
        print(f"Parameters (THOP): {params_thop/1e6:.2f} M")
    except ImportError:
        print("Warning: 'thop' library not installed. Run 'pip install thop' to calculate FLOPs.")
    except Exception as e:
        print(f"Warning: FLOPs calculation failed: {e}")
    
    # 3. 内存和延迟测试
    print("\n--- Memory and Latency Test (batch_size=1) ---")
    dummy_input = torch.randn(1, 3, args['scale'], args['scale']).cuda(device_ids[0])
    
    # 重置内存统计
    torch.cuda.reset_peak_memory_stats(device_ids[0])
    torch.cuda.empty_cache()
    
    # 测量总延迟和推理延迟
    with torch.no_grad():
        # 预热
        for _ in range(10):
            _ = model(dummy_input)
        torch.cuda.synchronize()
        
        # 测量推理延迟
        inference_start = torch.cuda.Event(enable_timing=True)
        inference_end = torch.cuda.Event(enable_timing=True)
        
        inference_start.record()
        _ = model(dummy_input)
        inference_end.record()
        torch.cuda.synchronize()
        inference_time = inference_start.elapsed_time(inference_end) / 1000.0
        
        # 测量总延迟(包括数据准备和后处理)
        total_start = torch.cuda.Event(enable_timing=True)
        total_end = torch.cuda.Event(enable_timing=True)
        
        total_start.record()
        img_var = dummy_input.clone()  # 模拟数据预处理
        output = model(img_var)
        pred = output[3].data.squeeze(0).cpu()  # 模拟后处理
        total_end.record()
        torch.cuda.synchronize()
        total_time = total_start.elapsed_time(total_end) / 1000.0
    
    # 获取峰值内存使用
    peak_memory_mb = torch.cuda.max_memory_allocated(device_ids[0]) / (1024*1024)
    print(f"Peak Activation Memory: {peak_memory_mb:.2f} MB")
    print(f"Pure Inference Time: {inference_time*1000:.2f} ms")
    print(f"Total Pipeline Time: {total_time*1000:.2f} ms")
    print(f"Pure Inference FPS: {1.0/inference_time:.2f}")
    print(f"Total Pipeline FPS: {1.0/total_time:.2f}")
    
    print("\n=== Starting Evaluation ===")

    # 设置路径
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/test_GDD"  # 测试数据路径
    current = "glass_mask_scsa"  # 结果文件夹名称

    # 设置输入输出路径
    image_folder = path_set(data_path, "image")  # 输入图像文件夹
    output_folder = path_set(data_path, current)  # 输出结果文件夹
    gt_folder = path_set(data_path, "mask")  # 真值文件夹

    # 检测玻璃区域并评估结果
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate(
        image_folder, output_folder, gt_folder, model, args['glass_threshold'],
        log_interval=100  # 每处理100张图片输出一次中间结果
    )

    # 打印详细评估结果
    print("\n" + "="*60)
    print("            COMPREHENSIVE EVALUATION RESULTS")
    print("="*60)
    
    print("\n📊 Accuracy Metrics:")
    print(f"  IoU (Intersection over Union): {iou:.4f}")
    print(f"  Accuracy:                      {acc:.4f}")
    print(f"  F-measure:                     {fm:.4f}")
    print(f"  MAE (Mean Absolute Error):     {mae:.4f}")
    print(f"  BER (Balanced Error Rate):     {ber:.4f}")
    print(f"  ABER (Adaptive BER):           {aber:.4f}")
    
    print("\n⚡ Performance Metrics:")
    print(f"  Pure Inference Time:           {inference_time*1000:.2f} ms")
    print(f"  Total Pipeline Time:           {total_time*1000:.2f} ms")
    print(f"  Pure Inference FPS:            {1.0/inference_time:.2f}")
    print(f"  Total Pipeline FPS:            {1.0/total_time:.2f}")
    
    print("\n🔧 Model Complexity:")
    print(f"  Total Parameters:              {total_params/1e6:.2f} M")
    try:
        print(f"  FLOPs:                         {flops/1e9:.2f} GFLOPs")
    except:
        pass
    
    print("\n💾 Hardware Context:")
    device_name = torch.cuda.get_device_name(device_ids[0])
    print(f"  GPU:                           {device_name}")
    print(f"  Input Resolution:              {args['scale']}x{args['scale']}")
    print(f"  Peak Memory Usage:             {peak_memory_mb:.2f} MB")
    
    print("\n" + "="*60)
    
    return iou, acc, fm, mae, ber, aber


if __name__ == '__main__':
    main() 