# run_test_examples.py
# -----------------------------------------------------------------------------
# ProteusGlassNet 测试示例脚本
# 展示如何使用test_proteus_glassnet.py进行各种测试
# -----------------------------------------------------------------------------

import os
import sys
import subprocess

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"执行命令: {cmd}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        print(f"返回码: {result.returncode}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def main():
    """主函数 - 运行各种测试示例"""
    
    # 检查是否存在模型权重文件
    model_paths = [
        "checkpoints/exp_001/checkpoints/model/stage1_model_best.pth",
        "checkpoints/exp_001/checkpoints/model/stage1_model_epoch_010_iou_0.8500_mae_0.1200.pth",
        "checkpoints/exp_001/checkpoints/model/stage1_model_epoch_005_iou_0.8000_mae_0.1500.pth"
    ]
    
    # 查找可用的模型权重
    available_model = None
    for path in model_paths:
        if os.path.exists(path):
            available_model = path
            break
    
    if not available_model:
        print("❌ 未找到可用的模型权重文件")
        print("请先训练模型或指定正确的模型路径")
        return
    
    print(f"✅ 找到模型权重: {available_model}")
    
    # 示例1: 性能基准测试
    print("\n📋 示例1: 性能基准测试")
    print("测试模型推理性能，包括FPS和平均推理时间")
    
    benchmark_cmd = f"python test_proteus_glassnet.py --mode benchmark --model_path {available_model} --num_runs 50"
    run_command(benchmark_cmd, "性能基准测试")
    
    # 示例2: 数据集测试
    print("\n📋 示例2: 数据集测试")
    print("在完整数据集上评估模型性能")
    
    dataset_cmd = f"python test_proteus_glassnet.py --mode dataset --model_path {available_model} --batch_size 4"
    run_command(dataset_cmd, "数据集测试")
    
    # 示例3: 单张图像测试（如果有测试图像）
    test_images = [
        "dataset/test_images/glass_001.jpg",
        "dataset/test_images/glass_002.jpg",
        "dataset/test_images/glass_003.jpg"
    ]
    
    for img_path in test_images:
        if os.path.exists(img_path):
            print(f"\n📋 示例3: 单张图像测试 - {img_path}")
            print("测试单张图像的玻璃检测效果")
            
            output_path = f"test_results/single_test_{os.path.basename(img_path)}"
            single_cmd = f"python test_proteus_glassnet.py --mode single --model_path {available_model} --image_path {img_path} --output_path {output_path}"
            run_command(single_cmd, f"单张图像测试: {img_path}")
            break
    else:
        print("\n📋 示例3: 单张图像测试")
        print("⚠️ 未找到测试图像，跳过单张图像测试")
        print("请将测试图像放在 dataset/test_images/ 目录下")
    
    # 示例4: 快速测试（不保存结果）
    print("\n📋 示例4: 快速测试")
    print("快速测试，不保存预测结果和可视化")
    
    quick_cmd = f"python test_proteus_glassnet.py --mode dataset --model_path {available_model} --batch_size 8 --no_save --no_visualize"
    run_command(quick_cmd, "快速测试")
    
    print("\n" + "="*60)
    print("🎉 所有测试示例执行完成!")
    print("="*60)
    print("📁 测试结果保存在: test_results/")
    print("📊 详细结果文件: test_results/test_results.txt")
    print("🖼️ 可视化结果: test_results/visualization_*.png")
    print("📈 预测结果: test_results/predictions/")

if __name__ == "__main__":
    main() 