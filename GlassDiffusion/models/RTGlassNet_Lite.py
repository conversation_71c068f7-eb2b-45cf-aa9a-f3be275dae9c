#!/usr/bin/env python3
"""
🚀 RTGlassNet Lite - 轻量化王牌组合版本
支持可选择DT分支：训练时双分支，推理时可选单分支
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 添加InceptionNeXt路径并导入
sys.path.insert(0, '/home/<USER>/ws/IG_SLAM/inceptionnext')
try:
    import models.inceptionnext as inception_models
    INCEPTIONNEXT_AVAILABLE = True
    print("✅ InceptionNeXt models imported successfully")
except ImportError as e:
    print(f"❌ Warning: InceptionNeXt not available: {e}")
    INCEPTIONNEXT_AVAILABLE = False

from ig_glass.attention_scsa import SCSA
from .diff_crf import SimplifiedDiffCRF

# 1. FeatureExtractor (保持不变)
class FeatureExtractor(nn.Module):
    """
    轻量化多尺度特征提取器
    """
    def __init__(self, backbone_type='inceptionnext_tiny', pretrained=False):
        super().__init__()
        self.backbone_type = backbone_type.lower()
        
        if not INCEPTIONNEXT_AVAILABLE:
            raise ImportError("InceptionNeXt models not available!")
        
        # 支持不同规模的InceptionNeXt
        if 'tiny' in self.backbone_type:
            self.backbone = inception_models.inceptionnext_tiny(pretrained=pretrained)
            self.feature_dims = [96, 192, 384, 768]  # Tiny尺寸
        elif 'small' in self.backbone_type:
            self.backbone = inception_models.inceptionnext_small(pretrained=pretrained)
            self.feature_dims = [96, 192, 384, 768]  # Small尺寸
        elif 'base' in self.backbone_type:
            self.backbone = inception_models.inceptionnext_base(pretrained=pretrained)
            self.feature_dims = [128, 256, 512, 1024]  # Base尺寸
        else:
            raise ValueError(f"Unsupported backbone: {self.backbone_type}")
            
        # 移除分类头
        if hasattr(self.backbone, 'head'):
            self.backbone.head = nn.Identity()
        if hasattr(self.backbone, 'fc'):
            self.backbone.fc = nn.Identity()
            
    def forward(self, x):
        features = []
        x = self.backbone.stem(x)
        
        for i, stage in enumerate(self.backbone.stages):
            x = stage(x)
            features.append(x)
            
        return features[-4:]  # 返回最后4个尺度的特征

# 2. LightweightFPN
class LightweightFPN(nn.Module):
    """
    轻量化FPN：减少通道数但保持性能
    """
    def __init__(self, in_channels_list, out_channels=96):  # 从128降到96
        super().__init__()
        self.out_channels = out_channels
        
        # 横向连接（减少通道数）
        self.lateral_convs = nn.ModuleList([
            nn.Conv2d(in_ch, out_channels, 1) 
            for in_ch in in_channels_list
        ])
        
        # 输出卷积（使用深度可分离卷积）
        self.fpn_convs = nn.ModuleList([
            self._make_separable_conv(out_channels, out_channels)
            for _ in in_channels_list
        ])
        
    def _make_separable_conv(self, in_ch, out_ch):
        """深度可分离卷积"""
        return nn.Sequential(
            nn.Conv2d(in_ch, in_ch, 3, padding=1, groups=in_ch),  # 深度卷积
            nn.Conv2d(in_ch, out_ch, 1),  # 逐点卷积
            nn.BatchNorm2d(out_ch),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, features):
        # 自顶向下路径
        laterals = [lateral_conv(features[i]) for i, lateral_conv in enumerate(self.lateral_convs)]
        
        # 上采样和融合
        for i in range(len(laterals) - 2, -1, -1):
            laterals[i] += F.interpolate(laterals[i + 1], size=laterals[i].shape[-2:], mode='bilinear', align_corners=False)
        
        # 最终输出
        fpn_outs = [fpn_conv(laterals[i]) for i, fpn_conv in enumerate(self.fpn_convs)]
        return fpn_outs[1]  # 返回P2 (1/4分辨率)

# 3. LightEdgeEnhancer（保持不变，这是王牌组件）
class LightEdgeEnhancer(nn.Module):
    """
    🌟 王牌组合核心：轻量级边缘增强器
    """
    def __init__(self, in_channels):
        super().__init__()
        # 边缘注意力模块
        self.edge_attention = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 1),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, in_channels, 1),
            nn.Sigmoid()
        )
        
        # 边缘检测卷积
        self.edge_conv = nn.Conv2d(in_channels, in_channels, 3, padding=1)
        
    def forward(self, x):
        # 计算边缘注意力权重
        edge_weights = self.edge_attention(x)
        
        # 应用边缘增强
        edge_features = self.edge_conv(x)
        enhanced_features = x + edge_weights * edge_features
        
        return enhanced_features

# 4. 可选择双分支预测头
class SelectableDualBranchHead(nn.Module):
    """
    🚀 可选择双分支预测头
    - 训练时：双分支 (主预测 + DT预测)
    - 推理时：可选单分支 (仅主预测)
    """
    def __init__(self, in_channels, lite_mode=False):
        super().__init__()
        self.lite_mode = lite_mode
        
        # 共享特征提取（轻量化版本）
        hidden_ch = in_channels // 2 if lite_mode else in_channels
        self.shared_conv = nn.Sequential(
            nn.Conv2d(in_channels, hidden_ch, 3, padding=1),
            nn.BatchNorm2d(hidden_ch),
            nn.LeakyReLU(inplace=True),
            nn.Conv2d(hidden_ch, hidden_ch, 3, padding=1),
            nn.BatchNorm2d(hidden_ch),
            nn.LeakyReLU(inplace=True)
        )
        
        # 主预测分支（必须）
        self.main_branch = nn.Conv2d(hidden_ch, 1, 1)
        
        # DT预测分支（可选）
        if not lite_mode:
            self.dt_branch = nn.Conv2d(hidden_ch, 1, 1)
        else:
            self.dt_branch = None
            
    def forward(self, x, enable_dt=True):
        shared_feat = self.shared_conv(x)
        
        # 主预测（必须）
        main_logits = self.main_branch(shared_feat)
        main_pred = torch.sigmoid(main_logits)
        
        # DT预测（可选）
        if self.dt_branch is not None and enable_dt:
            dt_logits = self.dt_branch(shared_feat)
            dt_pred = torch.sigmoid(dt_logits)
            return {
                'main_logits': main_logits,
                'main_pred': main_pred,
                'dt_logits': dt_logits,
                'dt_pred': dt_pred
            }
        else:
            return {
                'main_logits': main_logits,
                'main_pred': main_pred,
                'dt_logits': None,
                'dt_pred': None
            }

# 5. SCSA投影层
class SCSAWithProj(nn.Module):
    def __init__(self, in_channels, out_channels, head_num=8):
        super().__init__()
        self.proj = nn.Conv2d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()
        self.scsa = SCSA(out_channels, head_num)
        
    def forward(self, x):
        x = self.proj(x)
        return self.scsa(x)

# 6. 主模型
class RTGlassNetLite(nn.Module):
    """
    🚀 RTGlassNet Lite - 轻量化王牌组合
    特点：
    1. 可选择DT分支（训练双分支，推理可单分支）
    2. 轻量化FPN（96通道 vs 128通道）
    3. 保留王牌组合核心：LightEdgeEnhancer
    4. 灵活的推理模式
    """
    def __init__(self, backbone_type='inceptionnext_tiny', lite_mode=False, 
                 fpn_out=96, crf_iter=3, crf_bilateral_weight=8.0):
        super().__init__()
        
        self.lite_mode = lite_mode
        
        # 1. 特征提取器
        self.feature_extractor = FeatureExtractor(backbone_type)
        
        # 2. 轻量化FPN
        self.fpn = LightweightFPN(
            in_channels_list=self.feature_extractor.feature_dims,
            out_channels=fpn_out
        )
        
        # 3. 🌟 王牌组合：边缘增强器
        self.edge_enhancer = LightEdgeEnhancer(fpn_out)
        
        # 4. SCSA注意力融合
        self.scsa = SCSAWithProj(in_channels=fpn_out*2, out_channels=fpn_out, head_num=8)
        
        # 5. 可选择双分支预测头
        self.pred_head = SelectableDualBranchHead(fpn_out, lite_mode=lite_mode)
        
        # 6. CRF后处理
        self.diff_crf = SimplifiedDiffCRF(n_iter=crf_iter, bilateral_weight=crf_bilateral_weight)
        
    def forward(self, x, enable_dt=None, enable_crf=True):
        """
        前向传播
        Args:
            x: 输入图像 [B, 3, H, W]
            enable_dt: 是否启用DT分支（None时根据训练状态自动决定）
            enable_crf: 是否启用CRF后处理
        """
        # 自动决定DT分支状态
        if enable_dt is None:
            enable_dt = self.training and not self.lite_mode
        
        original_img = x.clone()
        B, _, H, W = x.shape
        
        # 1. 特征提取
        features = self.feature_extractor(x)
        
        # 2. FPN融合
        fpn_feat = self.fpn(features)
        
        # 3. 🌟 边缘增强（王牌组合核心）
        enhanced_feat = self.edge_enhancer(fpn_feat)
        
        # 4. SCSA注意力融合
        concat_feat = torch.cat([fpn_feat, enhanced_feat], dim=1)
        fused_feat = self.scsa(concat_feat)
        
        # 5. 双分支预测
        outputs = self.pred_head(fused_feat, enable_dt=enable_dt)
        
        # 6. CRF后处理
        if enable_crf:
            main_pred_refined = self.diff_crf(outputs['main_pred'], original_img)
            outputs['refined_pred'] = main_pred_refined
        else:
            outputs['refined_pred'] = outputs['main_pred']
            
        return outputs
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / 1024 / 1024,
            'lite_mode': self.lite_mode,
            'features': ['LightweightFPN', 'LightEdgeEnhancer', 'SCSA', 'SelectableDualBranch', 'CRF']
        }
    
    def switch_to_lite_mode(self):
        """切换到轻量化模式（推理时使用）"""
        self.lite_mode = True
        # 可以进一步移除DT分支权重来节省内存
        if hasattr(self.pred_head, 'dt_branch'):
            self.pred_head.dt_branch = None
    
    def switch_to_full_mode(self):
        """切换到完整模式（训练时使用）"""
        self.lite_mode = False

# 7. 便捷构造函数
def create_rtglassnet_lite(mode='tiny', lite_inference=False):
    """
    便捷创建RTGlassNet Lite模型
    
    Args:
        mode: 'tiny', 'small', 'base' - 控制模型大小
        lite_inference: 是否为轻量化推理模式
    """
    backbone_mapping = {
        'tiny': 'inceptionnext_tiny',
        'small': 'inceptionnext_small', 
        'base': 'inceptionnext_base'
    }
    
    fpn_channels = {
        'tiny': 80,   # 超轻量
        'small': 96,  # 轻量
        'base': 128   # 标准
    }
    
    return RTGlassNetLite(
        backbone_type=backbone_mapping[mode],
        lite_mode=lite_inference,
        fpn_out=fpn_channels[mode]
    )

if __name__ == '__main__':
    # 测试不同模式
    print("🚀 RTGlassNet Lite 模型测试")
    print("=" * 50)
    
    for mode in ['tiny', 'small', 'base']:
        print(f"\n📊 {mode.upper()} 模式:")
        
        try:
            # 完整模式（训练）
            model_full = create_rtglassnet_lite(mode=mode, lite_inference=False)
            info_full = model_full.get_model_info()
            
            # 轻量模式（推理）
            model_lite = create_rtglassnet_lite(mode=mode, lite_inference=True)
            info_lite = model_lite.get_model_info()
            
            print(f"   完整模式: {info_full['total_params']/1e6:.1f}M 参数")
            print(f"   轻量模式: {info_lite['total_params']/1e6:.1f}M 参数")
            print(f"   推理优化: -{(info_full['total_params']-info_lite['total_params'])/1e3:.1f}K 参数")
            
        except Exception as e:
            print(f"   ❌ 模式测试失败: {e}")
    
    print(f"\n🌟 轻量化策略总结:")
    print(f"   1. 训练时：完整双分支，最佳性能")
    print(f"   2. 推理时：可选单分支，最高速度") 
    print(f"   3. 部署时：灵活切换，按需优化")
    print(f"   4. 王牌组合：LightEdgeEnhancer始终保留") 