import torch
import torch.nn as nn
import torch.nn.functional as F

class EdgeAwareBCELoss(nn.Module):
    """
    边缘感知BCE损失，数值稳定，适配logits输入。
    """
    def __init__(self, edge_weight=1.3):
        super().__init__()
        self.edge_weight = edge_weight
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.],
            [-1., 8., -1.],
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))
        self.bce = nn.BCEWithLogitsLoss(reduction='none')

    def forward(self, logits, target):
        target = torch.clamp(target, min=0.0, max=1.0)
        if logits.dim() == 3:
            logits = logits.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
        target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
        edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
        bce_loss = self.bce(logits, target)
        weighted_loss = bce_loss * edge_weight_map
        return weighted_loss.mean()

class EdgeAwareFocalLoss(nn.Module):
    """
    边缘感知Focal损失，数值稳定，适配logits输入。
    """
    def __init__(self, alpha=0.25, gamma=2.0, edge_weight=1.3):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.edge_weight = edge_weight
        self.register_buffer('laplacian_kernel', torch.tensor([
            [-1., -1., -1.],
            [-1., 8., -1.],
            [-1., -1., -1.]
        ]).view(1, 1, 3, 3))

    def forward(self, logits, target):
        target = torch.clamp(target, min=0.0, max=1.0)
        if logits.dim() == 3:
            logits = logits.unsqueeze(1)
        if target.dim() == 3:
            target = target.unsqueeze(1)
        prob = torch.sigmoid(logits)
        target_edges = F.relu(torch.tanh(F.conv2d(target, self.laplacian_kernel.to(target.device), padding=1)))
        edge_weight_map = target_edges * (self.edge_weight - 1.0) + 1.0
        bce_loss = F.binary_cross_entropy_with_logits(logits, target, reduction='none')
        pt = prob * target + (1 - prob) * (1 - target)
        focal_weight = (1 - pt) ** self.gamma
        alpha_weight = target * self.alpha + (1 - target) * (1 - self.alpha)
        focal_loss = alpha_weight * focal_weight * bce_loss
        weighted_loss = focal_loss * edge_weight_map
        return weighted_loss.mean()

class LightIOU(nn.Module):
    """
    轻量级IoU损失，数值稳定，适配logits输入。
    """
    def __init__(self):
        super().__init__()
    def forward(self, logits, target):
        prob = torch.sigmoid(logits)
        target = torch.clamp(target, min=0.0, max=1.0)
        intersection = torch.sum(target * prob, dim=(1, 2, 3))
        union = torch.sum(target, dim=(1, 2, 3)) + torch.sum(prob, dim=(1, 2, 3)) - intersection
        union = torch.clamp(union, min=1e-7)
        iou = (intersection / union).mean()
        iou_loss = 1.0 - iou
        return iou_loss

class DistanceTransformLoss(nn.Module):
    """
    距离变换损失 - 🌟 王牌组合的核心组件
    专门优化边界预测质量，提供精准的梯度信号
    """
    def __init__(self, l1_weight=0.7, mse_weight=0.3):
        super().__init__()
        self.l1_weight = l1_weight
        self.mse_weight = mse_weight
    
    def forward(self, dt_logits, dt_target):
        """
        计算距离变换损失
        Args:
            dt_logits: [B, 1, H, W] DT预测logits
            dt_target: [B, 1, H, W] 目标DT图 [0, 1]范围
        """
        dt_pred = torch.sigmoid(dt_logits)
        dt_target = torch.clamp(dt_target, 0.0, 1.0)
        
        # L1损失 - 适合梯度信息
        l1_loss = F.l1_loss(dt_pred, dt_target)
        
        # MSE损失 - 适合连续值回归  
        mse_loss = F.mse_loss(dt_pred, dt_target)
        
        # 组合损失
        loss = self.l1_weight * l1_loss + self.mse_weight * mse_loss
        return loss

class CompositeLoss(nn.Module):
    """
    🌟 王牌组合损失函数：Focal + IoU + Distance Transform
    完美实现 LightEdgeEnhancer + DT Loss 的协同优化
    """
    def __init__(self, focal_weight=0.4, iou_weight=0.4, dt_weight=0.2, use_focal=True):
        super().__init__()
        self.focal_weight = focal_weight
        self.iou_weight = iou_weight
        self.dt_weight = dt_weight
        self.use_focal = use_focal
        
        # 损失函数
        self.iou_loss = LightIOU()
        self.dt_loss = DistanceTransformLoss()
        
        if use_focal:
            self.main_loss = EdgeAwareFocalLoss()
        else:
            self.main_loss = EdgeAwareBCELoss()
    
    def forward(self, outputs, gt_masks, dt_maps):
        """
        计算复合损失
        Args:
            outputs: 模型输出字典 {
                'main_logits': 主预测logits [B, 1, H, W]
                'dt_logits': DT预测logits [B, 1, H, W]
                'main_pred': 主预测 [B, 1, H, W]
                'dt_pred': DT预测 [B, 1, H, W]
            }
            gt_masks: 真值掩码 [B, 1, H, W]
            dt_maps: 距离变换图 [B, 1, H, W]
        Returns:
            total_loss: 总损失
            loss_dict: 损失详情
        """
        # 提取预测结果
        main_logits = outputs['main_logits']
        dt_logits = outputs['dt_logits']
        
        # 计算主预测损失
        if self.use_focal:
            main_focal_loss = self.main_loss(main_logits, gt_masks)
        else:
            main_focal_loss = self.main_loss(main_logits, gt_masks)
        
        # 计算IoU损失
        main_iou_loss = self.iou_loss(main_logits, gt_masks)
        
        # 🌟 计算DT损失 - 王牌组合的核心
        dt_transform_loss = self.dt_loss(dt_logits, dt_maps)
        
        # 组合总损失
        total_loss = (
            self.focal_weight * main_focal_loss + 
            self.iou_weight * main_iou_loss + 
            self.dt_weight * dt_transform_loss
        )
        
        # 损失详情
        loss_dict = {
            'focal_loss': main_focal_loss.item(),
            'iou_loss': main_iou_loss.item(),
            'dt_loss': dt_transform_loss.item(),
            'total_loss': total_loss.item()
        }
        
        return total_loss, loss_dict 