"""
@Time    : 2025
<AUTHOR> Glennine & Gemini

@Project : IG_SLAM
@File    : gdnet_fpn_scsa.py
@Function: Modernized GDNet with FPN and SCSA. Replaces LightLCFI with a standard FPN decoder for cleaner, more efficient feature fusion.

"""
import torch
import torch.nn as nn
import torch.nn.functional as F

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import SCSA


class FPNDecoder(nn.Module):
    """
    Standard Feature Pyramid Network Decoder.
    Takes multi-scale features from the backbone and fuses them.
    """
    def __init__(self, in_channels_list, out_channels=256):
        super(FPNDecoder, self).__init__()
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels

        self.lateral_convs = nn.ModuleList()
        self.output_convs = nn.ModuleList()

        for in_channels in self.in_channels_list:
            self.lateral_convs.append(nn.Conv2d(in_channels, out_channels, kernel_size=1))
            self.output_convs.append(nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1))

        # Initialize weights
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_uniform_(m.weight, a=1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, features):
        # features = [c2, c3, c4, c5]
        laterals = [conv(f) for conv, f in zip(self.lateral_convs, features)]

        # Top-down pathway
        for i in range(len(laterals) - 1, 0, -1):
            laterals[i-1] += F.interpolate(laterals[i], size=laterals[i-1].shape[2:], mode='bilinear', align_corners=False)

        # Output convolutions
        outs = [conv(lat) for conv, lat in zip(self.output_convs, laterals)]
        # outs = [p2, p3, p4, p5]
        return outs


class PredictionHead(nn.Module):
    """Simple prediction head for segmentation"""
    def __init__(self, in_channels, out_channels=1):
        super(PredictionHead, self).__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, out_channels, kernel_size=1)
        )

    def forward(self, x):
        return self.conv(x)


class GDNetFPNSCSA(nn.Module):
    """GDNet with FPN and SCSA"""
    def __init__(self, backbone_path=None, crf_iter=3, trainable_crf=True):
        super(GDNetFPNSCSA, self).__init__()

        # --- Backbone ---
        resnext = ResNeXt101(backbone_path)
        self.layer0 = resnext.layer0 # 64, H/2, W/2
        self.layer1 = resnext.layer1 # 256, H/4, W/4 (c2)
        self.layer2 = resnext.layer2 # 512, H/8, W/8 (c3)
        self.layer3 = resnext.layer3 # 1024, H/16, W/16 (c4)
        self.layer4 = resnext.layer4 # 2048, H/32, W/32 (c5)

        # --- FPN Decoder ---
        fpn_in_channels = [256, 512, 1024, 2048]
        self.fpn_decoder = FPNDecoder(in_channels_list=fpn_in_channels, out_channels=256)

        # --- SCSA Attention ---
        # Applied on the highest resolution FPN feature map (P2)
        self.scsa = SCSA(dim=256, head_num=8, window_size=7)

        # --- Prediction Head ---
        self.pred_head = PredictionHead(in_channels=256, out_channels=1)

        # --- Differentiable CRF ---
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,
            gaussian_weight=5.0,
            bilateral_spatial_sigma=40.0,
            bilateral_color_sigma=3.0,
            gaussian_sigma=1.5,
            trainable=trainable_crf
        )

        # --- Class Balancing Weights ---
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)

    def forward(self, x):
        input_size = x.shape[2:]

        # --- Backbone Feature Extraction ---
        l0 = self.layer0(x)
        c2 = self.layer1(l0)
        c3 = self.layer2(c2)
        c4 = self.layer3(c3)
        c5 = self.layer4(c4)

        # --- FPN Feature Fusion ---
        fpn_features = self.fpn_decoder([c2, c3, c4, c5])
        p2 = fpn_features[0] # Highest resolution feature map

        # --- SCSA Context Enhancement ---
        p2_enhanced = self.scsa(p2)

        # --- Prediction ---
        main_logits = self.pred_head(p2_enhanced)
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        main_pred = torch.sigmoid(main_logits)

        # --- CRF Refinement ---
        # Prepare 2-channel logits for CRF
        main_prob = torch.clamp(main_pred, min=1e-7, max=1.0 - 1e-7)
        
        # Check for NaN or Inf values
        if torch.isnan(main_prob).any() or torch.isinf(main_prob).any():
            print("警告: 主预测中检测到NaN或Inf值")
            main_prob = torch.nan_to_num(main_prob, nan=0.5, posinf=1.0, neginf=0.0)
        
        bg_logits = torch.log((1 - main_prob) * self.class_weights[0])
        fg_logits = torch.log(main_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # Normalize input image for CRF
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # Apply CRF with exception handling
        try:
            crf_output = self.crf(combined_logits, normalized_img)
            # Check if CRF output has valid shape
            if crf_output.size(1) == 0:
                print("警告: CRF返回了通道数为0的张量。创建备用输出。")
                refined_pred = torch.cat([1 - main_prob, main_prob], dim=1)
            elif crf_output.size(1) == 2:
                refined_pred = crf_output[:, 1:2, :, :] # Select foreground channel
            else:
                refined_pred = crf_output
        except Exception as e:
            print(f"CRF错误: {e}")
            # Create fallback prediction
            refined_pred = torch.cat([1 - main_prob, main_prob], dim=1)
            if refined_pred.size(1) == 2:
                refined_pred = refined_pred[:, 1:2, :, :] # Select foreground channel

        return {
            'main_pred': main_pred,
            'main_logits': main_logits,
            'refined_pred': refined_pred
        }
