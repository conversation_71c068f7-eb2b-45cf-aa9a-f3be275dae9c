#!/usr/bin/env python3
"""
🔍 调试test_gdd.py输出全白mask的问题
分析可能的原因并提供修复方案
"""

import os
import warnings
import cv2
import numpy as np
import torch
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
from GlassDiffusion.models.RTGlassNet import RTGlassNet
import matplotlib.pyplot as plt

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

def debug_model_output(model, test_image_path, device):
    """调试模型输出"""
    print("🔍 调试模型输出...")
    
    # 读取测试图像
    img_rgb = cv2.imread(test_image_path)
    if img_rgb is None:
        print(f"❌ 无法读取图像: {test_image_path}")
        return None
    
    img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
    print(f"✅ 图像读取成功: {img_rgb.shape}")
    
    # 预处理
    img_transform = A.<PERSON>([
        <PERSON>.Resize(height=384, width=384),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])
    
    transformed = img_transform(image=img_rgb)
    img_tensor = transformed['image'].unsqueeze(0).to(device)
    print(f"✅ 预处理完成: {img_tensor.shape}, 范围: [{img_tensor.min():.3f}, {img_tensor.max():.3f}]")
    
    # 模型推理
    model.eval()
    with torch.no_grad():
        outputs = model(img_tensor)
    
    print(f"\n📊 模型输出分析:")
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: shape={value.shape}, range=[{value.min():.6f}, {value.max():.6f}], mean={value.mean():.6f}")
        else:
            print(f"  {key}: {type(value)}")
    
    return outputs

def analyze_prediction_pipeline(outputs, threshold=0.5):
    """分析预测管道"""
    print(f"\n🔬 分析预测管道 (阈值={threshold})...")
    
    # 检查refined_pred
    refined_pred = outputs.get('refined_pred')
    main_pred = outputs.get('main_pred')
    
    if refined_pred is not None:
        print(f"refined_pred: shape={refined_pred.shape}, range=[{refined_pred.min():.6f}, {refined_pred.max():.6f}]")
        
        # 检查是否全为极值
        if refined_pred.max() - refined_pred.min() < 1e-5:
            print(f"⚠️ refined_pred几乎为常数值: {refined_pred.mean():.6f}")
            
        if refined_pred.mean() < 1e-4:
            print(f"⚠️ refined_pred均值过小: {refined_pred.mean():.6f}")
            
        # 二值化分析
        binary_pred = (refined_pred > threshold).float()
        white_ratio = binary_pred.mean().item()
        print(f"refined_pred二值化后白色像素比例: {white_ratio:.4f}")
        
        if white_ratio > 0.95:
            print("❌ 检测到全白问题: refined_pred二值化后几乎全为白色")
        elif white_ratio < 0.05:
            print("❌ 检测到全黑问题: refined_pred二值化后几乎全为黑色")
    
    if main_pred is not None:
        print(f"main_pred: shape={main_pred.shape}, range=[{main_pred.min():.6f}, {main_pred.max():.6f}]")
        binary_main = (main_pred > threshold).float()
        main_white_ratio = binary_main.mean().item()
        print(f"main_pred二值化后白色像素比例: {main_white_ratio:.4f}")

def test_different_thresholds(outputs):
    """测试不同阈值的效果"""
    print(f"\n🎯 测试不同阈值效果...")
    
    pred_tensor = outputs.get('refined_pred', outputs.get('main_pred'))
    if pred_tensor is None:
        print("❌ 没有找到预测张量")
        return
    
    thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
    
    for thresh in thresholds:
        binary_pred = (pred_tensor > thresh).float()
        white_ratio = binary_pred.mean().item()
        print(f"  阈值 {thresh}: 白色像素比例 {white_ratio:.4f}")

def check_model_weights(model):
    """检查模型权重是否正常"""
    print(f"\n🔧 检查模型权重...")
    
    total_params = 0
    zero_params = 0
    nan_params = 0
    
    for name, param in model.named_parameters():
        total_params += param.numel()
        zero_params += (param == 0).sum().item()
        nan_params += torch.isnan(param).sum().item()
    
    print(f"  总参数数: {total_params}")
    print(f"  零参数数: {zero_params} ({zero_params/total_params*100:.2f}%)")
    print(f"  NaN参数数: {nan_params}")
    
    if zero_params / total_params > 0.5:
        print("⚠️ 警告: 超过50%的参数为零，可能权重加载有问题")
    
    if nan_params > 0:
        print("❌ 错误: 发现NaN参数，模型权重异常")

def create_fixed_test_script():
    """创建修复后的测试脚本"""
    print(f"\n🛠️ 创建修复后的测试脚本...")
    
    fixed_script = '''#!/usr/bin/env python3
"""
🔧 修复后的test_gdd.py
解决输出mask全白的问题
"""

import os
import warnings
import cv2
import numpy as np
import torch
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',
    'scale': 384,
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理
img_transform = A.Compose([
    A.Resize(height=args['scale'], width=args['scale']),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ToTensorV2()
])

def detect_glass_and_evaluate_fixed(image_folder, output_folder, gt_folder, model, glass_threshold):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files.sort()
    
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0
    count = 0
    
    for image_file in tqdm(image_files, desc="Processing"):
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        
        # 读取图像
        img_rgb = cv2.imread(image_path)
        if img_rgb is None:
            continue
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
        
        # 读取GT
        if not os.path.exists(gt_path):
            continue
        gt_mask_u8 = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        if gt_mask_u8 is None:
            continue
        
        # 预处理
        transformed = img_transform(image=img_rgb)
        img_var = transformed['image'].unsqueeze(0).cuda(device_ids[0])
        
        # 模型推理
        with torch.no_grad():
            outputs = model(img_var)
            
            # 🔧 修复1: 优先使用main_pred，避免CRF问题
            pred_tensor = outputs['main_pred']
            
            # 🔧 修复2: 检查预测张量的有效性
            if torch.isnan(pred_tensor).any() or torch.isinf(pred_tensor).any():
                print(f"⚠️ 检测到异常值在 {image_file}")
                pred_tensor = torch.zeros_like(pred_tensor)
            
            # 🔧 修复3: 确保张量在合理范围内
            pred_tensor = torch.clamp(pred_tensor, 0.0, 1.0)
            
            # 🔧 修复4: 如果refined_pred可用且合理，则使用它
            refined_pred = outputs.get('refined_pred')
            if refined_pred is not None:
                if not (torch.isnan(refined_pred).any() or torch.isinf(refined_pred).any()):
                    if refined_pred.max() - refined_pred.min() > 1e-5 and refined_pred.mean() > 1e-4:
                        pred_tensor = refined_pred
            
            # 提取预测概率图
            if pred_tensor.dim() == 4:
                pred_prob_map = pred_tensor.squeeze(0).squeeze(0)
            else:
                pred_prob_map = pred_tensor.squeeze()
        
        # 🔧 修复5: 动态阈值调整
        # 如果使用固定阈值导致全白或全黑，尝试自适应阈值
        binary_pred = (pred_prob_map > glass_threshold).float()
        white_ratio = binary_pred.mean().item()
        
        if white_ratio > 0.95 or white_ratio < 0.05:
            # 使用Otsu阈值或均值阈值
            pred_np = pred_prob_map.cpu().numpy()
            adaptive_threshold = np.mean(pred_np)
            binary_pred = (pred_prob_map > adaptive_threshold).float()
            print(f"使用自适应阈值 {adaptive_threshold:.3f} for {image_file}")
        
        prediction_float = binary_pred.cpu().numpy()
        
        # 处理GT
        gt_resized = cv2.resize(gt_mask_u8, (args['scale'], args['scale']))
        gt_float = (gt_resized / 255.0).astype(np.float32)
        
        # 保存预测结果
        pred_to_save = (prediction_float * 255).astype(np.uint8)
        output_path = os.path.join(output_folder, os.path.splitext(image_file)[0] + '.png')
        cv2.imwrite(output_path, pred_to_save)
        
        # 计算指标
        tiou = compute_iou(prediction_float, gt_float)
        tacc = compute_acc(prediction_float, gt_float)
        precision, recall = compute_precision_recall(prediction_float, gt_float)
        tfm = compute_fmeasure(precision, recall)
        tmae = compute_mae(prediction_float, gt_float)
        tber = compute_ber(prediction_float, gt_float)
        taber = compute_aber(prediction_float, gt_float)
        
        count += 1
        iou += tiou
        acc += tacc
        fm += tfm
        mae += tmae
        ber += tber
        aber += taber
    
    if count > 0:
        return iou/count, acc/count, fm/count, mae/count, ber/count, aber/count
    else:
        return 0, 0, 0, 0, 0, 0

def main():
    print("🔧 使用修复后的测试脚本")
    
    # 加载模型
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    model = RTGlassNet(backbone_type=backbone_type, crf_iter=args['crf_iter'], crf_bilateral_weight=args['crf_bilateral_weight'])
    
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    model.cuda(device_ids[0])
    model.eval()
    
    # 测试
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    image_folder = os.path.join(data_path, "image")
    output_folder = os.path.join(data_path, "glass_mask_gdd_fixed")
    gt_folder = os.path.join(data_path, "mask")
    
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate_fixed(
        image_folder, output_folder, gt_folder, model, args['glass_threshold']
    )
    
    print(f"\\n修复后的结果:")
    print(f"IoU: {iou:.4f}")
    print(f"Accuracy: {acc:.4f}")
    print(f"F-measure: {fm:.4f}")
    print(f"MAE: {mae:.4f}")

if __name__ == '__main__':
    main()
'''
    
    with open('GlassDiffusion/test_gdd_fixed.py', 'w') as f:
        f.write(fixed_script)
    
    print("✅ 修复脚本已保存为 test_gdd_fixed.py")

def main():
    print("🔍 调试test_gdd.py输出全白mask问题")
    print("=" * 60)
    
    # 检查模型文件是否存在
    ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
    model_file = 'BEST_fold5_epoch075_iou0.9797.pth'
    model_path = os.path.join(ckpt_path, model_file)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请检查模型路径和文件名")
        return
    
    print(f"✅ 找到模型文件: {model_path}")
    
    # 加载模型
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    checkpoint = torch.load(model_path, map_location=device)
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    model = RTGlassNet(backbone_type=backbone_type, crf_iter=3, crf_bilateral_weight=5.0)
    
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    model.to(device)
    
    # 检查模型权重
    check_model_weights(model)
    
    # 找一张测试图像
    test_image_dir = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd/image"
    if os.path.exists(test_image_dir):
        image_files = [f for f in os.listdir(test_image_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        if image_files:
            test_image_path = os.path.join(test_image_dir, image_files[0])
            print(f"✅ 使用测试图像: {test_image_path}")
            
            # 调试模型输出
            outputs = debug_model_output(model, test_image_path, device)
            
            if outputs:
                # 分析预测管道
                analyze_prediction_pipeline(outputs)
                
                # 测试不同阈值
                test_different_thresholds(outputs)
        else:
            print(f"❌ 测试图像目录为空: {test_image_dir}")
    else:
        print(f"❌ 测试图像目录不存在: {test_image_dir}")
    
    # 创建修复脚本
    create_fixed_test_script()
    
    print(f"\n🎯 可能的问题和解决方案:")
    print("1. CRF后处理异常 → 优先使用main_pred")
    print("2. 阈值设置不当 → 使用自适应阈值")
    print("3. 预测值异常 → 添加数值检查和裁剪")
    print("4. 权重加载问题 → 检查权重加载日志")
    print("5. 预处理不一致 → 确保与训练时一致")
    
    print(f"\n🚀 建议:")
    print("1. 使用修复后的脚本 test_gdd_fixed.py")
    print("2. 检查模型训练时的预处理流程")
    print("3. 验证权重文件的完整性")

if __name__ == '__main__':
    main()
