import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 🔧 修复：使用timm库导入InceptionNeXt模型
try:
    import timm
    INCEPTIONNEXT_AVAILABLE = True
    print("✅ InceptionNeXt models (timm) imported successfully")
except ImportError as e:
    print(f"❌ Warning: timm not available: {e}")
    INCEPTIONNEXT_AVAILABLE = False

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from ig_glass.attention_scsa import SCSA
try:
    from models.diff_crf import SimplifiedDiffCRF
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.append(parent_dir)
    from models.diff_crf import SimplifiedDiffCRF

# 1. LightLCFI模块（从gdnet_scsa.py复制）
class LightLCFI(nn.Module):
    """Lightweight Large Context Feature Integration module with depthwise separable convolutions"""
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(LightLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = int(input_channels / 4)
        self.channels_double = int(input_channels / 2)
        self.dr1 = dr1
        self.dr2 = dr2
        self.dr3 = dr3
        self.dr4 = dr4
        self.padding1 = 1 * dr1
        self.padding2 = 2 * dr2
        self.padding3 = 3 * dr3
        self.padding4 = 4 * dr4

        # Channel reduction with 1x1 convolutions
        self.p1_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p1
        self.p1_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (3, 1), 1, padding=(self.padding1, 0),
                      dilation=(self.dr1, 1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (1, 3), 1, padding=(0, self.padding1),
                      dilation=(1, self.dr1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p2
        self.p2_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (5, 1), 1, padding=(self.padding2, 0),
                      dilation=(self.dr2, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 5), 1, padding=(0, self.padding2),
                      dilation=(1, self.dr2), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p3
        self.p3_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (7, 1), 1, padding=(self.padding3, 0),
                      dilation=(self.dr3, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 7), 1, padding=(0, self.padding3),
                      dilation=(1, self.dr3), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p4
        self.p4_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (9, 1), 1, padding=(self.padding4, 0),
                      dilation=(self.dr4, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 9), 1, padding=(0, self.padding4),
                      dilation=(1, self.dr4), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Use SCSA for attention instead of CBAM
        self.scsa = SCSA(
            dim=self.input_channels,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )

        # Final channel reduction
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU())

    def forward(self, x):
        p1_input = self.p1_channel_reduction(x)
        p1 = self.p1_fusion(torch.cat((self.p1_d1(p1_input), self.p1_d2(p1_input)), 1))

        p2_input = torch.cat((self.p2_channel_reduction(x), p1), 1)
        p2 = self.p2_fusion(torch.cat((self.p2_d1(p2_input), self.p2_d2(p2_input)), 1))

        p3_input = torch.cat((self.p3_channel_reduction(x), p2), 1)
        p3 = self.p3_fusion(torch.cat((self.p3_d1(p3_input), self.p3_d2(p3_input)), 1))

        p4_input = torch.cat((self.p4_channel_reduction(x), p3), 1)
        p4 = self.p4_fusion(torch.cat((self.p4_d1(p4_input), self.p4_d2(p4_input)), 1))

        # 使用SCSA处理特征
        combined_features = torch.cat((p1, p2, p3, p4), 1)
        attended_features = self.scsa(combined_features)
        channel_reduction = self.channel_reduction(attended_features)

        return channel_reduction

# 2. LightEdgeEnhancer（从gdnet_scsa.py复制）
class LightEdgeEnhancer(nn.Module):
    """Lightweight module to enhance edge features"""
    def __init__(self, in_channels):
        super(LightEdgeEnhancer, self).__init__()
        # Depthwise separable convolution
        self.edge_conv = nn.Sequential(
            # Depthwise
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            # Pointwise
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # Laplacian kernel for edge detection
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
        
    def forward(self, x):
        # Apply edge-aware enhancement
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        
        # Apply edge-aware convolution
        enhanced = self.edge_conv(x)
        
        # Combine with original features with edge attention
        return x + enhanced * edge_features

# 3. FeatureExtractor（支持InceptionNeXt）
class FeatureExtractor(nn.Module):
    """
    多尺度特征提取器，使用timm库并正确加载预训练权重
    """
    def __init__(self, backbone_type='inceptionnext_base', pretrained=True, drop_path_rate=0.1):
        super().__init__()
        self.backbone_type = backbone_type.lower()
        
        if not INCEPTIONNEXT_AVAILABLE:
            raise ImportError("timm library not available!")
        
        # 修正: 使用正确的timm模型名称，并设置pretrained=True
        if 'tiny' in self.backbone_type:
            model_name = 'inception_next_tiny.sail_in1k'
            self.out_channels_list = [96, 192, 384, 768]
        elif 'small' in self.backbone_type:
            model_name = 'inception_next_small.sail_in1k'
            self.out_channels_list = [96, 192, 384, 768]
        elif 'base' in self.backbone_type:
            model_name = 'inception_next_base.sail_in1k'
            self.out_channels_list = [128, 256, 512, 1024]
        else:
            raise ValueError(f"Unknown InceptionNeXt backbone: {backbone_type}")

        print(f"🔧 创建backbone: {model_name} (pretrained={pretrained})")
        try:
            # 使用与RTGlassNet相同的配置
            self.backbone = timm.create_model(
                model_name,
                pretrained=pretrained,
                features_only=True,
                out_indices=(0, 1, 2, 3),  # 对应C2, C3, C4, C5
                drop_path_rate=drop_path_rate
            )
            print("✅ Backbone创建成功")
            
            # 打印每层的输出通道数
            if hasattr(self.backbone, 'feature_info'):
                print("📊 特征层信息:")
                for idx, info in enumerate(self.backbone.feature_info):
                    print(f"   Layer {idx}: {info['num_chs']} channels")
                    
        except Exception as e:
            print(f"❌ Backbone创建失败: {str(e)}")
            raise

    def forward(self, x):
        # 直接返回timm骨干网络提取的特征
        feats = self.backbone(x)
        return feats

    @property
    def out_channels(self):
        """返回backbone各层的输出通道数"""
        if hasattr(self.backbone, 'feature_info'):
            return [info['num_chs'] for info in self.backbone.feature_info]
        return self.out_channels_list

# 4. RTGlassNetv2主干
class RTGlassNetv2(nn.Module):
    """
    实时玻璃分割v2架构：Backbone + LightLCFI + SCSA + diffCRF + DT监督
    使用LightLCFI替代FPN，保持与gdnet_scsa相同的架构
    新增双支距离变换监督，提升边界精度
    """
    def __init__(self, backbone_type='inceptionnext_base', crf_iter=3, crf_bilateral_weight=10.0, drop_path_rate=0.1):
        super().__init__()
        self.feature_extractor = FeatureExtractor(backbone_type=backbone_type, drop_path_rate=drop_path_rate)
        in_channels_list = self.feature_extractor.out_channels
        
        # 动态计算通道数
        self.backbone_type = backbone_type.lower()
        if 'tiny' in self.backbone_type:
            # InceptionNeXt-Tiny: [96, 192, 384, 768]
            h5_channels = 768 // 4  # 192
            h4_channels = 384 // 4  # 96
            h3_channels = 192 // 4  # 48
            l2_channels = 96 // 4   # 24
            h_fusion_channels = h5_channels + h4_channels + h3_channels  # 192 + 96 + 48 = 336
            # 确保能被4整除
            h_fusion_channels = ((h_fusion_channels + 3) // 4) * 4  # 336 -> 336 (已经是4的倍数)
            final_fusion_channels = h_fusion_channels + l2_channels  # 336 + 24 = 360
            # 确保能被4整除
            final_fusion_channels = ((final_fusion_channels + 3) // 4) * 4  # 360 -> 360 (已经是4的倍数)
        elif 'small' in self.backbone_type:
            # InceptionNeXt-Small: [96, 192, 384, 768]
            h5_channels = 768 // 4  # 192
            h4_channels = 384 // 4  # 96
            h3_channels = 192 // 4  # 48
            l2_channels = 96 // 4   # 24
            h_fusion_channels = h5_channels + h4_channels + h3_channels  # 192 + 96 + 48 = 336
            # 确保能被4整除
            h_fusion_channels = ((h_fusion_channels + 3) // 4) * 4  # 336 -> 336 (已经是4的倍数)
            final_fusion_channels = h_fusion_channels + l2_channels  # 336 + 24 = 360
            # 确保能被4整除
            final_fusion_channels = ((final_fusion_channels + 3) // 4) * 4  # 360 -> 360 (已经是4的倍数)
        else:
            # InceptionNeXt-Base: [128, 256, 512, 1024]
            h5_channels = 1024 // 4  # 256
            h4_channels = 512 // 4   # 128
            h3_channels = 256 // 4   # 64
            l2_channels = 128 // 4   # 32
            h_fusion_channels = h5_channels + h4_channels + h3_channels  # 256 + 128 + 64 = 448
            # 确保能被4整除
            h_fusion_channels = ((h_fusion_channels + 3) // 4) * 4  # 448 -> 448 (已经是4的倍数)
            final_fusion_channels = h_fusion_channels + l2_channels  # 448 + 32 = 480
            # 确保能被4整除
            final_fusion_channels = ((final_fusion_channels + 3) // 4) * 4  # 480 -> 480 (已经是4的倍数)
        
        # 使用LightLCFI模块替代FPN - 使用更大的膨胀率范围
        self.h5_conv = LightLCFI(in_channels_list[3], dr1=1, dr2=3, dr3=5, dr4=7)  # 更大的感受野
        self.h4_conv = LightLCFI(in_channels_list[2], dr1=1, dr2=2, dr3=4, dr4=6)
        self.h3_conv = LightLCFI(in_channels_list[1], dr1=1, dr2=2, dr3=3, dr4=5)
        self.l2_conv = LightLCFI(in_channels_list[0], dr1=1, dr2=2, dr3=3, dr4=4)  # 保持原始设置

        # h fusion
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 使用SCSA替代CBAM - 确保通道数能被4整除
        self.h_fusion = SCSA(
            dim=h_fusion_channels,  # 动态计算
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.h_fusion_conv = nn.Sequential(
            # Depthwise separable conv
            nn.Conv2d(h_fusion_channels, h_fusion_channels, 3, 1, 1, groups=h_fusion_channels),
            nn.Conv2d(h_fusion_channels, h_fusion_channels, 1, 1, 0),
            nn.BatchNorm2d(h_fusion_channels), nn.ReLU())

        # l fusion
        self.l_fusion_conv = nn.Sequential(
            # Depthwise separable conv
            nn.Conv2d(l2_channels, l2_channels, 3, 1, 1, groups=l2_channels),
            nn.Conv2d(l2_channels, l2_channels, 1, 1, 0),
            nn.BatchNorm2d(l2_channels), nn.ReLU())
        self.h2l = nn.ConvTranspose2d(h_fusion_channels, 1, 8, 4, 2)

        # Edge enhancer
        self.edge_enhancer = LightEdgeEnhancer(h_fusion_channels)

        # final fusion
        self.h_up_for_final_fusion = nn.ConvTranspose2d(h_fusion_channels, h_fusion_channels//2, 8, 4, 2)
        
        # 使用SCSA替代CBAM
        # 计算final_fusion的实际输入通道数
        actual_final_fusion_channels = h_fusion_channels//2 + l2_channels  # h_up输出 + l_fusion
        # 确保能被4整除
        actual_final_fusion_channels = ((actual_final_fusion_channels + 3) // 4) * 4
        
        self.final_fusion = SCSA(
            dim=actual_final_fusion_channels,  # 使用实际计算的通道数
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.final_fusion_conv = nn.Sequential(
            # Depthwise separable conv
            nn.Conv2d(actual_final_fusion_channels, actual_final_fusion_channels, 3, 1, 1, groups=actual_final_fusion_channels),
            nn.Conv2d(actual_final_fusion_channels, actual_final_fusion_channels, 1, 1, 0),
            nn.BatchNorm2d(actual_final_fusion_channels), nn.ReLU())

        # 🌟 新增：双支预测头 - 主预测 + DT预测
        self.shared_conv = nn.Sequential(
            nn.Conv2d(actual_final_fusion_channels, actual_final_fusion_channels, 3, padding=1),
            nn.BatchNorm2d(actual_final_fusion_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(actual_final_fusion_channels, actual_final_fusion_channels, 3, padding=1),
            nn.BatchNorm2d(actual_final_fusion_channels),
            nn.ReLU(inplace=True)
        )
        
        # 主预测分支
        self.main_branch = nn.Conv2d(actual_final_fusion_channels, 1, 1)
        
        # 🌟 DT预测分支 - 距离变换监督
        self.dt_branch = nn.Conv2d(actual_final_fusion_channels, 1, 1)

        # 原有的分层预测（保持兼容性）
        self.h_predict = nn.Conv2d(h_fusion_channels, 1, 3, 1, 1)
        self.l_predict = nn.Conv2d(l2_channels, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(actual_final_fusion_channels, 1, 3, 1, 1)

        # CRF layer - 可以选择使用DiffCRF或SimplifiedDiffCRF
        use_full_crf = False  # 设置为True使用完整的DiffCRF
        
        if use_full_crf:
            # 使用完整的DiffCRF（内存消耗大，但理论上精度更高）
            from models.diff_crf import DiffCRF
            self.crf = DiffCRF(
                n_iter=crf_iter,
                pos_weight=5.0,
                pos_xy_std=1.5,
                bi_xy_std=40.0,
                bi_rgb_std=3.0,
                trainable=True
            )
        else:
            # 使用简化的SimplifiedDiffCRF（推荐，稳定高效）
            self.crf = SimplifiedDiffCRF(
                n_iter=crf_iter,
                bilateral_weight=10.0,  # From compat=10
                gaussian_weight=5.0,     # From compat=5
                bilateral_spatial_sigma=40.0,  # From sxy=40
                bilateral_color_sigma=3.0,     # From srgb=3
                gaussian_sigma=1.5,            # From sxy=1.5
                trainable=True
            )

        # Class balancing weights for handling imbalanced data
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)

        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True

    def forward(self, x):
        # x: [batch_size, channel=3, h, w]
        input_size = x.shape[2:]
        
        # 特征提取
        c2, c3, c4, c5 = self.feature_extractor(x)
        
        # 使用LightLCFI处理各层特征
        h5_conv = self.h5_conv(c5)
        h4_conv = self.h4_conv(c4)
        h3_conv = self.h3_conv(c3)
        l2_conv = self.l2_conv(c2)

        # h fusion
        h5_up = self.h5_up(h5_conv)
        h3_down = self.h3_down(h3_conv)
        h_fusion_input = torch.cat((h5_up, h4_conv, h3_down), 1)
        h_fusion = self.h_fusion(h_fusion_input)
        h_fusion = self.h_fusion_conv(h_fusion)
        h_fusion = self.edge_enhancer(h_fusion)

        # l fusion
        l_fusion = self.l_fusion_conv(l2_conv)
        h2l = self.h2l(h_fusion)
        l_fusion = torch.sigmoid(h2l) * l_fusion

        # final fusion
        h_up_for_final_fusion = self.h_up_for_final_fusion(h_fusion)
        final_fusion_input = torch.cat((h_up_for_final_fusion, l_fusion), 1)
        final_fusion = self.final_fusion(final_fusion_input)
        final_fusion = self.final_fusion_conv(final_fusion)

        # --- 统一预测 ---
        # 1. 新的双分支预测 (主预测)
        shared_features = self.shared_conv(final_fusion)
        main_logits = self.main_branch(shared_features)
        dt_logits = self.dt_branch(shared_features)

        # 2. 用于深度监督的旧预测
        h_logits = self.h_predict(h_fusion)
        l_logits = self.l_predict(l_fusion)
        final_logits_sup = self.final_predict(final_fusion) # final_predict for supervision

        # --- 上采样所有logits ---
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        dt_logits = F.interpolate(dt_logits, size=input_size, mode='bilinear', align_corners=True)
        h_logits = F.interpolate(h_logits, size=input_size, mode='bilinear', align_corners=True)
        l_logits = F.interpolate(l_logits, size=input_size, mode='bilinear', align_corners=True)
        final_logits_sup = F.interpolate(final_logits_sup, size=input_size, mode='bilinear', align_corners=True)

        # --- 计算所有概率图 ---
        main_pred = torch.sigmoid(main_logits)
        dt_pred = torch.sigmoid(dt_logits)
        h_pred_sup = torch.sigmoid(h_logits)
        l_pred_sup = torch.sigmoid(l_logits)
        final_pred_sup = torch.sigmoid(final_logits_sup)

        # --- CRF精炼 (核心步骤) ---
        # 使用主预测 main_pred 作为CRF的输入
        main_prob = torch.clamp(main_pred, min=1e-7, max=1.0 - 1e-7)
        
        if torch.isnan(main_prob).any() or torch.isinf(main_prob).any():
            main_prob = torch.nan_to_num(main_prob, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 创建CRF需要的2通道logits
        bg_logits = torch.log((1 - main_prob) * self.class_weights[0])
        fg_logits = torch.log(main_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # 标准化原始图像 (CRF需要)
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # 调用CRF
        try:
            crf_output = self.crf(combined_logits, normalized_img)
            if crf_output.size(1) == 2:
                refined_pred = crf_output[:, 1:2, :, :]  # 只取前景
            else: # Fallback
                refined_pred = main_pred
        except Exception:
            refined_pred = main_pred # 如果CRF出错，则回退

        # --- 统一返回字典 ---
        return {
            # 主要预测 (用于最终评估和主损失)
            'main_pred': main_pred,
            'refined_pred': refined_pred,
            'main_logits': main_logits,
            
            # 辅助预测 (用于辅助损失)
            'dt_pred': dt_pred,
            'dt_logits': dt_logits,
            
            # 深度监督预测 (用于可选的监督损失)
            'sup_h_pred': h_pred_sup,
            'sup_l_pred': l_pred_sup,
            'sup_final_pred': final_pred_sup,
        } 