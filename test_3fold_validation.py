#!/usr/bin/env python3
"""
测试3-fold交叉验证功能
验证数据拆分是否正确，无重叠
"""

import os
import sys
from ig_glass.glass_dataloader import GlassCrossValidationLoader

def test_3fold_validation():
    """测试3折交叉验证"""
    print("🧪 测试3折交叉验证功能")
    print("=" * 60)
    
    n_folds = 3
    all_train_files = []
    all_test_files = []
    
    for fold in range(n_folds):
        print(f"\n📂 测试第 {fold+1} 折...")
        
        # 创建训练集
        train_loader = GlassCrossValidationLoader(
            mode='train',
            fold=fold,
            augment_data=False,
            target_size=416,
            n_folds=n_folds
        )
        
        # 创建测试集
        test_loader = GlassCrossValidationLoader(
            mode='test', 
            fold=fold,
            augment_data=False,
            target_size=416,
            n_folds=n_folds
        )
        
        train_files = set(train_loader.inp_files)
        test_files = set(test_loader.inp_files)
        
        # 验证当前fold内部无重叠
        overlap = train_files.intersection(test_files)
        print(f"   Fold {fold+1} 内部重叠: {len(overlap)}张")
        
        if len(overlap) > 0:
            print(f"❌ Fold {fold+1} 存在内部重叠!")
            return False
        
        # 收集所有文件用于跨fold验证
        all_train_files.extend(train_files)
        all_test_files.extend(test_files)
        
        print(f"   训练集: {len(train_files)}张")
        print(f"   测试集: {len(test_files)}张")
        print(f"   总计: {len(train_files) + len(test_files)}张")
    
    # 验证总体数据
    print(f"\n📊 总体验证:")
    all_files = set(all_train_files + all_test_files)
    print(f"   所有唯一文件: {len(all_files)}张")
    print(f"   预期文件数: 约2980张")
    
    # 验证每个文件恰好在3个fold中出现过
    from collections import Counter
    file_counts = Counter(all_train_files + all_test_files)
    
    # 检查是否每个文件都恰好出现3次(在3个fold中各出现1次)
    correct_count = sum(1 for count in file_counts.values() if count == n_folds)
    total_files = len(file_counts)
    
    print(f"   文件出现次数正确的: {correct_count}/{total_files}")
    
    if correct_count == total_files:
        print("✅ 3折交叉验证测试通过!")
        print("🎯 每个样本都会被用于训练和验证")
        return True
    else:
        print("❌ 3折交叉验证测试失败!")
        return False

def test_data_coverage():
    """测试数据覆盖率"""
    print(f"\n🔍 测试数据覆盖率...")
    
    # 使用原始数据加载器获取所有文件
    from ig_glass.glass_dataloader import GlassDataLoader
    original_loader = GlassDataLoader(
        mode='train_gdd',
        augment_data=False,
        target_size=416
    )
    
    all_original_files = set(original_loader.inp_files)
    print(f"   原始数据集: {len(all_original_files)}张")
    
    # 收集3折交叉验证的所有文件
    n_folds = 3
    cv_files = set()
    
    for fold in range(n_folds):
        train_loader = GlassCrossValidationLoader(
            mode='train',
            fold=fold,
            n_folds=n_folds
        )
        test_loader = GlassCrossValidationLoader(
            mode='test',
            fold=fold, 
            n_folds=n_folds
        )
        
        cv_files.update(train_loader.inp_files)
        cv_files.update(test_loader.inp_files)
    
    print(f"   交叉验证覆盖: {len(cv_files)}张")
    
    # 检查覆盖率
    coverage = len(cv_files) / len(all_original_files) * 100
    print(f"   覆盖率: {coverage:.1f}%")
    
    if coverage >= 99.0:  # 允许少量误差
        print("✅ 数据覆盖率测试通过!")
        return True
    else:
        print("❌ 数据覆盖率不足!")
        return False

if __name__ == '__main__':
    print("🚀 开始测试3折交叉验证...")
    
    # 测试3折验证
    fold_success = test_3fold_validation()
    
    # 测试数据覆盖率
    coverage_success = test_data_coverage()
    
    print("\n" + "=" * 60)
    if fold_success and coverage_success:
        print("🎉 所有测试通过! 3折交叉验证可以安全使用")
        print("\n📋 使用方法:")
        print("   python train_scsa.py --use_kfold True --n_folds 3")
        print("   python choose_training_strategy.py  # 智能选择")
        sys.exit(0)
    else:
        print("💥 测试失败! 请检查配置")
        sys.exit(1) 