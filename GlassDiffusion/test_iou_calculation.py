#!/usr/bin/env python3
"""
🧪 IoU计算测试脚本
验证训练代码中IoU计算的正确性，并提供完整的测试功能
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
from torch.utils.data import DataLoader
import argparse
from tqdm import tqdm
import json
from datetime import datetime

# 添加路径
sys.path.append('/home/<USER>/ws/IG_SLAM')

from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.glass_dataloader import GlassDataLoader
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss

def calculate_iou_metrics(pred, gt, threshold=0.5):
    """
    计算详细的IoU指标
    Args:
        pred: 预测结果 [B, 1, H, W] 或 [B, H, W]
        gt: 真值掩码 [B, 1, H, W] 或 [B, H, W]  
        threshold: 二值化阈值
    Returns:
        dict: 包含各种IoU计算方式的结果
    """
    # 确保维度一致
    if pred.dim() == 4 and pred.shape[1] == 1:
        pred = pred.squeeze(1)
    if gt.dim() == 4 and gt.shape[1] == 1:
        gt = gt.squeeze(1)
    
    # 二值化预测
    pred_bin = (pred > threshold).float()
    gt_bin = (gt > threshold).float()
    
    # 方法1: 逐样本计算IoU然后平均 (正确方法)
    batch_ious = []
    for i in range(pred_bin.shape[0]):
        intersection = (pred_bin[i] * gt_bin[i]).sum()
        union = pred_bin[i].sum() + gt_bin[i].sum() - intersection
        iou = intersection / (union + 1e-7)
        batch_ious.append(iou.item())
    method1_iou = np.mean(batch_ious)

    # 方法2: 累积所有像素计算IoU (全局累积方法)
    total_intersection = (pred_bin * gt_bin).sum()
    total_union = pred_bin.sum() + gt_bin.sum() - total_intersection
    method2_iou = (total_intersection / (total_union + 1e-7)).item()
    
    # 方法3: 逐样本计算IoU，加权平均 (按像素数量加权)
    weighted_iou_sum = 0.0
    total_pixels = 0
    for i in range(pred_bin.shape[0]):
        intersection = (pred_bin[i] * gt_bin[i]).sum()
        union = pred_bin[i].sum() + gt_bin[i].sum() - intersection
        iou = intersection / (union + 1e-7)
        pixel_count = pred_bin[i].numel()
        weighted_iou_sum += iou.item() * pixel_count
        total_pixels += pixel_count
    method3_iou = weighted_iou_sum / total_pixels
    
    # 额外指标
    precision = total_intersection / (pred_bin.sum() + 1e-7)
    recall = total_intersection / (gt_bin.sum() + 1e-7)
    f1 = 2 * precision * recall / (precision + recall + 1e-7)
    
    return {
        'method1_iou': method1_iou,  # 错误方法
        'method2_iou': method2_iou,  # 正确方法
        'method3_iou': method3_iou,  # 加权方法
        'precision': precision.item(),
        'recall': recall.item(),
        'f1': f1.item(),
        'total_intersection': total_intersection.item(),
        'total_union': total_union.item()
    }

def test_model_on_dataset(model_path, data_dir, batch_size=6, img_size=416, device='cuda'):
    """
    在数据集上测试模型并计算IoU
    """
    print(f"🧪 开始模型测试...")
    print(f"   模型路径: {model_path}")
    print(f"   数据目录: {data_dir}")
    print(f"   批次大小: {batch_size}")
    print(f"   图像尺寸: {img_size}")
    
    # 加载模型
    device = torch.device(device if torch.cuda.is_available() else 'cpu')
    model = RTGlassNet(backbone_type='inceptionnext_base').to(device)
    
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=device)
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
        else:
            model.load_state_dict(checkpoint)
        print(f"✅ 模型加载成功")
    else:
        print(f"⚠️ 模型文件不存在，使用随机初始化权重进行测试")
    
    # 创建测试数据集
    test_dataset = GlassDataLoader(
        data_dir=data_dir,
        split='valid',  # 使用验证集
        target_size=(img_size, img_size),
        split_ratio=0.8,
        random_seed=42,
        glass_aug_config=None  # 测试时不使用增强
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )
    
    print(f"📊 测试数据: {len(test_dataset)}张图像")
    
    # 测试模型
    model.eval()
    all_metrics = []
    total_intersection = 0.0
    total_union = 0.0
    
    with torch.no_grad():
        for batch_idx, (images, gt_masks, dt_maps) in enumerate(tqdm(test_loader, desc="测试中")):
            images = images.to(device)
            gt_masks = gt_masks.to(device)
            
            # 前向传播
            outputs = model(images)
            main_pred = outputs['main_pred']
            
            # 计算各种IoU指标
            metrics = calculate_iou_metrics(main_pred, gt_masks)
            all_metrics.append(metrics)
            
            # 累积全局IoU计算
            pred_bin = (main_pred > 0.5).float()
            intersection = (pred_bin * gt_masks).sum()
            union = pred_bin.sum() + gt_masks.sum() - intersection
            total_intersection += intersection.item()
            total_union += union.item()
    
    # 汇总结果
    global_iou = total_intersection / (total_union + 1e-7)
    
    # 计算各方法的平均值
    method1_avg = np.mean([m['method1_iou'] for m in all_metrics])
    method2_avg = np.mean([m['method2_iou'] for m in all_metrics])
    method3_avg = np.mean([m['method3_iou'] for m in all_metrics])
    precision_avg = np.mean([m['precision'] for m in all_metrics])
    recall_avg = np.mean([m['recall'] for m in all_metrics])
    f1_avg = np.mean([m['f1'] for m in all_metrics])
    
    results = {
        'test_info': {
            'model_path': model_path,
            'data_dir': data_dir,
            'num_samples': len(test_dataset),
            'batch_size': batch_size,
            'img_size': img_size,
            'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        },
        'iou_methods': {
            'method1_batch_avg': method1_avg,  # 正确方法：逐样本平均
            'method2_batch_avg': method2_avg,  # 全局累积方法
            'method3_weighted_avg': method3_avg,  # 加权平均
            'global_cumulative': global_iou  # 全局累积
        },
        'other_metrics': {
            'precision': precision_avg,
            'recall': recall_avg,
            'f1': f1_avg
        }
    }
    
    return results

def print_results(results):
    """打印测试结果"""
    print(f"\n🎯 IoU计算测试结果")
    print(f"=" * 60)
    print(f"测试信息:")
    print(f"  - 样本数量: {results['test_info']['num_samples']}")
    print(f"  - 批次大小: {results['test_info']['batch_size']}")
    print(f"  - 图像尺寸: {results['test_info']['img_size']}")
    print(f"  - 测试时间: {results['test_info']['test_time']}")
    
    print(f"\nIoU计算方法对比:")
    iou_methods = results['iou_methods']
    print(f"  - 方法1 (逐样本平均): {iou_methods['method1_batch_avg']:.6f} ✅ 推荐")
    print(f"  - 方法2 (全局累积):   {iou_methods['method2_batch_avg']:.6f}")
    print(f"  - 方法3 (加权平均):   {iou_methods['method3_weighted_avg']:.6f}")
    print(f"  - 全局累积验证:      {iou_methods['global_cumulative']:.6f}")
    
    print(f"\n其他指标:")
    other = results['other_metrics']
    print(f"  - Precision: {other['precision']:.6f}")
    print(f"  - Recall:    {other['recall']:.6f}")
    print(f"  - F1-Score:  {other['f1']:.6f}")
    
    # 分析差异
    global_iou = iou_methods['global_cumulative']
    method1_diff = abs(iou_methods['method1_batch_avg'] - global_iou)
    method2_diff = abs(iou_methods['method2_batch_avg'] - global_iou)
    
    print(f"\n📊 方法差异分析:")
    print(f"  - 方法1与全局差异: {method1_diff:.6f}")
    print(f"  - 方法2与全局差异: {method2_diff:.6f}")
    
    if method1_diff > 0.001:
        print(f"  ⚠️ 方法1存在明显偏差，建议使用全局累积方法")
    else:
        print(f"  ✅ 各方法结果基本一致")

def main():
    parser = argparse.ArgumentParser(description='🧪 IoU计算测试')
    parser.add_argument('--model_path', default='./ckpt/RTGlassNet_KFold/weights/BEST_fold1_epoch080_iou0.8500.pth', 
                       help='模型权重路径')
    parser.add_argument('--data_dir', default='/home/<USER>/ws/IG_SLAM/', help='数据目录')
    parser.add_argument('--batch_size', default=6, type=int, help='批次大小')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--device', default='cuda', help='设备')
    parser.add_argument('--save_results', default='./test_iou_results.json', help='结果保存路径')
    
    args = parser.parse_args()
    
    # 运行测试
    results = test_model_on_dataset(
        model_path=args.model_path,
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        img_size=args.img_size,
        device=args.device
    )
    
    # 打印结果
    print_results(results)
    
    # 保存结果
    if args.save_results:
        with open(args.save_results, 'w') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 结果已保存到: {args.save_results}")

if __name__ == '__main__':
    main()
