#!/usr/bin/env python3
"""
🔍 对比main_pred vs refined_pred的性能差异
验证训练时和测试时使用不同预测输出的影响
"""

import os
import warnings
import cv2
import numpy as np
import torch
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',
    'scale': 384,
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理
img_transform = <PERSON><PERSON>([
    <PERSON><PERSON>(height=args['scale'], width=args['scale']),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ToTensorV2()
])

def analyze_prediction_outputs(outputs, image_file):
    """分析模型的不同输出"""
    main_pred = outputs['main_pred']
    refined_pred = outputs.get('refined_pred')
    main_logits = outputs.get('main_logits')
    
    print(f"\n🔍 分析 {image_file}:")
    print(f"  main_pred: shape={main_pred.shape}, range=[{main_pred.min():.6f}, {main_pred.max():.6f}], mean={main_pred.mean():.6f}")
    
    if refined_pred is not None:
        print(f"  refined_pred: shape={refined_pred.shape}, range=[{refined_pred.min():.6f}, {refined_pred.max():.6f}], mean={refined_pred.mean():.6f}")
        
        # 检查refined_pred是否异常
        if torch.isnan(refined_pred).any():
            print(f"  ⚠️ refined_pred包含NaN值")
        if torch.isinf(refined_pred).any():
            print(f"  ⚠️ refined_pred包含Inf值")
        if refined_pred.max() - refined_pred.min() < 1e-5:
            print(f"  ⚠️ refined_pred几乎为常数")
        if refined_pred.mean() < 1e-4:
            print(f"  ⚠️ refined_pred均值过小")
    
    if main_logits is not None:
        print(f"  main_logits: shape={main_logits.shape}, range=[{main_logits.min():.6f}, {main_logits.max():.6f}], mean={main_logits.mean():.6f}")

def compare_predictions(image_folder, gt_folder, model, num_samples=10):
    """对比main_pred和refined_pred的性能"""
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files.sort()
    
    # 只测试前num_samples张图像
    image_files = image_files[:num_samples]
    
    main_results = []
    refined_results = []
    
    print(f"🔍 对比测试 {len(image_files)} 张图像...")
    
    for i, image_file in enumerate(tqdm(image_files, desc="对比测试")):
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        
        # 读取图像和GT
        img_rgb = cv2.imread(image_path)
        if img_rgb is None:
            continue
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
        
        if not os.path.exists(gt_path):
            continue
        gt_mask_u8 = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        if gt_mask_u8 is None:
            continue
        
        # 预处理
        transformed = img_transform(image=img_rgb)
        img_var = transformed['image'].unsqueeze(0).cuda(device_ids[0])
        
        # 处理GT
        gt_resized = cv2.resize(gt_mask_u8, (args['scale'], args['scale']))
        gt_float = (gt_resized / 255.0).astype(np.float32)
        
        # 模型推理
        with torch.no_grad():
            outputs = model(img_var)
            
            # 分析输出（只对前3张图像）
            if i < 3:
                analyze_prediction_outputs(outputs, image_file)
            
            # 提取预测
            main_pred = outputs['main_pred'].squeeze().cpu().numpy()
            refined_pred = outputs.get('refined_pred')
            
            # 测试main_pred
            main_binary = (main_pred > args['glass_threshold']).astype(np.float32)
            main_iou = compute_iou(main_binary, gt_float)
            main_acc = compute_acc(main_binary, gt_float)
            main_precision, main_recall = compute_precision_recall(main_binary, gt_float)
            main_f1 = compute_fmeasure(main_precision, main_recall)
            main_mae = compute_mae(main_binary, gt_float)
            
            main_results.append({
                'file': image_file,
                'iou': main_iou,
                'acc': main_acc,
                'f1': main_f1,
                'mae': main_mae,
                'precision': main_precision,
                'recall': main_recall
            })
            
            # 测试refined_pred（如果可用）
            if refined_pred is not None:
                refined_pred_np = refined_pred.squeeze().cpu().numpy()
                
                # 检查refined_pred是否有效
                if not (np.isnan(refined_pred_np).any() or np.isinf(refined_pred_np).any()):
                    if refined_pred_np.max() - refined_pred_np.min() > 1e-5:
                        refined_binary = (refined_pred_np > args['glass_threshold']).astype(np.float32)
                        refined_iou = compute_iou(refined_binary, gt_float)
                        refined_acc = compute_acc(refined_binary, gt_float)
                        refined_precision, refined_recall = compute_precision_recall(refined_binary, gt_float)
                        refined_f1 = compute_fmeasure(refined_precision, refined_recall)
                        refined_mae = compute_mae(refined_binary, gt_float)
                        
                        refined_results.append({
                            'file': image_file,
                            'iou': refined_iou,
                            'acc': refined_acc,
                            'f1': refined_f1,
                            'mae': refined_mae,
                            'precision': refined_precision,
                            'recall': refined_recall
                        })
                    else:
                        print(f"  ⚠️ {image_file}: refined_pred无效（常数值）")
                        refined_results.append(None)
                else:
                    print(f"  ⚠️ {image_file}: refined_pred包含异常值")
                    refined_results.append(None)
            else:
                refined_results.append(None)
    
    return main_results, refined_results

def print_comparison_results(main_results, refined_results):
    """打印对比结果"""
    print(f"\n📊 对比结果分析:")
    print("=" * 80)
    
    # 计算main_pred的平均指标
    main_metrics = {
        'iou': np.mean([r['iou'] for r in main_results]),
        'acc': np.mean([r['acc'] for r in main_results]),
        'f1': np.mean([r['f1'] for r in main_results]),
        'mae': np.mean([r['mae'] for r in main_results]),
        'precision': np.mean([r['precision'] for r in main_results]),
        'recall': np.mean([r['recall'] for r in main_results])
    }
    
    # 计算refined_pred的平均指标（排除None值）
    valid_refined = [r for r in refined_results if r is not None]
    invalid_count = len([r for r in refined_results if r is None])
    
    if valid_refined:
        refined_metrics = {
            'iou': np.mean([r['iou'] for r in valid_refined]),
            'acc': np.mean([r['acc'] for r in valid_refined]),
            'f1': np.mean([r['f1'] for r in valid_refined]),
            'mae': np.mean([r['mae'] for r in valid_refined]),
            'precision': np.mean([r['precision'] for r in valid_refined]),
            'recall': np.mean([r['recall'] for r in valid_refined])
        }
    else:
        refined_metrics = None
    
    print(f"🎯 main_pred (训练时使用的预测):")
    print(f"  IoU:       {main_metrics['iou']:.4f}")
    print(f"  Accuracy:  {main_metrics['acc']:.4f}")
    print(f"  F1-Score:  {main_metrics['f1']:.4f}")
    print(f"  MAE:       {main_metrics['mae']:.4f}")
    print(f"  Precision: {main_metrics['precision']:.4f}")
    print(f"  Recall:    {main_metrics['recall']:.4f}")
    
    print(f"\n🔧 refined_pred (CRF后处理，测试时优先使用):")
    if refined_metrics:
        print(f"  IoU:       {refined_metrics['iou']:.4f}")
        print(f"  Accuracy:  {refined_metrics['acc']:.4f}")
        print(f"  F1-Score:  {refined_metrics['f1']:.4f}")
        print(f"  MAE:       {refined_metrics['mae']:.4f}")
        print(f"  Precision: {refined_metrics['precision']:.4f}")
        print(f"  Recall:    {refined_metrics['recall']:.4f}")
        print(f"  有效样本:  {len(valid_refined)}/{len(refined_results)}")
        print(f"  无效样本:  {invalid_count}")
        
        # 计算差异
        print(f"\n📈 性能差异 (refined - main):")
        for metric in ['iou', 'acc', 'f1', 'precision', 'recall']:
            diff = refined_metrics[metric] - main_metrics[metric]
            direction = "↑" if diff > 0 else "↓" if diff < 0 else "="
            print(f"  {metric.upper()}: {diff:+.4f} {direction}")
        
        mae_diff = refined_metrics['mae'] - main_metrics['mae']
        mae_direction = "↓" if mae_diff < 0 else "↑" if mae_diff > 0 else "="
        print(f"  MAE: {mae_diff:+.4f} {mae_direction}")
        
    else:
        print(f"  ❌ 所有refined_pred都无效！")
        print(f"  无效样本: {invalid_count}/{len(refined_results)}")
    
    print(f"\n🎯 结论:")
    if refined_metrics and refined_metrics['iou'] > main_metrics['iou']:
        print(f"  ✅ CRF后处理提升了性能")
    elif refined_metrics and refined_metrics['iou'] < main_metrics['iou']:
        print(f"  ❌ CRF后处理降低了性能")
        print(f"  💡 建议测试时也使用main_pred")
    else:
        print(f"  ⚠️ CRF后处理大部分失效")
        print(f"  💡 强烈建议测试时使用main_pred")

def main():
    print("🔍 main_pred vs refined_pred 性能对比测试")
    print("=" * 60)
    
    # 加载模型
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"✅ 加载模型: {model_path}")
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    model = RTGlassNet(
        backbone_type=backbone_type, 
        crf_iter=args['crf_iter'], 
        crf_bilateral_weight=args['crf_bilateral_weight']
    )
    
    if 'model' in checkpoint:
        model.load_state_dict(checkpoint['model'], strict=False)
    else:
        model.load_state_dict(checkpoint, strict=False)
    
    model.cuda(device_ids[0])
    model.eval()
    
    # 测试数据路径
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    image_folder = os.path.join(data_path, "image")
    gt_folder = os.path.join(data_path, "mask")
    
    # 运行对比测试
    main_results, refined_results = compare_predictions(image_folder, gt_folder, model, num_samples=20)
    
    # 打印结果
    print_comparison_results(main_results, refined_results)
    
    print(f"\n💡 修复建议:")
    print(f"1. 如果refined_pred性能更差，修改test_gdd.py优先使用main_pred")
    print(f"2. 如果refined_pred大量失效，考虑禁用CRF或调整CRF参数")
    print(f"3. 确保训练和测试使用相同的预测输出")

if __name__ == '__main__':
    main()
