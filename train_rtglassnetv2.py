"""
RTGlassNetv2 训练脚本 V3 (最终版)
- 移植了gdnet_scsa的完整训练策略：
  1. 差分学习率 (Differential Learning Rate)
  2. SGD优化器 + ReduceLROnPlateau自适应调度器
  3. EarlyStopping早停机制
- 使用V3版数据加载器，确保高强度且保护结构的增强
"""
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import os

from GlassDiffusion.models.RTGlassNetv2 import RTGlassNetv2
from GlassDiffusion.models.loss_rtgv2 import RTGlassNetv2Loss
from GlassDiffusion.glass_dataloader import GlassDataset

class EarlyStopping:
    """早停机制，防止过拟合"""
    def __init__(self, patience=10, min_delta=0.001, verbose=True):
        self.patience = patience
        self.min_delta = min_delta
        self.verbose = verbose
        self.counter = 0
        self.best_score = None
        self.early_stop = False

    def __call__(self, val_score):
        if self.best_score is None:
            self.best_score = val_score
        elif val_score < self.best_score + self.min_delta:
            self.counter += 1
            if self.verbose:
                print(f"[EarlyStopping] 计数: {self.counter} / {self.patience}")
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = val_score
            self.counter = 0

class Engine:
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_save_path = os.path.join(self.args.base_save_path, 'RTGlassNetv2', 'weights')
        os.makedirs(self.model_save_path, exist_ok=True)

    def run(self):
        print("🚀 开始RTGlassNetv2 V3版训练...")
        print(f"📱 使用设备: {self.device}")

        # 1. 数据加载器 (使用V3版，配置高强度增强)
        train_dataset = GlassDataset(
            data_dir=self.args.data_dir, split='train', target_size=(self.args.img_size, self.args.img_size),
            aug_config='heavy'
        )
        val_dataset = GlassDataset(
            data_dir=self.args.data_dir, split='valid', target_size=(self.args.img_size, self.args.img_size)
        )
        train_loader = DataLoader(train_dataset, batch_size=self.args.bs, shuffle=True, num_workers=self.args.n_worker, pin_memory=True)
        val_loader = DataLoader(val_dataset, batch_size=self.args.bs, shuffle=False, num_workers=self.args.n_worker)

        # 2. 模型
        model = RTGlassNetv2(backbone_type=self.args.backbone).to(self.device)

        # 3. 损失函数
        criterion = RTGlassNetv2Loss(
            focal_weight=0.5, iou_weight=0.3, edge_weight=0.2, dt_weight=0.1
        )

        # 4. 优化器与调度器 (移植自gdnet_scsa)
        backbone_params = list(model.feature_extractor.parameters())
        decoder_params = [p for n, p in model.named_parameters() if 'feature_extractor' not in n]
        
        optimizer = optim.SGD([
            {'params': backbone_params, 'lr': self.args.lr_backbone, 'momentum': 0.9, 'weight_decay': self.args.wd},
            {'params': decoder_params, 'lr': self.args.lr_decoder, 'momentum': 0.9, 'weight_decay': self.args.wd}
        ])
        
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=5, verbose=True)

        # 5. 早停机制
        early_stopper = EarlyStopping(patience=15, verbose=True)

        # 6. 训练循环
        best_iou = 0.0
        for epoch in range(1, self.args.epochs + 1):
            self.train_one_epoch(model, train_loader, optimizer, criterion, epoch)
            
            val_iou, val_loss = self.validate(model, val_loader, criterion, epoch)
            
            scheduler.step(val_iou)
            
            if val_iou > best_iou:
                best_iou = val_iou
                print(f"🏆 Epoch {epoch}: 新的最佳IoU: {best_iou:.4f}")
                torch.save(model.state_dict(), os.path.join(self.model_save_path, f'BEST_epoch{epoch:03d}_iou{best_iou:.4f}.pth'))

            early_stopper(val_iou)
            if early_stopper.early_stop:
                print("🛑 早停触发!")
                break
        
        print("🎉 训练完成!")

    def train_one_epoch(self, model, loader, optimizer, criterion, epoch):
        model.train()
        running_loss = 0.0
        progress = tqdm(loader, desc=f"训练 Epoch {epoch}")

        for images, masks, dt_maps in progress:
            images, masks, dt_maps = images.to(self.device), masks.to(self.device), dt_maps.to(self.device)
            
            optimizer.zero_grad()
            predictions = model(images)
            total_loss, loss_dict = criterion(predictions, masks, dt_maps)
            total_loss.backward()
            optimizer.step()
            
            running_loss += total_loss.item()
            progress.set_postfix(loss=f"{total_loss.item():.4f}", avg_loss=f"{running_loss / (progress.n + 1):.4f}")

    def validate(self, model, loader, criterion, epoch):
        model.eval()
        total_iou = 0.0
        total_loss = 0.0
        progress = tqdm(loader, desc=f"验证 Epoch {epoch}")

        with torch.no_grad():
            for images, masks, dt_maps in progress:
                images, masks, dt_maps = images.to(self.device), masks.to(self.device), dt_maps.to(self.device)
                
                predictions = model(images)
                loss, loss_dict = criterion(predictions, masks, dt_maps)
                total_loss += loss.item()

                # 使用最终的refined_pred计算IoU
                pred_mask = (predictions['refined_pred'] > 0.5).float()
                intersection = (pred_mask * masks).sum()
                union = pred_mask.sum() + masks.sum() - intersection
                iou = (intersection + 1e-7) / (union + 1e-7)
                total_iou += iou.item()
                
                progress.set_postfix(loss=f"{loss.item():.4f}", iou=f"{iou.item():.4f}")

        avg_iou = total_iou / len(loader)
        avg_loss = total_loss / len(loader)
        print(f"验证结果 Epoch {epoch}: Avg Loss: {avg_loss:.4f}, Avg IoU: {avg_iou:.4f}")
        return avg_iou, avg_loss

def main():
    import argparse
    parser = argparse.ArgumentParser(description="RTGlassNetv2 最终版训练脚本")
    parser.add_argument('--data_dir', type=str, default='/home/<USER>/ws/IG_SLAM/', help='数据集根目录')
    parser.add_argument('--backbone', type=str, default='inceptionnext_base', help='骨干网络')
    parser.add_argument('--epochs', type=int, default=200, help='训练轮数')
    parser.add_argument('--bs', type=int, default=4, help='批次大小')
    parser.add_argument('--lr_backbone', type=float, default=5e-5, help='Backbone学习率')
    parser.add_argument('--lr_decoder', type=float, default=5e-4, help='Decoder学习率')
    parser.add_argument('--wd', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--img_size', type=int, default=416, help='图像尺寸')
    parser.add_argument('--n_worker', type=int, default=4, help='工作线程数')
    parser.add_argument('--base_save_path', type=str, default='./ckpt', help='模型保存路径')
    args = parser.parse_args()
    
    engine = Engine(args)
    engine.run()

if __name__ == '__main__':
    main()
