# RTGlassNetv2: Real-Time Glass Segmentation Network

## Network Architecture

```mermaid
graph TB
    subgraph "Input"
        A[RGB Image<br/>3×H×W]
    end
    
    subgraph "Backbone: InceptionNeXt"
        B1[C2: 96/128×H/4×W/4]
        B2[C3: 192/256×H/8×W/8]
        B3[C4: 384/512×H/16×W/16]
        B4[C5: 768/1024×H/32×W/32]
    end
    
    subgraph "Multi-Scale Feature Processing"
        C1[LightLCFI-C2<br/>Depthwise Separable Conv<br/>+ SCSA Attention]
        C2[LightLCFI-C3<br/>Depthwise Separable Conv<br/>+ SCSA Attention]
        C3[LightLCFI-C4<br/>Depthwise Separable Conv<br/>+ SCSA Attention]
        C4[LightLCFI-C5<br/>Depthwise Separable Conv<br/>+ SCSA Attention]
    end
    
    subgraph "Hierarchical Feature Fusion"
        D1[High-Level Fusion<br/>C5↑ + C4 + C3↓<br/>SCSA + Edge Enhancement]
        D2[Low-Level Fusion<br/>C2 + High-Level Features<br/>Attention-Guided]
    end
    
    subgraph "Final Feature Integration"
        E[Final Fusion<br/>High↑ + Low<br/>SCSA Attention]
    end
    
    subgraph "Prediction Heads"
        F1[Main Branch<br/>Shared Conv + 1×1 Conv]
        F2[Deep Supervision H<br/>3×3 Conv]
        F3[Deep Supervision L<br/>3×3 Conv]
        F4[Deep Supervision Final<br/>3×3 Conv]
    end
    
    subgraph "Post-Processing"
        G[SimplifiedDiffCRF<br/>Bilateral + Gaussian<br/>Trainable Parameters]
    end
    
    subgraph "Output"
        H[Refined Prediction<br/>1×H×W]
    end
    
    A --> B1
    A --> B2
    A --> B3
    A --> B4
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C4 --> D1
    C3 --> D1
    C2 --> D1
    C1 --> D2
    D1 --> D2
    
    D1 --> E
    D2 --> E
    
    E --> F1
    E --> F2
    E --> F3
    E --> F4
    
    F1 --> G
    G --> H
    
    style A fill:#e1f5fe
    style H fill:#e8f5e8
    style G fill:#fff3e0
    style D1 fill:#f3e5f5
    style D2 fill:#f3e5f5
    style E fill:#e0f2f1
```

## Key Components

### 1. LightLCFI (Lightweight Large Context Feature Integration)
- **Depthwise Separable Convolutions**: Reduce computational complexity
- **Multi-scale Dilated Convolutions**: Capture context at different scales
- **SCSA Attention**: Spatial-channel attention mechanism
- **Progressive Feature Integration**: P1→P2→P3→P4 cascade

### 2. SCSA (Spatial-Channel Self-Attention)
- **Multi-head Attention**: 8 attention heads
- **Window-based Processing**: 7×7 window size
- **Group Kernel Sizes**: [3, 5, 7, 9] for multi-scale attention
- **Gated Mechanism**: Sigmoid gating for adaptive feature selection

### 3. Hierarchical Feature Fusion
- **High-Level Fusion**: C5↑ + C4 + C3↓ with edge enhancement
- **Low-Level Fusion**: C2 + high-level features with attention guidance
- **Final Integration**: Multi-scale feature aggregation

### 4. SimplifiedDiffCRF
- **Bilateral Filtering**: Spatial and color similarity
- **Gaussian Smoothing**: Spatial consistency
- **Trainable Parameters**: End-to-end optimization
- **Efficient Implementation**: Reduced computational overhead

## Loss Function Architecture

```mermaid
graph TB
    subgraph "Model Predictions"
        P1[Main Logits]
        P2[Refined Prediction<br/>CRF Output]
        P3[Deep Supervision H]
        P4[Deep Supervision L]
        P5[Deep Supervision Final]
    end
    
    subgraph "Ground Truth"
        GT[Binary Mask<br/>0/1 Values]
    end
    
    subgraph "Composite Loss Components"
        L1[Edge-Aware Focal Loss<br/>α=0.25, γ=2.0<br/>Edge Weight=1.3]
        L2[Light IoU Loss<br/>Intersection/Union]
        L3[Edge-Aware BCE Loss<br/>Laplacian Edge Weighting]
    end
    
    subgraph "Loss Computation"
        C1[Composite Loss<br/>Focal + IoU + Edge]
        C2[Probability Loss<br/>Logits Conversion]
    end
    
    subgraph "Weighted Loss Aggregation"
        W1[Refined Loss<br/>Weight: 1.0]
        W2[Main Loss<br/>Weight: 0.8]
        W3[Final Sup Loss<br/>Weight: 0.4]
        W4[H Sup Loss<br/>Weight: 0.2]
        W5[L Sup Loss<br/>Weight: 0.2]
    end
    
    subgraph "Total Loss"
        TL[Total Loss<br/>Weighted Sum]
    end
    
    P1 --> C1
    P2 --> C2
    P3 --> C2
    P4 --> C2
    P5 --> C2
    
    GT --> L1
    GT --> L2
    GT --> L3
    
    L1 --> C1
    L2 --> C1
    L3 --> C1
    
    C1 --> W1
    C1 --> W2
    C2 --> W3
    C2 --> W4
    C2 --> W5
    
    W1 --> TL
    W2 --> TL
    W3 --> TL
    W4 --> TL
    W5 --> TL
    
    style GT fill:#e8f5e8
    style TL fill:#ffebee
    style L1 fill:#e3f2fd
    style L2 fill:#e3f2fd
    style L3 fill:#e3f2fd
```

## Loss Function Details

### 1. Edge-Aware Focal Loss
- **Focal Parameters**: α=0.25, γ=2.0 for class imbalance
- **Edge Enhancement**: Laplacian kernel-based edge weighting
- **Edge Weight**: 1.3× for boundary regions

### 2. Light IoU Loss
- **Direct IoU Optimization**: Intersection over Union
- **Numerical Stability**: Epsilon smoothing (1e-7)
- **Boundary Focus**: Emphasizes segmentation boundaries

### 3. Edge-Aware BCE Loss
- **Binary Cross-Entropy**: Standard classification loss
- **Edge Weighting**: Laplacian-based boundary emphasis
- **Sigmoid Activation**: Probability output

### 4. Deep Supervision Strategy
- **Multi-level Supervision**: H, L, and Final level predictions
- **Progressive Weighting**: Higher weights for refined outputs
- **Gradient Flow**: Improved training stability

## Technical Specifications

| Component | Parameters | Output Size | Key Features |
|-----------|------------|-------------|--------------|
| Backbone | InceptionNeXt-T/S/B | Multi-scale | Pre-trained ImageNet |
| LightLCFI | Depthwise Separable | Reduced channels | Multi-dilation |
| SCSA | 8 heads, 7×7 window | Attention maps | Group kernels |
| CRF | 3 iterations | Refined prediction | Trainable |
| Total Params | ~50M (Tiny) / ~100M (Base) | 1×H×W | Real-time capable |

## Performance Characteristics

- **Real-time Processing**: 30+ FPS on modern GPUs
- **Memory Efficient**: Depthwise separable convolutions
- **Multi-scale Context**: Hierarchical feature fusion
- **Boundary Accuracy**: Edge-aware loss functions
- **End-to-end Training**: CRF integration 