\documentclass[11pt]{article}
\usepackage{tikz}
\usepackage{xcolor}
\usepackage{amsmath}
\usepackage{amssymb}
\usetikzlibrary{shapes,arrows,positioning,fit,backgrounds}

\begin{document}

% 定义颜色
\definecolor{inputcolor}{RGB}{225,245,254}
\definecolor{backbonecolor}{RGB}{232,245,233}
\definecolor{attentioncolor}{RGB}{243,229,245}
\definecolor{fusioncolor}{RGB}{224,242,241}
\definecolor{outputcolor}{RGB}{255,243,224}
\definecolor{losscolor}{RGB}{227,242,253}

\begin{figure}[htbp]
\centering
\begin{tikzpicture}[
    node distance=1.5cm and 2cm,
    box/.style={rectangle, draw, rounded corners, minimum width=2cm, minimum height=0.8cm, align=center},
    bigbox/.style={rectangle, draw, rounded corners, minimum width=3cm, minimum height=1.2cm, align=center},
    lossbox/.style={rectangle, draw, rounded corners, minimum width=2.5cm, minimum height=0.6cm, align=center},
    arrow/.style={->, thick},
    group/.style={rectangle, draw, dashed, rounded corners, inner sep=0.3cm}
]

% 输入
\node[box, fill=inputcolor] (input) {RGB Image\\$3 \times H \times W$};

% Backbone
\node[group, fit=(input), label=above:{\textbf{InceptionNeXt Backbone}}] (backbone_group) {};
\node[box, fill=backbonecolor, below=of input] (c2) {C2: $96/128 \times \frac{H}{4} \times \frac{W}{4}$};
\node[box, fill=backbonecolor, below=of c2] (c3) {C3: $192/256 \times \frac{H}{8} \times \frac{W}{8}$};
\node[box, fill=backbonecolor, below=of c3] (c4) {C4: $384/512 \times \frac{H}{16} \times \frac{W}{16}$};
\node[box, fill=backbonecolor, below=of c4] (c5) {C5: $768/1024 \times \frac{H}{32} \times \frac{W}{32}$};

% LightLCFI模块
\node[group, right=of backbone_group, label=above:{\textbf{LightLCFI Processing}}] (lcfi_group) {};
\node[box, fill=attentioncolor, right=of c2] (lcfi2) {LightLCFI-C2\\Depthwise + SCSA};
\node[box, fill=attentioncolor, right=of c3] (lcfi3) {LightLCFI-C3\\Depthwise + SCSA};
\node[box, fill=attentioncolor, right=of c4] (lcfi4) {LightLCFI-C4\\Depthwise + SCSA};
\node[box, fill=attentioncolor, right=of c5] (lcfi5) {LightLCFI-C5\\Depthwise + SCSA};

% 特征融合
\node[group, right=of lcfi_group, label=above:{\textbf{Hierarchical Fusion}}] (fusion_group) {};
\node[bigbox, fill=fusioncolor, right=of lcfi4] (high_fusion) {High-Level Fusion\\$C5\uparrow + C4 + C3\downarrow$\\SCSA + Edge Enhancement};
\node[bigbox, fill=fusioncolor, below=of high_fusion] (low_fusion) {Low-Level Fusion\\$C2 + \text{High Features}$\\Attention-Guided};

% 最终融合
\node[group, right=of fusion_group, label=above:{\textbf{Final Integration}}] (final_group) {};
\node[bigbox, fill=fusioncolor, right=of high_fusion] (final_fusion) {Final Fusion\\$\text{High}\uparrow + \text{Low}$\\SCSA Attention};

% 预测头
\node[group, right=of final_group, label=above:{\textbf{Multi-Head Prediction}}] (pred_group) {};
\node[box, fill=outputcolor, right=of final_fusion] (main_pred) {Main Branch\\Shared Conv + $1\times1$};
\node[box, fill=outputcolor, below=of main_pred] (deep_h) {Deep Sup H\\$3\times3$ Conv};
\node[box, fill=outputcolor, below=of deep_h] (deep_l) {Deep Sup L\\$3\times3$ Conv};
\node[box, fill=outputcolor, below=of deep_l] (deep_final) {Deep Sup Final\\$3\times3$ Conv};

% CRF后处理
\node[group, right=of pred_group, label=above:{\textbf{Post-Processing}}] (post_group) {};
\node[bigbox, fill=outputcolor, right=of main_pred] (crf) {SimplifiedDiffCRF\\Bilateral + Gaussian\\Trainable};

% 输出
\node[box, fill=outputcolor, right=of crf] (output) {Refined Prediction\\$1 \times H \times W$};

% 连接线
\draw[arrow] (input) -- (c2);
\draw[arrow] (input) -- (c3);
\draw[arrow] (input) -- (c4);
\draw[arrow] (input) -- (c5);

\draw[arrow] (c2) -- (lcfi2);
\draw[arrow] (c3) -- (lcfi3);
\draw[arrow] (c4) -- (lcfi4);
\draw[arrow] (c5) -- (lcfi5);

\draw[arrow] (lcfi5) -- (high_fusion);
\draw[arrow] (lcfi4) -- (high_fusion);
\draw[arrow] (lcfi3) -- (high_fusion);
\draw[arrow] (lcfi2) -- (low_fusion);
\draw[arrow] (high_fusion) -- (low_fusion);

\draw[arrow] (high_fusion) -- (final_fusion);
\draw[arrow] (low_fusion) -- (final_fusion);

\draw[arrow] (final_fusion) -- (main_pred);
\draw[arrow] (final_fusion) -- (deep_h);
\draw[arrow] (final_fusion) -- (deep_l);
\draw[arrow] (final_fusion) -- (deep_final);

\draw[arrow] (main_pred) -- (crf);
\draw[arrow] (crf) -- (output);

\end{tikzpicture}
\caption{RTGlassNetv2网络架构图。网络采用InceptionNeXt作为骨干网络，通过LightLCFI模块进行多尺度特征处理，使用SCSA注意力机制进行特征融合，最后通过SimplifiedDiffCRF进行后处理优化。}
\label{fig:rtglassnetv2_architecture}
\end{figure}

% 损失函数架构图
\begin{figure}[htbp]
\centering
\begin{tikzpicture}[
    node distance=1.2cm and 1.5cm,
    box/.style={rectangle, draw, rounded corners, minimum width=2cm, minimum height=0.6cm, align=center},
    bigbox/.style={rectangle, draw, rounded corners, minimum width=2.5cm, minimum height=0.8cm, align=center},
    lossbox/.style={rectangle, draw, rounded corners, minimum width=2.2cm, minimum height=0.5cm, align=center},
    arrow/.style={->, thick},
    group/.style={rectangle, draw, dashed, rounded corners, inner sep=0.2cm}
]

% 模型预测
\node[group, label=above:{\textbf{Model Predictions}}] (pred_group) {
    \node[box, fill=losscolor] (main_logits) {Main Logits};
    \node[box, fill=losscolor, below=of main_logits] (refined) {Refined (CRF)};
    \node[box, fill=losscolor, below=of refined] (deep_h) {Deep Sup H};
    \node[box, fill=losscolor, below=of deep_h] (deep_l) {Deep Sup L};
    \node[box, fill=losscolor, below=of deep_l] (deep_final) {Deep Sup Final};
};

% Ground Truth
\node[box, fill=backbonecolor, left=of refined] (gt) {Ground Truth\\Binary Mask};

% 损失组件
\node[group, right=of pred_group, label=above:{\textbf{Loss Components}}] (loss_group) {
    \node[lossbox, fill=attentioncolor] (focal) {Edge-Aware Focal\\$\alpha=0.25, \gamma=2.0$};
    \node[lossbox, fill=attentioncolor, below=of focal] (iou) {Light IoU Loss\\Intersection/Union};
    \node[lossbox, fill=attentioncolor, below=of iou] (edge) {Edge-Aware BCE\\Laplacian Weighting};
};

% 损失计算
\node[group, right=of loss_group, label=above:{\textbf{Loss Computation}}] (comp_group) {
    \node[bigbox, fill=fusioncolor] (composite) {Composite Loss\\Focal + IoU + Edge};
    \node[bigbox, fill=fusioncolor, below=of composite] (prob_loss) {Probability Loss\\Logits Conversion};
};

% 加权聚合
\node[group, right=of comp_group, label=above:{\textbf{Weighted Aggregation}}] (weight_group) {
    \node[lossbox, fill=outputcolor] (w1) {Refined Loss\\Weight: 1.0};
    \node[lossbox, fill=outputcolor, below=of w1] (w2) {Main Loss\\Weight: 0.8};
    \node[lossbox, fill=outputcolor, below=of w2] (w3) {Final Sup\\Weight: 0.4};
    \node[lossbox, fill=outputcolor, below=of w4] (w4) {H Sup\\Weight: 0.2};
    \node[lossbox, fill=outputcolor, below=of w5] (w5) {L Sup\\Weight: 0.2};
};

% 总损失
\node[bigbox, fill=outputcolor, right=of w3] (total_loss) {Total Loss\\Weighted Sum};

% 连接线
\draw[arrow] (gt) -- (focal);
\draw[arrow] (gt) -- (iou);
\draw[arrow] (gt) -- (edge);

\draw[arrow] (main_logits) -- (composite);
\draw[arrow] (refined) -- (prob_loss);
\draw[arrow] (deep_h) -- (prob_loss);
\draw[arrow] (deep_l) -- (prob_loss);
\draw[arrow] (deep_final) -- (prob_loss);

\draw[arrow] (focal) -- (composite);
\draw[arrow] (iou) -- (composite);
\draw[arrow] (edge) -- (composite);

\draw[arrow] (composite) -- (w1);
\draw[arrow] (composite) -- (w2);
\draw[arrow] (prob_loss) -- (w3);
\draw[arrow] (prob_loss) -- (w4);
\draw[arrow] (prob_loss) -- (w5);

\draw[arrow] (w1) -- (total_loss);
\draw[arrow] (w2) -- (total_loss);
\draw[arrow] (w3) -- (total_loss);
\draw[arrow] (w4) -- (total_loss);
\draw[arrow] (w5) -- (total_loss);

\end{tikzpicture}
\caption{RTGlassNetv2损失函数架构图。采用多组件损失函数，包括边缘感知Focal损失、IoU损失和边缘感知BCE损失，通过深度监督策略进行多层级优化。}
\label{fig:rtglassnetv2_loss}
\end{figure}

% 技术规格表
\begin{table}[htbp]
\centering
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Component} & \textbf{Parameters} & \textbf{Output Size} & \textbf{Key Features} \\
\hline
Backbone & InceptionNeXt-T/S/B & Multi-scale & Pre-trained ImageNet \\
\hline
LightLCFI & Depthwise Separable & Reduced channels & Multi-dilation \\
\hline
SCSA & 8 heads, $7\times7$ window & Attention maps & Group kernels \\
\hline
CRF & 3 iterations & Refined prediction & Trainable \\
\hline
Total Params & $\sim$50M (Tiny) / $\sim$100M (Base) & $1\times H\times W$ & Real-time capable \\
\hline
\end{tabular}
\caption{RTGlassNetv2技术规格表}
\label{tab:rtglassnetv2_specs}
\end{table}

\end{document} 