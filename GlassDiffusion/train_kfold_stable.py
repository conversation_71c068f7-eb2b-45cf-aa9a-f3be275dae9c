#!/usr/bin/env python3
"""
🎯 RTGlassNet K-Fold稳定性训练脚本
支持80:20数据分割和5折交叉验证，解决IoU起伏问题
"""

import os
import warnings
import numpy as np
import random
from sklearn.model_selection import KFold

# 抑制警告
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import json
import gc

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    print(f"🎲 随机种子设置为: {seed}")

def clear_cuda_cache():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet K-Fold稳定性训练')
    
    # 基础参数
    parser.add_argument('--epochs', default=80, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='批次大小')
    parser.add_argument('--lr', default=0.0003, type=float, help='学习率')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--n_worker', default=2, type=int, help='工作进程数')
    
    # K-Fold参数
    parser.add_argument('--k_folds', default=5, type=int, help='K-Fold交叉验证的K值')
    parser.add_argument('--current_fold', default=None, type=int, help='当前训练的fold')
    
    # 损失权重
    parser.add_argument('--focal_weight', default=0.25, type=float, help='Focal损失权重')
    parser.add_argument('--iou_weight', default=0.65, type=float, help='IoU损失权重')
    parser.add_argument('--dt_weight', default=0.10, type=float, help='DT损失权重')
    
    # 其他参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    
    return parser.parse_args()

class KFoldEngine:
    def __init__(self, args):
        set_seed(42)
        clear_cuda_cache()
        
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.img_size = args.img_size
        self.n_worker = args.n_worker
        self.k_folds = args.k_folds
        self.current_fold = args.current_fold
        
        # 损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        # 其他配置
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        self.model_path = args.base_save_path + '/RTGlassNet_KFold'
        
        # 创建保存目录
        os.makedirs(os.path.join(self.model_path, 'weights'), exist_ok=True)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化损失函数
        self.criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=True
        )
        
        print(f"🎯 RTGlassNet K-Fold稳定性训练配置:")
        print(f"   🔧 数据分割: 80%训练+20%验证")
        print(f"   🔧 K-Fold: {self.k_folds}折")
        print(f"   🔧 批次大小: {self.bs}")
        print(f"   🔧 图像尺寸: {self.img_size}x{self.img_size}")
        print(f"   🔧 学习率: {self.lr}")
        print(f"   🔧 损失权重: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔧 IoU权重占比: {self.iou_weight*100:.1f}%")

    def create_model(self):
        """创建模型"""
        model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        self._load_backbone_weight(model)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   📊 模型参数: {total_params/1e6:.1f}M")
        
        return model

    def _load_backbone_weight(self, model):
        """加载预训练权重"""
        if self.backbone_weight and os.path.exists(self.backbone_weight):
            print(f"加载权重: {self.backbone_weight}")
            state_dict = torch.load(self.backbone_weight, map_location=self.device)
            model_backbone = model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"已加载 {len(filtered_dict)} 个参数")

    def create_data_splits(self):
        """创建数据分割"""
        print(f"\n🔄 创建数据分割...")
        
        # 创建完整数据集
        full_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='train',
            target_size=(self.img_size, self.img_size), 
            split_ratio=0.8,  # 80%训练数据
            random_seed=42,
            glass_aug_config='light'  # 轻量增强
        )
        
        # 创建验证数据集
        val_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='valid',
            target_size=(self.img_size, self.img_size), 
            split_ratio=0.8,  # 剩余20%验证
            random_seed=42,
            glass_aug_config=None  # 验证不增强
        )
        
        print(f"📊 数据分割: 训练{len(full_data)}，验证{len(val_data)}")
        
        # 创建K-Fold分割
        indices = list(range(len(full_data)))
        kfold = KFold(n_splits=self.k_folds, shuffle=True, random_state=42)
        fold_splits = list(kfold.split(indices))
        
        return full_data, val_data, fold_splits

    def train_fold(self, fold_idx, full_data, val_data, train_indices, val_indices):
        """训练单个fold"""
        print(f"\n🎯 训练 Fold {fold_idx + 1}/{self.k_folds}")
        print(f"   训练样本: {len(train_indices)}, 验证样本: {len(val_indices)}")
        
        clear_cuda_cache()
        
        # 创建模型和优化器
        model = self.create_model()
        optimizer = optim.AdamW(model.parameters(), lr=self.lr, weight_decay=0.0001)
        
        # 创建数据加载器
        train_dataset = Subset(full_data, train_indices)
        val_dataset = Subset(full_data, val_indices)  # 从训练集中分割验证
        
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.bs, 
            shuffle=True, 
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset, 
            batch_size=self.bs, 
            shuffle=False, 
            num_workers=self.n_worker,
            pin_memory=True
        )
        
        # 创建TensorBoard记录
        writer = SummaryWriter(log_dir=f'runs/kfold_fold{fold_idx+1}')
        
        best_iou = 0.0
        iou_history = []
        
        for epoch in range(1, self.epochs + 1):
            model.train()
            running_loss = 0.0
            
            train_tqdm = tqdm(train_loader, desc=f"Fold{fold_idx+1} Epoch{epoch}")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                running_loss += loss.item()
                
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}",
                    focal=f"{loss_dict['focal_loss']:.4f}",
                    iou=f"{loss_dict['iou_loss']:.4f}",
                    dt=f"{loss_dict['dt_loss']:.4f}"
                )
                
            epoch_loss = running_loss / len(train_loader)
            print(f"Fold{fold_idx+1} Epoch{epoch} Loss: {epoch_loss:.6f}")
            
            # 验证
            if epoch % 5 == 0:
                val_loss, val_iou = self.validate(model, val_loader)
                iou_history.append(val_iou)
                
                print(f"Fold{fold_idx+1} Epoch{epoch} Val IoU: {val_iou:.4f}")
                
                # 稳定性分析
                if len(iou_history) >= 3:
                    recent_std = np.std(iou_history[-3:])
                    print(f"   最近3轮IoU标准差: {recent_std:.4f}")
                
                # 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    self.save_model(fold_idx, epoch, model, optimizer, val_iou, is_best=True)
                
                # 记录到TensorBoard
                writer.add_scalar('Loss/Val', val_loss, epoch)
                writer.add_scalar('IoU/Val', val_iou, epoch)
                writer.add_scalar('IoU/Best', best_iou, epoch)
                
        writer.close()
        
        print(f"🎯 Fold {fold_idx + 1} 完成! 最佳IoU: {best_iou:.4f}")
        
        # 清理内存
        del model, optimizer
        clear_cuda_cache()
        
        return best_iou

    def validate(self, model, val_loader):
        """验证函数"""
        model.eval()
        running_loss = 0.0
        iou_list = []
        
        with torch.no_grad():
            for inp_imgs, gt_masks, dt_maps in val_loader:
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                outputs = model(inp_imgs)
                loss, _ = self.criterion(outputs, gt_masks, dt_maps)
                
                running_loss += loss.item()
                
                # 计算IoU
                main_pred = outputs['main_pred']
                pred_bin = (main_pred > 0.5).float()
                intersection = (pred_bin * gt_masks).sum(dim=(1,2,3))
                union = pred_bin.sum(dim=(1,2,3)) + gt_masks.sum(dim=(1,2,3)) - intersection
                iou = (intersection / (union + 1e-7)).mean().item()
                iou_list.append(iou)
        
        val_loss = running_loss / len(val_loader)
        mean_iou = sum(iou_list) / len(iou_list)
        
        return val_loss, mean_iou

    def save_model(self, fold_idx, epoch, model, optimizer, val_iou, is_best=False):
        """保存模型"""
        state = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'epoch': epoch,
            'fold': fold_idx,
            'val_iou': val_iou,
            'config': {
                'backbone_type': self.backbone_type,
                'k_folds': self.k_folds,
                'focal_weight': self.focal_weight,
                'iou_weight': self.iou_weight,
                'dt_weight': self.dt_weight
            }
        }
        
        if is_best:
            filename = f'BEST_fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
            print(f"🏆 保存最佳模型: {filename}")
        else:
            filename = f'fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
        
        save_path = os.path.join(self.model_path, 'weights', filename)
        torch.save(state, save_path)

    def train_kfold(self):
        """执行K-Fold交叉验证"""
        print(f"\n🎯 开始K-Fold交叉验证...")
        
        # 创建数据分割
        full_data, val_data, fold_splits = self.create_data_splits()
        
        fold_results = []
        
        # 训练每个fold
        folds_to_train = [self.current_fold] if self.current_fold is not None else range(self.k_folds)
        
        for fold_idx in folds_to_train:
            if fold_idx >= self.k_folds:
                print(f"❌ Fold {fold_idx} 超出范围")
                continue
                
            train_indices, val_indices = fold_splits[fold_idx]
            best_iou = self.train_fold(fold_idx, full_data, val_data, train_indices, val_indices)
            fold_results.append(best_iou)
        
        # 汇总结果
        if len(fold_results) > 1:
            mean_iou = np.mean(fold_results)
            std_iou = np.std(fold_results)
            
            print(f"\n🎯 K-Fold结果汇总:")
            print(f"   平均IoU: {mean_iou:.4f} ± {std_iou:.4f}")
            print(f"   各Fold: {[f'{x:.4f}' for x in fold_results]}")
            print(f"   最佳: {max(fold_results):.4f}")
            print(f"   最差: {min(fold_results):.4f}")
            print(f"   稳定性: {std_iou/mean_iou:.4f}")
            
            # 保存结果
            summary = {
                'k_folds': self.k_folds,
                'fold_results': fold_results,
                'mean_iou': mean_iou,
                'std_iou': std_iou,
                'stability': std_iou/mean_iou
            }
            
            with open(os.path.join(self.model_path, 'kfold_summary.json'), 'w') as f:
                json.dump(summary, f, indent=2)
                
        elif len(fold_results) == 1:
            print(f"\n🎯 单Fold结果: {fold_results[0]:.4f}")

def main():
    args = parse_arguments()
    engine = KFoldEngine(args)
    engine.train_kfold()

if __name__ == '__main__':
    main() 