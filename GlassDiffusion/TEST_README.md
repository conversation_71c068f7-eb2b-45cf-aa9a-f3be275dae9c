# ProteusGlassNet 测试指南

## 📋 概述

本测试套件为ProteusGlassNet提供了完整的评估功能，支持多种测试模式：

- **单张图像测试**: 测试单张图像的玻璃检测效果
- **数据集测试**: 在完整数据集上评估模型性能
- **性能基准测试**: 测试模型推理速度和效率

## 🚀 快速开始

### 1. 基本测试命令

```bash
# 数据集测试（推荐）
python test_proteus_glassnet.py --mode dataset --model_path path/to/model.pth

# 性能基准测试
python test_proteus_glassnet.py --mode benchmark --model_path path/to/model.pth

# 单张图像测试
python test_proteus_glassnet.py --mode single --model_path path/to/model.pth --image_path path/to/image.jpg
```

### 2. 运行所有测试示例

```bash
python run_test_examples.py
```

## 📊 测试模式详解

### 1. 数据集测试 (dataset)

在完整数据集上评估模型性能，计算各种指标。

**命令格式:**
```bash
python test_proteus_glassnet.py --mode dataset --model_path <模型路径> [选项]
```

**选项:**
- `--batch_size <数字>`: 批次大小 (默认: 8)
- `--data_dir <路径>`: 自定义数据目录
- `--no_save`: 不保存预测结果
- `--no_visualize`: 不生成可视化

**输出指标:**
- IoU (Intersection over Union)
- MAE (Mean Absolute Error)
- Precision, Recall, F1-Score
- Accuracy

**示例:**
```bash
# 基本数据集测试
python test_proteus_glassnet.py --mode dataset --model_path checkpoints/model_best.pth

# 快速测试（不保存结果）
python test_proteus_glassnet.py --mode dataset --model_path checkpoints/model_best.pth --batch_size 16 --no_save
```

### 2. 性能基准测试 (benchmark)

测试模型推理性能，包括FPS和平均推理时间。

**命令格式:**
```bash
python test_proteus_glassnet.py --mode benchmark --model_path <模型路径> [选项]
```

**选项:**
- `--num_runs <数字>`: 测试运行次数 (默认: 100)

**输出指标:**
- 平均推理时间 (ms)
- FPS (Frames Per Second)
- 总测试时间

**示例:**
```bash
# 标准性能测试
python test_proteus_glassnet.py --mode benchmark --model_path checkpoints/model_best.pth

# 快速性能测试
python test_proteus_glassnet.py --mode benchmark --model_path checkpoints/model_best.pth --num_runs 50
```

### 3. 单张图像测试 (single)

测试单张图像的玻璃检测效果，生成可视化结果。

**命令格式:**
```bash
python test_proteus_glassnet.py --mode single --model_path <模型路径> --image_path <图像路径> [选项]
```

**必需参数:**
- `--image_path <路径>`: 输入图像路径

**选项:**
- `--output_path <路径>`: 预测结果保存路径
- `--no_visualize`: 不生成可视化结果

**输出:**
- 二值化预测掩码
- 概率图可视化
- 对比可视化（原图+预测+二值化结果）

**示例:**
```bash
# 基本单张图像测试
python test_proteus_glassnet.py --mode single --model_path checkpoints/model_best.pth --image_path test.jpg

# 指定输出路径
python test_proteus_glassnet.py --mode single --model_path checkpoints/model_best.pth --image_path test.jpg --output_path result.png
```

## 📁 输出文件结构

测试完成后，结果保存在 `test_results/` 目录下：

```
test_results/
├── test_results.txt          # 详细测试结果
├── predictions/              # 预测结果图像
│   ├── batch_0_sample_0_image.png
│   ├── batch_0_sample_0_mask.png
│   ├── batch_0_sample_0_pred.png
│   └── ...
└── visualization_*.png       # 可视化结果
```

### 结果文件说明

**test_results.txt:**
```
ProteusGlassNet 测试结果
========================================
iou: 0.8542
mae: 0.1234
precision: 0.8765
recall: 0.8321
f1: 0.8538
accuracy: 0.9123
test_time: 00:02:15
```

**预测结果图像:**
- `*_image.png`: 原始图像
- `*_mask.png`: 真值掩码
- `*_pred.png`: 模型预测结果

## 🔧 高级用法

### 1. 自定义数据目录

```bash
python test_proteus_glassnet.py --mode dataset --model_path model.pth --data_dir /path/to/custom/dataset
```

### 2. 批量测试多张图像

```bash
# 创建测试图像列表
for img in test_images/*.jpg; do
    python test_proteus_glassnet.py --mode single --model_path model.pth --image_path "$img" --output_path "results/$(basename "$img")"
done
```

### 3. 性能对比测试

```bash
# 测试不同模型
for model in models/*.pth; do
    echo "Testing $(basename "$model")"
    python test_proteus_glassnet.py --mode benchmark --model_path "$model" --num_runs 100
done
```

## ⚠️ 注意事项

### 1. 模型权重格式

测试脚本支持两种模型权重格式：
- **纯权重文件**: 直接包含模型参数
- **完整检查点**: 包含模型参数和训练状态

### 2. 图像预处理

单张图像测试会自动进行以下预处理：
- 调整尺寸到配置的图像大小
- ImageNet标准化
- 转换为PyTorch tensor格式

### 3. 内存使用

- 大数据集测试时建议使用较小的batch_size
- 性能测试会占用较多GPU内存，确保有足够空间

### 4. 依赖库

确保安装以下依赖：
```bash
pip install torch torchvision opencv-python matplotlib tqdm pillow numpy
```

## 🐛 常见问题

### Q1: 模型加载失败
**A:** 检查模型路径是否正确，确保文件存在且格式正确。

### Q2: CUDA内存不足
**A:** 减小batch_size或使用CPU模式。

### Q3: 图像加载失败
**A:** 检查图像路径和格式，支持常见格式如jpg、png等。

### Q4: 测试结果异常
**A:** 检查模型是否与测试脚本兼容，确保使用正确的模型架构。

## 📈 性能基准

在RTX 4080上的典型性能表现：

| 图像尺寸 | 批次大小 | 平均推理时间 | FPS |
|---------|---------|-------------|-----|
| 512x512 | 1       | 15.2ms      | 65.8 |
| 512x512 | 8       | 18.5ms      | 54.1 |
| 1024x1024| 1      | 45.3ms      | 22.1 |

## 🤝 贡献

欢迎提交问题和改进建议！

---

**最后更新**: 2024年12月
**版本**: 1.0.0 