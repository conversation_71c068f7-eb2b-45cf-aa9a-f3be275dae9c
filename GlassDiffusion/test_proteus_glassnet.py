# test_proteus_glassnet.py
# -----------------------------------------------------------------------------
# ProteusGlassNet 测试脚本
# 支持多种测试模式：单张图像、批量测试、完整数据集评估
# 包含详细的指标计算和可视化功能
# -----------------------------------------------------------------------------

import os
import sys
import argparse
import torch
import torch.nn.functional as F
import numpy as np
import cv2
from tqdm import tqdm
from PIL import Image
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from glass_dataloader import create_glass_dataloaders
from models import ProteusGlassNet
from utils.metrics import SegmentationMetrics
from utils.utils import setup_seed, Timer, format_time

class ProteusGlassNetTester:
    """ProteusGlassNet测试器"""
    
    def __init__(self, config, model_path=None):
        """
        初始化测试器
        Args:
            config: 配置对象
            model_path: 模型权重路径
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        print("🔄 创建ProteusGlassNet模型...")
        self.model = ProteusGlassNet(
            vit_model_name=config.VIT_MODEL_NAME,
            backbone_path=config.BACKBONE_PRETRAINED_PATH,
            extract_layers=[3, 6, 9, 11],
            crf_iter=config.CRF_ITER,
            trainable_crf=config.CRF_TRAINABLE
        ).to(self.device)
        
        # 加载模型权重
        if model_path and os.path.exists(model_path):
            print(f"📦 加载模型权重: {model_path}")
            checkpoint = torch.load(model_path, map_location=self.device)
            
            if isinstance(checkpoint, dict) and 'model' in checkpoint:
                # 完整检查点
                self.model.load_state_dict(checkpoint['model'])
                print(f"✅ 模型加载成功 - Epoch: {checkpoint.get('epoch', 'N/A')}")
            else:
                # 纯权重文件
                self.model.load_state_dict(checkpoint)
                print("✅ 模型权重加载成功")
        else:
            print("⚠️ 未提供模型权重路径，使用随机初始化模型")
        
        # 创建指标计算器
        self.metrics = SegmentationMetrics()
        
        # 创建输出目录
        self.output_dir = os.path.join(config.OUTPUT_DIR, 'test_results')
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("✅ ProteusGlassNet测试器初始化完成!")
    
    def test_single_image(self, image_path, output_path=None, visualize=True):
        """
        测试单张图像
        Args:
            image_path: 输入图像路径
            output_path: 输出路径（可选）
            visualize: 是否可视化结果
        """
        print(f"🔍 测试单张图像: {image_path}")
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法加载图像: {image_path}")
            return None
        
        # 预处理
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image_tensor = self._preprocess_image(image_rgb)
        
        # 推理
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(image_tensor)
            pred = outputs['main_pred']
        
        # 后处理
        pred_np = pred.cpu().numpy()[0, 0]  # [H, W]
        pred_binary = (pred_np > 0.5).astype(np.uint8) * 255
        
        # 保存结果
        if output_path:
            cv2.imwrite(output_path, pred_binary)
            print(f"💾 预测结果已保存: {output_path}")
        
        # 可视化
        if visualize:
            self._visualize_single_result(image_rgb, pred_np, pred_binary, image_path)
        
        return pred_np, pred_binary
    
    def test_dataset(self, data_dir=None, batch_size=8, save_predictions=True):
        """
        测试完整数据集
        Args:
            data_dir: 数据目录（可选，默认使用配置中的路径）
            batch_size: 批次大小
            save_predictions: 是否保存预测结果
        """
        print("🔍 开始数据集测试...")
        
        # 创建数据加载器
        if data_dir:
            # 使用指定目录
            print(f"📁 使用指定数据目录: {data_dir}")
            # 这里需要根据实际数据结构调整
            test_loader = self._create_test_loader(data_dir, batch_size)
        else:
            # 使用配置中的数据加载器
            print("📁 使用配置中的数据加载器")
            _, test_loader = create_glass_dataloaders(
                data_dir=config.PROJECT_ROOT,
                batch_size=batch_size,
                target_size=config.STAGE1_IMG_SIZE,
                split_ratio=0.9,
                random_seed=42,
                num_workers=4
            )
        
        # 测试
        results = self._evaluate_dataloader(test_loader, save_predictions)
        
        # 打印结果
        self._print_test_results(results)
        
        return results
    
    def _evaluate_dataloader(self, dataloader, save_predictions=True):
        """评估数据加载器"""
        self.model.eval()
        self.metrics.reset()
        
        total_loss = 0
        total_iou = 0
        total_mae = 0
        total_samples = 0
        
        # 创建预测保存目录
        if save_predictions:
            pred_dir = os.path.join(self.output_dir, 'predictions')
            os.makedirs(pred_dir, exist_ok=True)
        
        timer = Timer()
        
        with torch.no_grad():
            pbar = tqdm(dataloader, desc="测试中", ncols=100)
            for batch_idx, (images, masks, dt_maps) in enumerate(pbar):
                images = images.to(self.device, non_blocking=True)
                masks = masks.to(self.device, non_blocking=True)
                dt_maps = dt_maps.to(self.device, non_blocking=True)
                
                # 前向传播
                outputs = self.model(images)
                
                # 计算指标
                pred = outputs['main_pred']
                self.metrics.update(pred, masks)
                
                # 计算IoU
                iou = self._calculate_batch_iou(pred, masks)
                total_iou += iou * images.size(0)
                
                # 计算MAE
                mae = F.l1_loss(pred, masks).item()
                total_mae += mae * images.size(0)
                
                total_samples += images.size(0)
                
                # 保存预测结果
                if save_predictions and batch_idx < 10:  # 只保存前10个批次
                    self._save_batch_predictions(images, masks, pred, batch_idx, pred_dir)
                
                # 更新进度条
                avg_iou = total_iou / total_samples
                avg_mae = total_mae / total_samples
                pbar.set_postfix({
                    'IoU': f'{avg_iou:.4f}',
                    'MAE': f'{avg_mae:.4f}'
                })
        
        # 计算最终指标
        final_metrics = self.metrics.compute()
        final_metrics['iou'] = total_iou / total_samples
        final_metrics['mae'] = total_mae / total_samples
        final_metrics['test_time'] = timer.elapsed()
        
        return final_metrics
    
    def _calculate_batch_iou(self, pred, target):
        """计算批次的IoU"""
        pred_binary = (pred > 0.5).float()
        intersection = (pred_binary * target).sum(dim=(2, 3))
        union = pred_binary.sum(dim=(2, 3)) + target.sum(dim=(2, 3)) - intersection
        union = torch.clamp(union, min=1e-7)
        iou = (intersection / union).mean()
        return iou.item()
    
    def _save_batch_predictions(self, images, masks, preds, batch_idx, pred_dir):
        """保存批次预测结果"""
        for i in range(min(4, images.size(0))):  # 只保存前4张
            # 转换图像
            img = images[i].cpu().permute(1, 2, 0).numpy()
            img = ((img - img.min()) / (img.max() - img.min()) * 255).astype(np.uint8)
            
            # 转换掩码
            mask = masks[i, 0].cpu().numpy()
            mask = (mask * 255).astype(np.uint8)
            
            # 转换预测
            pred = preds[i, 0].cpu().numpy()
            pred_binary = (pred > 0.5).astype(np.uint8) * 255
            
            # 保存
            cv2.imwrite(os.path.join(pred_dir, f'batch_{batch_idx}_sample_{i}_image.png'), 
                       cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
            cv2.imwrite(os.path.join(pred_dir, f'batch_{batch_idx}_sample_{i}_mask.png'), mask)
            cv2.imwrite(os.path.join(pred_dir, f'batch_{batch_idx}_sample_{i}_pred.png'), pred_binary)
    
    def _preprocess_image(self, image):
        """预处理图像"""
        # 调整尺寸
        image_resized = cv2.resize(image, config.STAGE1_IMG_SIZE)
        
        # 归一化
        image_normalized = image_resized.astype(np.float32) / 255.0
        
        # ImageNet标准化
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        image_normalized = (image_normalized - mean) / std
        
        # 转换为tensor
        image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0)
        
        return image_tensor.to(self.device)
    
    def _visualize_single_result(self, image, pred_prob, pred_binary, image_path):
        """可视化单张图像结果"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 原图
        axes[0].imshow(image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # 概率图
        im1 = axes[1].imshow(pred_prob, cmap='viridis')
        axes[1].set_title('Prediction Probability')
        axes[1].axis('off')
        plt.colorbar(im1, ax=axes[1])
        
        # 二值化结果
        axes[2].imshow(pred_binary, cmap='gray')
        axes[2].set_title('Binary Prediction')
        axes[2].axis('off')
        
        plt.tight_layout()
        
        # 保存可视化结果
        vis_path = os.path.join(self.output_dir, f'visualization_{os.path.basename(image_path)}.png')
        plt.savefig(vis_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"📊 可视化结果已保存: {vis_path}")
    
    def _print_test_results(self, results):
        """打印测试结果"""
        print("\n" + "="*60)
        print("📊 ProteusGlassNet 测试结果")
        print("="*60)
        print(f"🎯 IoU: {results['iou']:.4f}")
        print(f"📏 MAE: {results['mae']:.4f}")
        print(f"🎯 Precision: {results.get('precision', 0):.4f}")
        print(f"🎯 Recall: {results.get('recall', 0):.4f}")
        print(f"🎯 F1-Score: {results.get('f1', 0):.4f}")
        print(f"🎯 Accuracy: {results.get('accuracy', 0):.4f}")
        print(f"⏱️ 测试时间: {format_time(results['test_time'])}")
        print("="*60)
        
        # 保存结果到文件
        result_file = os.path.join(self.output_dir, 'test_results.txt')
        with open(result_file, 'w') as f:
            f.write("ProteusGlassNet 测试结果\n")
            f.write("="*40 + "\n")
            for key, value in results.items():
                if key != 'test_time':
                    f.write(f"{key}: {value:.4f}\n")
                else:
                    f.write(f"{key}: {format_time(value)}\n")
        
        print(f"💾 详细结果已保存: {result_file}")
    
    def benchmark_performance(self, num_runs=100):
        """性能基准测试"""
        print(f"⚡ 开始性能基准测试 ({num_runs} 次运行)...")
        
        # 创建随机输入
        dummy_input = torch.randn(1, 3, *config.STAGE1_IMG_SIZE).to(self.device)
        
        # 预热
        self.model.eval()
        with torch.no_grad():
            for _ in range(10):
                _ = self.model(dummy_input)
        
        # 基准测试
        torch.cuda.synchronize()
        timer = Timer()
        
        with torch.no_grad():
            for _ in tqdm(range(num_runs), desc="性能测试"):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        total_time = timer.elapsed()
        
        # 计算性能指标
        avg_time = total_time / num_runs
        fps = 1.0 / avg_time
        
        print(f"⚡ 性能基准测试结果:")
        print(f"  - 平均推理时间: {avg_time*1000:.2f} ms")
        print(f"  - FPS: {fps:.2f}")
        print(f"  - 总测试时间: {format_time(total_time)}")
        
        return {
            'avg_inference_time': avg_time,
            'fps': fps,
            'total_test_time': total_time
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ProteusGlassNet 测试脚本')
    parser.add_argument('--mode', choices=['single', 'dataset', 'benchmark'], 
                       default='dataset', help='测试模式')
    parser.add_argument('--model_path', type=str, required=True,
                       help='模型权重路径')
    parser.add_argument('--image_path', type=str, default=None,
                       help='单张图像路径（single模式需要）')
    parser.add_argument('--output_path', type=str, default=None,
                       help='输出路径（single模式可选）')
    parser.add_argument('--data_dir', type=str, default=None,
                       help='数据目录（dataset模式可选）')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='批次大小')
    parser.add_argument('--num_runs', type=int, default=100,
                       help='性能测试运行次数')
    parser.add_argument('--no_save', action='store_true',
                       help='不保存预测结果')
    parser.add_argument('--no_visualize', action='store_true',
                       help='不生成可视化结果')
    
    args = parser.parse_args()
    
    # 设置随机种子
    setup_seed(42)
    
    # 创建测试器
    tester = ProteusGlassNetTester(config, args.model_path)
    
    # 根据模式执行测试
    if args.mode == 'single':
        if not args.image_path:
            print("❌ single模式需要指定--image_path")
            return
        
        tester.test_single_image(
            args.image_path, 
            args.output_path, 
            visualize=not args.no_visualize
        )
    
    elif args.mode == 'dataset':
        results = tester.test_dataset(
            data_dir=args.data_dir,
            batch_size=args.batch_size,
            save_predictions=not args.no_save
        )
    
    elif args.mode == 'benchmark':
        performance = tester.benchmark_performance(args.num_runs)
    
    print("✅ 测试完成!")

if __name__ == "__main__":
    main() 