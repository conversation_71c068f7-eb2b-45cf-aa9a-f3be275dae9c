# models/tgd_net.py
# -----------------------------------------------------------------------------
# TransXNet骨干版玻璃分割网络（SOTA多尺度融合架构）
# -----------------------------------------------------------------------------
import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
from typing import List, Dict

# 添加TransXNet路径（假设TransXNet在项目根目录下）
TRANSXNET_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../TransXNet/models'))
if TRANSXNET_PATH not in sys.path:
    sys.path.append(TRANSXNET_PATH)
from transxnet import TransXNet

# 导入本地CRF实现
from .diff_crf import SimplifiedDiffCRF

# 轻量化边缘增强模块（直接复用pgd_net实现）
class LightEdgeEnhancer(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
    def forward(self, x):
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        enhanced = self.edge_conv(x)
        return x + enhanced * edge_features

# 多尺度TransXNet特征提取器
class MultiScaleTransXNetFeatureExtractor(nn.Module):
    """
    多尺度TransXNet特征提取器，输出4个stage特征，适配FPN
    """
    def __init__(self, arch='tiny', pretrained_path=None, out_dims=[256, 256, 256, 256]):
        super().__init__()
        self.backbone = TransXNet(arch=arch, fork_feat=True, num_classes=0)
        if pretrained_path and os.path.exists(pretrained_path):
            # 修正：如果路径是目录，则加载目录下的data.pkl文件
            if os.path.isdir(pretrained_path):
                weight_file = os.path.join(pretrained_path, 'data.pkl')
            else:
                weight_file = pretrained_path
            
            if not os.path.exists(weight_file):
                print(f"⚠️ 权重文件不存在: {weight_file}")
                return

            print(f"📦 加载TransXNet预训练权重: {weight_file}")
            state = torch.load(weight_file, map_location='cpu')
            self.backbone.load_state_dict(state, strict=False)
        else:
            print("⚠️ 未加载TransXNet预训练权重，使用随机初始化！")
        # 适配器，将各stage特征统一到FPN输入维度
        self.adapters = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_dim, out_dim, kernel_size=1, bias=False),
                nn.BatchNorm2d(out_dim),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_dim, out_dim, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(out_dim),
                nn.ReLU(inplace=True)
            ) for in_dim, out_dim in zip(self.backbone.arch_settings[arch]['embed_dims'], out_dims)
        ])
    def forward(self, x):
        feats = self.backbone(x)  # list: [stage1, stage2, stage3, stage4]
        assert isinstance(feats, list) and len(feats) == 4
        out = [adapter(f) for f, adapter in zip(feats, self.adapters)]
        return out

# FPN解码器、FeatureRefiner、GeometryHead、SCSA等直接复用pgd_net.py实现
from .pgd_net import FPNDecoder, FeatureRefiner, GeometryHead, SCSA

# 主网络
class TransXGlassNet(nn.Module):
    """
    TransXGlassNet - 基于TransXNet骨干的多尺度玻璃分割网络
    """
    def __init__(self, arch='tiny', backbone_path=None, crf_iter=3, trainable_crf=True):
        super().__init__()
        print("🚀 初始化 TransXGlassNet ...")
        # 1. 多尺度特征提取
        self.backbone = MultiScaleTransXNetFeatureExtractor(
            arch=arch,
            pretrained_path=backbone_path,
            out_dims=[256, 256, 256, 256]
        )
        # 2. FPN解码
        self.fpn_decoder = FPNDecoder(feature_dims=[256, 256, 256, 256], output_dim=256)
        # 3. 特征精炼
        self.feature_refiner = FeatureRefiner(in_channels=256, out_channels=256)
        self.edge_enhancer = LightEdgeEnhancer(256)
        # 4. 几何头
        self.geometry_head = GeometryHead(in_channels=256, geometry_channels=64)
        # 5. SCSA注意力
        scsa_dim = 256 + 64
        scsa_dim = ((scsa_dim + 3) // 4) * 4
        self.scsa_adapter = nn.Conv2d(256 + 64, scsa_dim, kernel_size=1)
        self.scsa_attention = SCSA(dim=scsa_dim, head_num=8, window_size=7)
        # 6. 预测头
        self.main_predict = nn.Sequential(
            nn.Conv2d(scsa_dim, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 1, kernel_size=1)
        )
        self.transparency_predict = nn.Conv2d(256, 1, kernel_size=1)
        # 7. CRF - 使用与GDNetSCSA一致的参数
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,  # From compat=10
            gaussian_weight=5.0,     # From compat=5
            bilateral_spatial_sigma=40.0,  # From sxy=40
            bilateral_color_sigma=3.0,     # From srgb=3
            gaussian_sigma=1.5,            # From sxy=1.5
            trainable=trainable_crf
        )
        
        # Class balancing weights for handling imbalanced data
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)
        print("✅ TransXGlassNet 初始化完成!")
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        input_size = x.shape[2:]
        # 1. 特征提取
        multi_scale_features = self.backbone(x)
        # 2. FPN解码
        fpn_features = self.fpn_decoder(multi_scale_features)
        # 3. 特征精炼
        refined_features = self.feature_refiner(fpn_features['p2'])
        enhanced_features = self.edge_enhancer(refined_features)
        # 4. 几何头
        geometry_outputs = self.geometry_head(enhanced_features)
        # 5. SCSA
        scsa_input = torch.cat([enhanced_features, geometry_outputs['main_logits']], dim=1)
        scsa_feat = self.scsa_adapter(scsa_input)
        scsa_feat = self.scsa_attention(scsa_feat)
        # 6. 主预测
        main_logits = self.main_predict(scsa_feat)
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        dt_logits = F.interpolate(geometry_outputs['dt_logits'], size=input_size, mode='bilinear', align_corners=True)
        transparency_logits = F.interpolate(self.transparency_predict(fpn_features['p0']), size=input_size, mode='bilinear', align_corners=True)
        # 7. 激活
        main_pred = torch.sigmoid(main_logits)
        dt_pred = torch.sigmoid(dt_logits)
        transparency_pred = torch.sigmoid(transparency_logits)
        
        # 8. CRF后处理（采用GDNetSCSA的方式）
        # 确保预测在有效范围内
        main_prob = torch.clamp(main_pred, min=1e-7, max=1.0 - 1e-7)
        
        # 检查NaN或Inf值
        if torch.isnan(main_prob).any() or torch.isinf(main_prob).any():
            print("警告: 主预测中检测到NaN或Inf值")
            main_prob = torch.nan_to_num(main_prob, nan=0.5, posinf=1.0, neginf=0.0)
        
        # 创建2通道logits（背景，前景）
        bg_logits = torch.log((1 - main_prob) * self.class_weights[0])
        fg_logits = torch.log(main_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)
        
        # 归一化输入图像到[0,1]范围
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            # 反归一化ImageNet标准化
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)
        
        # 应用CRF
        try:
            crf_output = self.crf(combined_logits, normalized_img)
            # 检查CRF输出是否有效
            if crf_output.size(1) == 0:
                print("警告: CRF返回了通道数为0的张量。创建备用输出。")
                refined_pred = torch.cat([1 - main_prob, main_prob], dim=1)
            elif crf_output.size(1) == 2:
                refined_pred = crf_output[:, 1:2, :, :]  # 前景通道
            else:
                refined_pred = crf_output
        except Exception as e:
            print(f"CRF错误: {e}")
            # 创建备用预测
            refined_pred = torch.cat([1 - main_prob, main_prob], dim=1)
            if refined_pred.size(1) == 2:
                refined_pred = refined_pred[:, 1:2, :, :]  # 前景通道
        
        # 9. 输出
        outputs = {
            'main_pred': main_pred,
            'dt_pred': dt_pred,
            'transparency_pred': transparency_pred,
            'edge_pred': dt_pred,
            'final_pred': main_pred,
            'ensemble_pred': 0.7 * main_pred + 0.3 * dt_pred,
            'refined_pred': refined_pred
        }
        return outputs

__all__ = [
    'MultiScaleTransXNetFeatureExtractor',
    'TransXGlassNet'
] 