rqt_gui_py==0.5.3
python-qt-binding==0.4.4
interactive-markers==1.12.0
joint-state-publisher==1.15.1
smclib==1.8.6
rosunit==1.15.8
rospy==1.16.0
rosserial_python==0.9.2
message-filters==1.16.0
rqt-multiplot==0.0.12
rosgraph==1.16.0
tf2-geometry-msgs==0.7.7
rosserial_client==0.9.2
rosnode==1.16.0
tf2-sensor-msgs==0.7.7
rosservice==1.16.0
tf2-ros==0.7.7
xacro==1.14.16
rosserial_arduino==0.9.2
geneus==3.0.0
rosparam==1.16.0
rqt_gui==0.5.3
gennodejs==2.0.2
topic-tools==1.16.0
controller-manager-msgs==0.20.0
rostest==1.16.0
catkin==0.8.10
rosmsg==1.16.0
rviz==1.14.25
resource_retriever==1.12.8
rosbag==1.16.0
gazebo_ros==2.9.3
gazebo_plugins==2.9.3
rqt_graph==0.4.14
dynamic-reconfigure==1.7.3
image-geometry==1.16.2
actionlib==1.14.0
roswtf==1.16.0
qt-dotgraph==0.4.2
joint-state-publisher-gui==1.15.1
tf==1.13.2
roslib==1.15.8
tf2-py==0.7.7
gencpp==0.7.0
rqt-tf-tree==0.6.4
genpy==0.6.15
rosclean==1.15.8
laser_geometry==1.6.7
tf2-kdl==0.7.7
cv-bridge==1.16.2
base_local_planner==1.17.3
camera-calibration-parsers==1.12.1
genmsg==0.6.0
sensor-msgs==1.13.1
qt-gui-cpp==0.4.2
diagnostic-updater==1.11.0
roslz4==1.16.0
rosmaster==1.16.0
rostopic==1.16.0
genlisp==0.4.18
angles==1.9.13
roslaunch==1.16.0
qt-gui==0.4.2
exceptiongroup==1.2.2
PyYAML==6.0.2
pip==25.0.1
dash==3.0.1
decorator==5.2.1
grpcio==1.65.5
protobuf==4.25.8
nbformat==5.10.4
googleapis-common-protos==1.70.0
rpds-py==0.20.1
proto-plus==1.26.1
itsdangerous==2.2.0
google-ai-generativelanguage==0.1.0
python-dateutil==2.9.0.post0
scikit_build_core==0.11.1
python-dotenv==1.0.1
widgetsnbextension==4.0.13
cachetools==5.5.0
google-auth==2.34.0
traitlets==5.14.3
prompt_toolkit==3.0.50
addict==2.4.0
packaging==24.2
open3d==0.19.0
retrying==1.3.4
tensorboard-data-server==0.7.2
click==8.1.8
scikit-learn==1.3.2
referencing==0.35.1
absl-py==2.1.0
executing==2.2.0
pandas==2.0.3
ipython==8.12.3
importlib_metadata==8.4.0
Markdown==3.7
tqdm==4.67.1
attrs==25.3.0
cycler==0.12.1
pydantic_core==2.27.2
Werkzeug==3.0.4
wcwidth==0.2.13
kiss-icp==1.2.3
markdown-it-py==3.0.0
pure_eval==0.2.3
matplotlib-inline==0.1.7
pydantic==2.10.6
mdurl==0.1.2
jedi==0.19.2
blinker==1.8.2
rsa==4.9
requests-oauthlib==2.0.0
google-auth-oauthlib==1.0.0
plotly==6.0.1
map_closures==2.0.1
platformdirs==4.3.6
typing_extensions==4.13.0
backcall==0.2.0
google-generativeai==0.1.0rc1
Pygments==2.19.1
evo==1.30.5
pyquaternion==0.9.9
MarkupSafe==2.0.1
pkgutil_resolve_name==1.3.10
tomli==2.2.1
parso==0.8.4
fastjsonschema==2.21.1
ipywidgets==8.1.5
pyproject-metadata==0.9.1
ConfigArgParse==1.7
pickleshare==0.7.5
pillow==10.4.0
pybind11==2.13.6
grpcio-status==1.62.3
rich==14.0.0
Flask==3.0.3
joblib==1.4.2
threadpoolctl==3.5.0
jsonschema==4.23.0
tzdata==2024.1
torch-tb-profiler==0.4.3
comm==0.2.2
narwhals==1.32.0
typer==0.15.2
jupyter_core==5.7.2
pathspec==0.12.1
kiwisolver==1.4.7
tensorboard==2.14.0
pytz==2024.1
shellingham==1.5.4
jsonschema-specifications==2023.12.1
asttokens==3.0.0
nest-asyncio==1.6.0
pydantic-settings==2.8.1
google-api-core==2.25.1
jupyterlab_widgets==3.0.13
annotated-types==0.7.0
stack-data==0.6.3
cmake==3.31.6
ninja==********
kiss-slam==0.0.1
pytorch-msssim==1.0.0
opencv-python==*********
filelock==3.16.1
CacheControl==0.14.2
uvicorn==0.33.0
jupyter-client==7.3.4
Pygments==2.18.0
tornado==6.1
yarl==1.9.4
starlette==0.44.0
distro==1.8.0
requests==2.31.0
netifaces==0.11.0
msgpack==1.1.0
keyring==24.3.1
thop==0.1.1-2209072238
numpy==1.24.4
installer==0.7.0
pip==24.3.1
virtualenv==20.28.1
poetry-plugin-export==1.8.0
debugpy==1.6.7
threadpoolctl==2.2.0
ruamel.yaml==0.18.10
cryptography==41.0.3
psutil==5.9.8
psutil==5.9.0
PyQt5-Qt5==5.15.16
traitlets==5.14.3
packaging==23.2
packaging==23.1
gymnasium==0.29.1
docutils==0.20.1
ez-setup==0.9
kornia==0.7.3
contourpy==1.1.1
requests-toolbelt==1.0.0
platformdirs==3.10.0
Pillow==10.1.0
Pillow==10.0.1
tomlkit==0.12.0
gradio==4.44.1
fastapi==0.115.13
rospkg==1.5.0
PyQt5==5.15.11
entrypoints==0.4
poetry-core==1.9.1
scikit-image==0.21.0
catkin-pkg==1.0.0
gym-notices==0.0.8
xformers==0.0.22.post7
shapely==2.0.3
pyOpenSSL==23.2.0
numexpr==2.8.6
pandas==2.0.3
pyasn1_modules==0.4.1
pydub==0.25.1
ipykernel==6.29.5
aiosignal==1.3.1
Shimmy==1.3.0
semantic-version==2.10.0
smmap==5.0.1
rapidfuzz==3.9.7
defusedxml==0.7.1
ipython==7.33.0
colorama==0.4.6
pycryptodome==3.20.0
MarkupSafe==2.1.1
MarkupSafe==2.1.5
tifffile==2023.7.10
optuna==4.2.1
colorlog==6.9.0
cycler==0.12.1
Farama-Notifications==0.0.4
pexpect==4.9.0
python-dateutil==2.8.2
zipp==3.17.0
llvmlite==0.41.1
jedi==0.18.2
wcwidth==0.2.13
tqdm==4.66.4
jaraco.classes==3.4.0
httpcore==1.0.9
ffmpy==0.5.0
pyparsing==3.1.1
matplotlib-inline==0.1.7
sympy==1.11.1
imageio==2.35.1
cffi==1.15.1
torch_geometric==2.5.3
gradio_client==1.3.0
Jinja2==3.1.2
cloudpickle==3.0.0
cleo==2.1.0
alembic==1.14.1
pyzmq==25.1.2
pydensecrf==1.0rc3
pycparser==2.21
tzdata==2023.3
mkl-random==1.2.4
mkl-service==2.4.0
gitdb==4.0.11
rosbags==0.9.23
certifi==2024.8.30
greenlet==3.1.1
aiohttp==3.9.5
kiwisolver==1.4.5
idna==3.4
matplotlib==3.7.3
importlib-metadata==6.8.0
urllib3==2.2.3
SQLAlchemy==2.0.38
sniffio==1.3.1
async-timeout==4.0.3
ruff==0.12.0
backcall==0.2.0
httpx==0.28.1
Brotli==1.0.9
lazy_loader==0.4
six==1.16.0
websockets==12.0
multidict==6.0.5
distlib==0.3.9
prompt_toolkit==3.0.48
setproctitle==1.3.6
eval_type_backport==0.2.2
more-itertools==10.5.0
tomli==2.2.1
PyQt5_sip==12.15.0
sb3_contrib==2.3.0
pytz==2023.3.post1
openpyxl==3.1.5
frozenlist==1.4.1
python-multipart==0.0.20
PySocks==1.7.1
parso==0.8.4
fastjsonschema==2.21.1
PyYAML==6.0.1
natsort==8.4.0
et_xmlfile==2.0.0
xlwt==1.3.0
sentry-sdk==2.31.0
h11==0.16.0
zstandard==0.23.0
PyWavelets==1.4.1
torchvision==0.16.0
crashtest==0.4.1
scipy==1.10.1
ruamel.yaml.clib==0.2.8
importlib-resources==6.1.0
aiofiles==23.2.1
pickleshare==0.7.5
nptyping==2.5.0
setuptools==70.3.0
setuptools==75.3.0
charset-normalizer==2.0.4
jeepney==0.8.0
transforms3d==0.4.2
python-gnupg==0.5.2
scikit-learn==1.3.0
gmpy2==2.1.2
seaborn==0.13.2
build==1.2.2.post1
numba==0.58.1
mkl-fft==1.3.8
lz4==4.3.3
comm==0.2.2
decorator==5.1.1
einops==0.8.1
kornia_rs==0.1.8
pkginfo==1.12.0
joblib==1.2.0
argcomplete==3.6.0
pycryptodomex==3.20.0
fonttools==4.43.1
orjson==3.10.15
wandb==0.20.1
jupyter_core==5.7.2
torch==2.1.0
tensorboardX==*******
torchaudio==2.1.0
hf-xet==1.1.5
SecretStorage==3.3.3
anyio==4.5.2
pyproject_hooks==1.2.0
Cython==3.0.11
networkx==3.1
attrs==23.2.0
shellingham==1.5.4
mpmath==1.3.0
trove-classifiers==2025.1.10.15
huggingface-hub==0.33.0
fsspec==2023.10.0
pyasn1==0.6.1
nest_asyncio==1.6.0
squaternion==2023.9.2
pooch==1.7.0
ptyprocess==0.7.0
oauthlib==3.2.2
wheel==0.45.1
dulwich==0.21.7
gym==0.21.0
poetry==1.8.5
triton==2.1.0
stable_baselines3==2.3.2
Mako==1.3.9
typing_extensions==4.7.1
GitPython==3.1.40
apex==0.1
autocommand==2.2.2
backports.tarfile==1.2.0
packaging==24.1
importlib_metadata==8.0.0
platformdirs==4.2.2
jaraco.context==5.3.0
jaraco.text==3.12.1
more-itertools==10.3.0
typing_extensions==4.12.2
jaraco.functools==4.0.1
tomli==2.0.1
zipp==3.19.2
importlib_resources==6.4.0
inflect==7.3.1
wheel==0.43.0
jaraco.collections==5.1.0
typeguard==4.3.0
