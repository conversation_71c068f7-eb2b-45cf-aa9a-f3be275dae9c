"""
增强版可训练CRF - 结合TrainableCRF的稳定性和原始DenseCRF的理论完整性

核心改进：
1. 保留对数空间学习的数值稳定性
2. 添加兼容性矩阵，更接近原始DenseCRF
3. 可选的消息幅度控制，可以关闭以更接近原始
4. 更精确的双边滤波实现
5. 参数初始化更接近原始DenseCRF的最优值

作者：基于TrainableCRF改进，增强理论完整性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class EnhancedTrainableCRF(nn.Module):
    """
    增强版可训练CRF，结合稳定性和理论完整性
    
    主要改进：
    1. 添加兼容性矩阵学习
    2. 可选的消息幅度控制
    3. 更精确的双边滤波
    4. 原始DenseCRF参数初始化
    """
    
    def __init__(self,
                 n_iter: int = 5,
                 # 使用原始DenseCRF的最优参数作为初始值
                 initial_bilateral_weight: float = 10.0,  # 原始DenseCRF的w_bilateral
                 initial_gaussian_weight: float = 5.0,    # 原始DenseCRF的w_gaussian
                 initial_bilateral_spatial_sigma: float = 40.0,  # 原始DenseCRF的sxy_bilateral
                 initial_bilateral_color_sigma: float = 3.0,     # 原始DenseCRF的srgb
                 initial_gaussian_sigma: float = 1.5,            # 原始DenseCRF的sxy_gaussian
                 # 新增控制选项
                 use_compatibility_matrix: bool = True,    # 是否使用兼容性矩阵
                 use_message_control: bool = True,         # 是否使用消息幅度控制
                 use_iteration_decay: bool = False,        # 是否使用迭代衰减（原始DenseCRF无此功能）
                 message_control_strength: float = 1.0):  # 消息控制强度，1.0=tanh，更大值=更宽松
        """
        初始化增强版可训练CRF
        
        Args:
            n_iter: 迭代次数
            initial_*: 各参数初始值，使用原始DenseCRF的最优值
            use_compatibility_matrix: 是否使用兼容性矩阵（更接近原始）
            use_message_control: 是否使用消息幅度控制（稳定性 vs 原始性）
            use_iteration_decay: 是否使用迭代衰减（原始DenseCRF无此功能）
            message_control_strength: 消息控制强度
        """
        super().__init__()
        self.n_iter = n_iter
        self.use_compatibility_matrix = use_compatibility_matrix
        self.use_message_control = use_message_control
        self.use_iteration_decay = use_iteration_decay
        self.message_control_strength = message_control_strength

        # 保留对数空间学习的数值稳定性优势
        self.log_bilateral_weight = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_weight, dtype=torch.float32))
        )
        self.log_gaussian_weight = nn.Parameter(
            torch.log(torch.tensor(initial_gaussian_weight, dtype=torch.float32))
        )
        self.log_bilateral_spatial_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_spatial_sigma, dtype=torch.float32))
        )
        self.log_bilateral_color_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_color_sigma, dtype=torch.float32))
        )
        self.log_gaussian_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_gaussian_sigma, dtype=torch.float32))
        )
        
        # 🔥 新增：兼容性矩阵（更接近原始DenseCRF）
        if self.use_compatibility_matrix:
            # 初始化为标准的二分类兼容性矩阵
            initial_compat = torch.tensor([[-1.0, 1.0], [1.0, -1.0]], dtype=torch.float32)
            self.compatibility_matrix = nn.Parameter(initial_compat)
        
        # 可选的稳定性控制参数
        if self.use_message_control:
            self.message_scaler = nn.Parameter(torch.tensor(1.0, dtype=torch.float32))
        
        if self.use_iteration_decay:
            self.iteration_decay = nn.Parameter(torch.tensor(0.95, dtype=torch.float32))

    def _gaussian_filter(self, x: torch.Tensor, sigma: torch.Tensor) -> torch.Tensor:
        """改进的高斯滤波，更接近原始DenseCRF"""
        sigma_val = torch.clamp(sigma, min=0.1, max=100.0).item()
        
        # 使用更大的核大小，更接近原始DenseCRF
        kernel_size = int(4 * sigma_val + 1)  # 更大的核
        if kernel_size % 2 == 0:
            kernel_size += 1
        kernel_size = min(kernel_size, 31)  # 允许更大的核
        
        padding = kernel_size // 2
        grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
        
        gaussian_1d = torch.exp(-0.5 * (grid / sigma).pow(2))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()

        gaussian_2d = gaussian_1d.view(1, 1, -1, 1) * gaussian_1d.view(1, 1, 1, -1)
        gaussian_2d = gaussian_2d.expand(x.size(1), 1, kernel_size, kernel_size)

        return F.conv2d(x, gaussian_2d, padding=padding, groups=x.size(1))

    def _enhanced_bilateral_filter(self, 
                                  x: torch.Tensor, 
                                  guide: torch.Tensor, 
                                  spatial_sigma: torch.Tensor, 
                                  color_sigma: torch.Tensor) -> torch.Tensor:
        """
        增强的双边滤波，更接近原始DenseCRF的实现
        """
        # 1. 空间滤波
        spatial_filtered = self._gaussian_filter(x, spatial_sigma)
        
        # 2. 改进的颜色相似性计算
        B, C, H, W = guide.shape
        
        # 计算颜色差异的更精确方法
        # 使用滑动窗口计算局部颜色差异
        kernel_size = 5  # 局部窗口大小
        padding = kernel_size // 2
        
        # 展开guide为patches
        guide_patches = F.unfold(guide, kernel_size, padding=padding)  # [B, C*k*k, H*W]
        guide_patches = guide_patches.view(B, C, kernel_size*kernel_size, H, W)
        
        # 中心像素
        center_idx = kernel_size * kernel_size // 2
        guide_center = guide_patches[:, :, center_idx:center_idx+1, :, :]  # [B, C, 1, H, W]
        
        # 计算颜色差异
        color_diff = guide_patches - guide_center  # [B, C, k*k, H, W]
        color_diff_sq = color_diff.pow(2).sum(dim=1)  # [B, k*k, H, W]
        
        # 颜色权重
        color_sigma_sq = 2 * color_sigma.pow(2)
        color_weights = torch.exp(-color_diff_sq / color_sigma_sq)  # [B, k*k, H, W]
        
        # 应用权重到空间滤波结果
        # 这里简化处理，使用平均权重
        avg_color_weight = color_weights.mean(dim=1, keepdim=True)  # [B, 1, H, W]
        
        return spatial_filtered * avg_color_weight

    def _apply_compatibility_transform(self, message: torch.Tensor) -> torch.Tensor:
        """
        应用兼容性变换（原始DenseCRF的核心组件）
        """
        if not self.use_compatibility_matrix:
            return message
        
        # message: [B, C, H, W]
        B, C, H, W = message.shape
        
        # 重塑为 [B, C, H*W]
        message_flat = message.view(B, C, -1)
        
        # 应用兼容性矩阵: [C, C] x [B, C, H*W] -> [B, C, H*W]
        transformed = torch.einsum('ij,bjk->bik', self.compatibility_matrix, message_flat)
        
        # 重塑回 [B, C, H, W]
        return transformed.view(B, C, H, W)

    def forward(self, unary: torch.Tensor, img: torch.Tensor) -> torch.Tensor:
        """
        增强版CRF前向传播
        """
        if unary.device != img.device:
            img = img.to(unary.device)
        
        # 从对数空间恢复参数（保持数值稳定性）
        bilateral_weight = torch.exp(torch.clamp(self.log_bilateral_weight, min=-5, max=5))
        gaussian_weight = torch.exp(torch.clamp(self.log_gaussian_weight, min=-5, max=5))
        bilateral_spatial_sigma = torch.exp(torch.clamp(self.log_bilateral_spatial_sigma, min=-3, max=5))
        bilateral_color_sigma = torch.exp(torch.clamp(self.log_bilateral_color_sigma, min=-3, max=3))
        gaussian_sigma = torch.exp(torch.clamp(self.log_gaussian_sigma, min=-3, max=3))
        
        # 初始化
        logits = unary
        Q = F.softmax(logits, dim=1)

        # 平均场迭代
        for i in range(self.n_iter):
            # 消息传递
            gaussian_term = self._gaussian_filter(Q, gaussian_sigma)
            bilateral_term = self._enhanced_bilateral_filter(
                Q, img, bilateral_spatial_sigma, bilateral_color_sigma
            )
            
            # 组合消息
            raw_message = gaussian_weight * gaussian_term + bilateral_weight * bilateral_term
            
            # 🔥 应用兼容性变换（更接近原始DenseCRF）
            if self.use_compatibility_matrix:
                raw_message = self._apply_compatibility_transform(raw_message)
            
            # 可选的消息控制
            if self.use_message_control:
                # 可调节的消息控制强度
                if self.message_control_strength == 1.0:
                    controlled_message = torch.tanh(raw_message)
                else:
                    # 更宽松的控制：tanh(x/strength) * strength
                    controlled_message = torch.tanh(raw_message / self.message_control_strength) * self.message_control_strength
                
                # 应用可学习的缩放因子
                scaled_message = self.message_scaler * controlled_message
            else:
                # 不使用消息控制，更接近原始DenseCRF
                scaled_message = raw_message
            
            # 可选的迭代衰减
            if self.use_iteration_decay:
                decay_factor = torch.pow(torch.clamp(self.iteration_decay, min=0.1, max=1.0), i)
                scaled_message = decay_factor * scaled_message

            # 更新logits
            logits = unary + scaled_message
            Q = F.softmax(logits, dim=1)
            
        return logits
    
    def get_parameters_summary(self) -> dict:
        """获取参数摘要，包括兼容性矩阵"""
        with torch.no_grad():
            params = {
                'bilateral_weight': torch.exp(self.log_bilateral_weight).item(),
                'gaussian_weight': torch.exp(self.log_gaussian_weight).item(),
                'bilateral_spatial_sigma': torch.exp(self.log_bilateral_spatial_sigma).item(),
                'bilateral_color_sigma': torch.exp(self.log_bilateral_color_sigma).item(),
                'gaussian_sigma': torch.exp(self.log_gaussian_sigma).item(),
            }
            
            if self.use_compatibility_matrix:
                params['compatibility_matrix'] = self.compatibility_matrix.cpu().numpy().tolist()
            
            if self.use_message_control:
                params['message_scaler'] = self.message_scaler.item()
            
            if self.use_iteration_decay:
                params['iteration_decay'] = self.iteration_decay.item()
                
            return params

    @classmethod
    def create_original_like(cls, n_iter: int = 5):
        """
        创建最接近原始DenseCRF的版本
        """
        return cls(
            n_iter=n_iter,
            initial_bilateral_weight=10.0,
            initial_gaussian_weight=5.0,
            initial_bilateral_spatial_sigma=40.0,
            initial_bilateral_color_sigma=3.0,
            initial_gaussian_sigma=1.5,
            use_compatibility_matrix=True,   # 使用兼容性矩阵
            use_message_control=False,       # 不使用消息控制
            use_iteration_decay=False        # 不使用迭代衰减
        )
    
    @classmethod
    def create_stable_version(cls, n_iter: int = 5):
        """
        创建稳定训练版本
        """
        return cls(
            n_iter=n_iter,
            initial_bilateral_weight=10.0,
            initial_gaussian_weight=5.0,
            initial_bilateral_spatial_sigma=40.0,
            initial_bilateral_color_sigma=3.0,
            initial_gaussian_sigma=1.5,
            use_compatibility_matrix=True,   # 使用兼容性矩阵
            use_message_control=True,        # 使用消息控制
            use_iteration_decay=True,        # 使用迭代衰减
            message_control_strength=2.0     # 更宽松的控制
        )


def test_enhanced_crf():
    """测试增强版CRF"""
    print("=== 增强版TrainableCRF测试 ===")
    
    # 测试数据
    batch_size, num_classes, height, width = 2, 2, 64, 64
    unary = torch.randn(batch_size, num_classes, height, width)
    img = torch.rand(batch_size, 3, height, width)
    
    # 测试原始版本
    print("\n1. 原始DenseCRF风格版本:")
    crf_original = EnhancedTrainableCRF.create_original_like()
    output_original = crf_original(unary, img)
    params_original = crf_original.get_parameters_summary()
    print(f"参数数量: {sum(p.numel() for p in crf_original.parameters())}")
    print(f"输出范围: [{output_original.min():.4f}, {output_original.max():.4f}]")
    
    # 测试稳定版本
    print("\n2. 稳定训练版本:")
    crf_stable = EnhancedTrainableCRF.create_stable_version()
    output_stable = crf_stable(unary, img)
    params_stable = crf_stable.get_parameters_summary()
    print(f"参数数量: {sum(p.numel() for p in crf_stable.parameters())}")
    print(f"输出范围: [{output_stable.min():.4f}, {output_stable.max():.4f}]")
    
    print("\n✓ 增强版CRF测试通过!")


if __name__ == "__main__":
    test_enhanced_crf()
