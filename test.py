import os
import time
import random
import cv2
import numpy as np
import torch
from PIL import Image
from torch.autograd import Variable
from torchvision import transforms
from ig_glass.misc import *
from ig_glass.gdnet import GDNet
#from config import gdd_testing_root, gdd_results_root, backbone_path
backbone_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/resnext_101_32x4d.pth'
#gdd_training_root = "/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/IG-SLAM/re200.pth"
# device set
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# parameter set
ckpt_path = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt'
exp_name = 'IG-SLAM'
args = {
    'snapshot': '200', #200(orin),fofin(new),new200,gdd110
    'scale': 416, #416
    'crf': False, #True for grid search, False for original results
    'glass_threshold': 0.9,  # 玻璃区域识别阈值
}

# CRF参数网格搜索设置
# round1，best is [1.5, 40, 3，5, 10]
crf_params_grid = {
    'sxy1': [1.5, 3],      # 空间高斯核参数
    'sxy2': [40, 60],      # 双边滤波空间参数
    'srgb': [3, 5],        # 双边滤波颜色参数
    'compat1': [5, 3],     # 一阶相容性权重
    'compat2': [10, 5]     # 二阶相容性权重
}

# 预处理
img_transform = transforms.Compose([
    transforms.Resize((args['scale'], args['scale'])),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
])

to_pil = transforms.ToPILImage()

# 动态亮度调整目标范围
BRIGHTNESS_TARGET_MIN = 100
BRIGHTNESS_TARGET_MAX = 120


def calculate_brightness(image_path):
    """计算图像亮度"""
    image = cv2.imread(image_path)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    brightness = np.mean(hsv[:, :, 2])
    return brightness


def calculate_feature_points(image_path):
    """计算图像特征点"""
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    orb = cv2.ORB_create(nfeatures=10000)
    keypoints = orb.detect(image, None)
    return keypoints, len(keypoints)


def adjust_image_brightness(image, adjustment):
    """根据调整值调整图像亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    v = np.clip(v.astype(np.int32) + int(adjustment), 0, 255).astype(np.uint8)
    hsv = cv2.merge((h, s, v))
    return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)


def adjust_image_brightness_dynamically(image, target_min, target_max):
    """根据图像亮度的分布动态调整亮度"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v = hsv[:, :, 2]  # 提取亮度通道

    # 计算亮度的直方图
    hist = cv2.calcHist([v], [0], None, [256], [0, 256])
    cumulative_sum = np.cumsum(hist)
    median_brightness = np.searchsorted(cumulative_sum, cumulative_sum[-1] // 2)

    # 动态目标亮度，基于图像的亮度分布
    current_mean = np.mean(v)
    current_std = np.std(v)

    # 根据当前亮度的均值和标准差，动态调整目标亮度
    if current_mean < target_min:
        adjustment = target_min - median_brightness
    elif current_mean > target_max:
        adjustment = target_max - median_brightness
    else:
        adjustment = (target_max + target_min) / 2 - median_brightness

    # 调整图像亮度
    adjusted_image = adjust_image_brightness(image, adjustment)

    return adjusted_image


def detect_glass_and_adjust_brightness(image_folder, output_folder, net, glass_threshold,crf_params):
    """检测玻璃区域并调整亮度"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    if not image_files:
        raise Exception("输入文件夹为空，没有可处理的图像")
        
    brightness_list = []
    feature_points_list = []
    processed_count = 0
    error_count = 0
    
    # 性能测量
    inference_times = []
    total_times = []

    print(f"🔍 开始处理 {len(image_files)} 张图像...")

    for image_file in image_files:
        try:
            image_path = os.path.join(image_folder, image_file)
            
            # 验证输入图像
            if not os.path.exists(image_path):
                print(f"⚠️ 输入图像不存在: {image_path}")
                error_count += 1
                continue
                
            # 开始计时（总时间）
            total_start = torch.cuda.Event(enable_timing=True)
            total_end = torch.cuda.Event(enable_timing=True)
            total_start.record()
                
            # 读取原始图像
            original_image = cv2.imread(image_path)
            if original_image is None:
                print(f"⚠️ 无法读取图像: {image_path}")
                error_count += 1
                continue
                
            # 调整亮度
            adjusted_image = adjust_image_brightness_dynamically(original_image, BRIGHTNESS_TARGET_MIN, BRIGHTNESS_TARGET_MAX)
            temp_path = os.path.join(output_folder, 'temp.png')
            cv2.imwrite(temp_path, original_image)
            
            # 识别玻璃区域（测量推理时间）
            try:
                img_pil = Image.open(image_path)
                if img_pil.mode != 'RGB':
                    img_pil = img_pil.convert('RGB')
                w, h = img_pil.size
                img_var = Variable(img_transform(img_pil).unsqueeze(0)).cuda(device_ids[0])
                
                # 测量推理时间
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)
                
                start_time.record()
                with torch.no_grad():
                    f1, f2, f3 = net(img_var)
                end_time.record()
                
                # 等待GPU操作完成
                torch.cuda.synchronize()
                inference_time = start_time.elapsed_time(end_time)  # 毫秒
                inference_times.append(inference_time)
                
                f3 = f3.data.squeeze(0).cpu()
                f3_resized = np.array(transforms.Resize((h, w))(to_pil(f3)))
            except Exception as e:
                print(f"⚠️ 玻璃检测失败 {image_file}: {e}")
                error_count += 1
                continue

            # 应用CRF（如果启用）
            if args['crf']:
                adjusted_image = cv2.imread(temp_path)
                if adjusted_image is None:
                    print(f"⚠️ 无法读取调整后的图像: {temp_path}")
                    error_count += 1
                    continue
                f3_resized = crf_refine(adjusted_image, f3_resized,crf_params)
            
            # 生成玻璃掩码
            glass_mask = (f3_resized > glass_threshold).astype(np.uint8) if f3_resized.max() > glass_threshold else np.zeros((h, w), dtype=np.uint8)
            image_mask = glass_mask * 255

            # 保存结果
            result_path = os.path.join(output_folder, image_file)
            if not cv2.imwrite(result_path, image_mask):
                print(f"⚠️ 无法保存结果图像: {result_path}")
                error_count += 1
                continue
                
            # 记录总时间
            total_end.record()
            torch.cuda.synchronize()
            total_time = total_start.elapsed_time(total_end)  # 毫秒
            total_times.append(total_time)
                
            processed_count += 1
            
            # 每处理100张图片输出一次性能指标
            if processed_count % 100 == 0:
                current_inference_avg = np.mean(inference_times[-100:])
                current_total_avg = np.mean(total_times[-100:])
                current_fps = 1000.0 / current_total_avg
                print(f"\n当前性能 ({processed_count}/{len(image_files)}):")
                print(f"  推理延迟: {current_inference_avg:.2f}ms")
                print(f"  总延迟: {current_total_avg:.2f}ms")
                print(f"  FPS: {current_fps:.2f}")
            
        except Exception as e:
            print(f"⚠️ 处理图像 {image_file} 时出错: {e}")
            error_count += 1
            continue
            
    # 清理临时文件
    if os.path.exists(os.path.join(output_folder, 'temp.png')):
        os.remove(os.path.join(output_folder, 'temp.png'))
        
    # 计算最终性能指标（跳过前5张图片的预热时间）
    if len(inference_times) > 5:
        avg_inference_time = np.mean(inference_times[5:])
        avg_total_time = np.mean(total_times[5:])
        fps = 1000.0 / avg_total_time  # 转换为FPS
        
        print(f"\n🚀 最终性能指标:")
        print(f"   - 平均推理延迟: {avg_inference_time:.2f}ms")
        print(f"   - 平均总延迟: {avg_total_time:.2f}ms")
        print(f"   - 平均FPS: {fps:.2f}")
        
    print(f"\n✅ 处理完成:")
    print(f"   - 成功: {processed_count}/{len(image_files)}")
    print(f"   - 失败: {error_count}/{len(image_files)}")
    
    if processed_count == 0:
        raise Exception("没有成功处理任何图像")

    return brightness_list, feature_points_list

def path_set(tempdata_path,new_path):
    gdd_results_root = os.path.join(tempdata_path,new_path)
    return gdd_results_root



def evaluate_results(output_folder,gt_folder):
    # 获取所有图像文件
    image_files = [f for f in os.listdir(output_folder) if f != 'temp.png']
    if not image_files:
        raise Exception("输出文件夹为空，没有可评估的图像")
        
    # 打乱图像文件列表
    random.shuffle(image_files)
    # 计算每组的大致数量
    group_size = len(image_files) // 1
    # 分割成组
    groups = [image_files[i*group_size:(i+1)*group_size] for i in range(1)]
    if len(image_files) % 1 != 0:
        # 处理剩余的文件
        remaining = image_files[3*group_size:]
        for i, file in enumerate(remaining):
            groups[i].append(file)

    all_results = []
    for group in groups:
        count = 0
        iou, acc, fm, mae, ber,aber = 0, 0, 0, 0,0,0 
        for image_file in group:
            try:
                temp_name = image_file.split('.')[0]
                image_path = os.path.join(output_folder, image_file)
                if '.png' in image_file:
                    gt_name = image_file
                else:
                    gt_name = temp_name + '.png'
                gt_path = os.path.join(gt_folder, gt_name)
                
                # 验证文件存在
                if not os.path.exists(image_path):
                    print(f"⚠️ 跳过不存在的预测图像: {image_path}")
                    continue
                if not os.path.exists(gt_path):
                    print(f"⚠️ 跳过不存在的真值图像: {gt_path}")
                    continue
                    
                # 读取图像
                image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
                if image is None:
                    print(f"⚠️ 无法读取预测图像: {image_path}")
                    continue
                    
                gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
                if gt_mask is None:
                    print(f"⚠️ 无法读取真值图像: {gt_path}")
                    continue
                    
                # 确保尺寸一致
                if gt_mask.shape != image.shape:
                    print(f"⚠️ 图像尺寸不匹配，调整大小: {image_file}")
                    gt_mask = cv2.resize(gt_mask, (image.shape[1], image.shape[0]))
                
                prediction = image.astype(np.float32) / 255.0
                gt = gt_mask.astype(np.float32) / 255.0
                
                tiou = compute_iou(prediction, gt)
                tacc = compute_acc(prediction, gt)
                precision, recall = compute_precision_recall(prediction, gt)
                tfm = compute_fmeasure(precision, recall)
                tmae = compute_mae(prediction, gt)
                tber = compute_ber(prediction, gt)
                taber = compute_aber(prediction,gt)
                
                count += 1
                iou += tiou
                acc += tacc
                fm += tfm
                mae += tmae
                ber += tber
                aber +=taber
                
            except Exception as e:
                print(f"⚠️ 处理图像 {image_file} 时出错: {e}")
                continue
                
        if count > 0:
            iou = iou / count
            acc = acc / count
            fm = fm / count
            mae = mae / count
            ber = ber / count
            aber = aber/count
            all_results.append((iou, acc, fm, mae, ber,aber))
        else:
            print("⚠️ 当前组中没有成功处理的图像")
            
    if not all_results:
        raise Exception("没有成功评估任何图像")
        
    #all results / len(groups) get avg results
    avg_iou = sum([result[0] for result in all_results]) / len(all_results)
    avg_acc = sum([result[1] for result in all_results]) / len(all_results)
    avg_fm = sum([result[2] for result in all_results]) / len(all_results)
    avg_mae = sum([result[3] for result in all_results]) / len(all_results)
    avg_ber = sum([result[4] for result in all_results]) / len(all_results)
    avg_aber = sum([result[5] for result in all_results]) / len(all_results)

    return avg_iou, avg_acc, avg_fm, avg_mae, avg_ber,avg_aber

def main():
    print("\n=== 硬件信息 ===")
    device_name = torch.cuda.get_device_name(device_ids[0])
    print(f"🖥️  GPU: {device_name}")
    
    # Step 1: Load GDNet model
    net = GDNet(backbone_path=backbone_path).cuda(device_ids[0])
    if len(args['snapshot']) > 0:
        print(f'Load snapshot {args["snapshot"]} for testing')
        temp_path = torch.load(os.path.join(ckpt_path, exp_name, args['snapshot'] + '.pth'))
        if args['snapshot']=='200':
            net.load_state_dict(temp_path)
        else:
            net.load_state_dict(temp_path['model'])
        print(f'Load {os.path.join(ckpt_path, exp_name, args["snapshot"] + ".pth")} succeed!')

    net.eval()
    
    # 计算模型复杂度指标
    print("\n=== 模型复杂度分析 ===")
    
    # 1. 参数量统计
    total_params = sum(p.numel() for p in net.parameters())
    trainable_params = sum(p.numel() for p in net.parameters() if p.requires_grad)
    print(f"📊 总参数量: {total_params/1e6:.2f}M")
    print(f"📊 可训练参数: {trainable_params/1e6:.2f}M")
    
    # 2. FLOPs计算
    try:
        from thop import profile
        dummy_input = torch.randn(1, 3, args['scale'], args['scale']).cuda(device_ids[0])
        flops, _ = profile(net, inputs=(dummy_input, ), verbose=False)
        print(f"⚡ FLOPs: {flops/1e9:.2f}G")
    except ImportError:
        print("⚠️ 未安装thop库，无法计算FLOPs。请运行: pip install thop")
    except Exception as e:
        print(f"⚠️ FLOPs计算失败: {e}")
    
    # 3. 内存使用分析
    print("\n=== 内存使用分析 ===")
    torch.cuda.reset_peak_memory_stats(device_ids[0])
    torch.cuda.empty_cache()
    
    # 进行一次推理来测量内存使用
    dummy_input = torch.randn(1, 3, args['scale'], args['scale']).cuda(device_ids[0])
    with torch.no_grad():
        _ = net(dummy_input)
    
    peak_memory = torch.cuda.max_memory_allocated(device_ids[0]) / (1024*1024)  # 转换为MB
    current_memory = torch.cuda.memory_allocated(device_ids[0]) / (1024*1024)
    reserved_memory = torch.cuda.memory_reserved(device_ids[0]) / (1024*1024)
    
    print(f"💾 峰值显存: {peak_memory:.2f}MB")
    print(f"💾 当前显存: {current_memory:.2f}MB")
    print(f"💾 预留显存: {reserved_memory:.2f}MB")
    
    # 4. 延迟测试
    print("\n=== 延迟测试（包含预处理和后处理） ===")
    with torch.no_grad():
        # 创建计时器
        start_time = torch.cuda.Event(enable_timing=True)
        end_time = torch.cuda.Event(enable_timing=True)
        total_start_time = torch.cuda.Event(enable_timing=True)
        total_end_time = torch.cuda.Event(enable_timing=True)
        
        # 准备测试数据
        test_image = np.random.randint(0, 255, (args['scale'], args['scale'], 3), dtype=np.uint8)
        pil_image = Image.fromarray(test_image)
        
        # 预热
        print("预热中...")
        for _ in range(10):
            img_tensor = img_transform(pil_image).unsqueeze(0).cuda(device_ids[0])
            _ = net(img_tensor)
            
        # 正式测量
        inference_times = []
        total_times = []
        
        print(f"开始测量（100次）...")
        for _ in range(100):
            # 测量总时间（包含预处理和后处理）
            total_start_time.record()
            
            # 预处理
            img_tensor = img_transform(pil_image).unsqueeze(0).cuda(device_ids[0])
            
            # 推理（单独测量）
            start_time.record()
            outputs = net(img_tensor)
            end_time.record()
            
            # 后处理
            f3 = outputs[2].data.squeeze(0).cpu()
            f3_resized = np.array(transforms.Resize((args['scale'], args['scale']))(to_pil(f3)))
            glass_mask = (f3_resized > args['glass_threshold']).astype(np.uint8)
            
            total_end_time.record()
            
            # 同步并记录时间
            torch.cuda.synchronize()
            inference_times.append(start_time.elapsed_time(end_time))
            total_times.append(total_start_time.elapsed_time(total_end_time))
    
    # 计算统计信息
    inference_mean = np.mean(inference_times)
    inference_std = np.std(inference_times)
    total_mean = np.mean(total_times)
    total_std = np.std(total_times)
    
    inference_fps = 1000 / inference_mean
    total_fps = 1000 / total_mean
    
    print("\n⏱️  延迟统计:")
    print(f"   推理延迟: {inference_mean:.2f}ms ± {inference_std:.2f}ms")
    print(f"   总延迟: {total_mean:.2f}ms ± {total_std:.2f}ms")
    print(f"   预处理+后处理延迟: {(total_mean - inference_mean):.2f}ms")
    print("\n🚀 帧率统计:")
    print(f"   理论峰值FPS (仅推理): {inference_fps:.2f}")
    print(f"   实际FPS (全流程): {total_fps:.2f}")
    print("="*50)

    current = "glass_mask_base"
    data_path = "/home/<USER>/Documents/ig_slam_maskdata/test_GDD"
    
    # 验证数据路径
    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        return
        
    # 设置输入输出路径
    image_folder = path_set(data_path,"image")
    output_folder = path_set(data_path,current)
    gt_folder = path_set(data_path,"mask")
    
    # 验证必要的文件夹
    if not os.path.exists(image_folder):
        print(f"❌ 图像文件夹不存在: {image_folder}")
        return
    if not os.path.exists(gt_folder):
        print(f"❌ 真值文件夹不存在: {gt_folder}")
        return
        
    print(f"📁 输入图像: {image_folder}")
    print(f"📁 输出结果: {output_folder}")
    print(f"📁 真值标签: {gt_folder}")

    # Process images
    if args['crf']:
        print("\n🔍 开始CRF参数网格搜索...")
        best_iou = -1
        best_params = None
        best_metrics = None
        
        # 生成所有参数组合
        param_combinations = list(zip(
            crf_params_grid['sxy1'],
            crf_params_grid['sxy2'],
            crf_params_grid['srgb'],
            crf_params_grid['compat1'],
            crf_params_grid['compat2']
        ))
        
        for i, params in enumerate(param_combinations):
            print(f"\n⚙️ 测试参数组合 {i+1}/{len(param_combinations)}:")
            print(f"   sxy1={params[0]}, sxy2={params[1]}, srgb={params[2]}, compat1={params[3]}, compat2={params[4]}")
            
            current_output = f"{output_folder}_crf_{i+1}"
            try:
                brightness_list, feature_points_list = detect_glass_and_adjust_brightness(
                    image_folder, current_output, net, args['glass_threshold'], params
                )
                
                iou, acc, fm, mae, ber, aber = evaluate_results(current_output, gt_folder)
                
                print(f"\n📊 评估结果:")
                print(f"   IoU: {iou:.4f}")
                print(f"   Accuracy: {acc:.4f}")
                print(f"   F-measure: {fm:.4f}")
                print(f"   MAE: {mae:.4f}")
                print(f"   BER: {ber:.4f}")
                print(f"   ABER: {aber:.4f}")
                
                # 更新最佳结果
                if iou > best_iou:
                    best_iou = iou
                    best_params = params
                    best_metrics = (iou, acc, fm, mae, ber, aber)
                    print(f"\n🌟 发现新的最佳参数组合!")
                
            except Exception as e:
                print(f"❌ 参数组合测试失败: {e}")
                continue
        
        # 打印最终最佳结果
        if best_params is not None:
            print(f"\n🏆 最佳CRF参数组合:")
            print(f"   sxy1={best_params[0]}, sxy2={best_params[1]}, srgb={best_params[2]}")
            print(f"   compat1={best_params[3]}, compat2={best_params[4]}")
            print(f"\n📈 最佳评估结果:")
            print(f"   IoU: {best_metrics[0]:.4f}")
            print(f"   Accuracy: {best_metrics[1]:.4f}")
            print(f"   F-measure: {best_metrics[2]:.4f}")
            print(f"   MAE: {best_metrics[3]:.4f}")
            print(f"   BER: {best_metrics[4]:.4f}")
            print(f"   ABER: {best_metrics[5]:.4f}")
        else:
            print("\n❌ 未找到有效的参数组合")
            
    else:
        # 使用默认CRF参数
        default_params = [0.5, 20, 2, 10, 15]
        detect_glass_and_adjust_brightness(image_folder, output_folder, net, args['glass_threshold'], default_params)
        try:
            iou, acc, fm, mae, ber, aber = evaluate_results(output_folder, gt_folder)
            print(f"\n📊 评估结果:")
            print(f"   IoU: {iou:.4f}")
            print(f"   Accuracy: {acc:.4f}")
            print(f"   F-measure: {fm:.4f}")
            print(f"   MAE: {mae:.4f}")
            print(f"   BER: {ber:.4f}")
            print(f"   ABER: {aber:.4f}")
        except Exception as e:
            print(f"❌ 评估失败: {e}")

if __name__ == '__main__':
    main()
