# utils/utils.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 通用工具函数
# 包含模型保存加载、优化器创建、进度显示等功能
# -----------------------------------------------------------------------------

import os
import torch
import time
import shutil
from torch.optim.lr_scheduler import CosineAnnealingLR, StepLR
import config
import torch.nn as nn
from typing import Dict

class AverageMeter:
    """计算和存储平均值和当前值"""
    def __init__(self, name, fmt=':f'):
        self.name = name
        self.fmt = fmt
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count

    def __str__(self):
        fmtstr = '{name} {val' + self.fmt + '} ({avg' + self.fmt + '})'
        return fmtstr.format(**self.__dict__)

class ProgressMeter:
    """进度显示器"""
    def __init__(self, num_batches, meters, prefix=""):
        self.batch_fmtstr = self._get_batch_fmtstr(num_batches)
        self.meters = meters
        self.prefix = prefix

    def display(self, batch):
        entries = [self.prefix + self.batch_fmtstr.format(batch)]
        entries += [str(meter) for meter in self.meters]
        print('\t'.join(entries))

    def _get_batch_fmtstr(self, num_batches):
        num_digits = len(str(num_batches // 1))
        fmt = '{:' + str(num_digits) + 'd}'
        return '[' + fmt + '/' + fmt.format(num_batches) + ']'

def save_checkpoint(state, is_best, checkpoint_dir, filename='checkpoint.pth'):
    """
    保存模型检查点
    Args:
        state: 包含模型状态的字典
        is_best: 是否为最佳模型
        checkpoint_dir: 检查点保存目录
        filename: 文件名
    """
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    filepath = os.path.join(checkpoint_dir, filename)
    torch.save(state, filepath)
    
    if is_best:
        best_filepath = os.path.join(checkpoint_dir, 'model_best.pth')
        shutil.copyfile(filepath, best_filepath)
    
    print(f"📁 检查点已保存: {filepath}")

def load_checkpoint(checkpoint_path: str, model: nn.Module) -> Dict:
    """
    加载检查点
    Args:
        checkpoint_path: 检查点路径
        model: 模型实例
    Returns:
        checkpoint: 检查点字典
    """
    print(f"📦 正在加载检查点: {checkpoint_path}")
    
    if not os.path.exists(checkpoint_path):
        print(f"⚠️ 检查点文件不存在: {checkpoint_path}")
        return {}
        
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # 处理不同格式的检查点
        if isinstance(checkpoint, dict):
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                # 假设整个字典就是状态字典
                state_dict = checkpoint
        else:
            # 假设直接是状态字典
            state_dict = checkpoint
            
        # 尝试加载状态字典
        try:
            model.load_state_dict(state_dict, strict=False)
            print("✅ 检查点加载成功!")
        except Exception as e:
            print(f"⚠️ 加载状态字典时出错: {str(e)}")
            # 尝试清理状态字典中的模块前缀
            cleaned_state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
            try:
                model.load_state_dict(cleaned_state_dict, strict=False)
                print("✅ 使用清理后的状态字典加载成功!")
            except Exception as e2:
                print(f"❌ 清理后的状态字典加载也失败: {str(e2)}")
                return {}
                
        return checkpoint
        
    except Exception as e:
        print(f"❌ 加载检查点时出错: {str(e)}")
        return {}

def create_optimizer(model, optimizer_type='adamw', lr=1e-4, weight_decay=1e-4, vit_lr_ratio=0.1):
    """
    创建优化器 - 支持差分学习率 (安静版)
    """
    vit_params = []
    other_params = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            if 'backbone' in name or 'vit_backbone' in name:
                vit_params.append(param)
            else:
                other_params.append(param)
    
    param_groups = [
        {'params': vit_params, 'lr': lr * vit_lr_ratio, 'weight_decay': weight_decay},
        {'params': other_params, 'lr': lr, 'weight_decay': weight_decay}
    ]
    
    if optimizer_type.lower() == 'adamw':
        optimizer = torch.optim.AdamW(param_groups)
    elif optimizer_type.lower() == 'adam':
        optimizer = torch.optim.Adam(param_groups)
    elif optimizer_type.lower() == 'sgd':
        optimizer = torch.optim.SGD(param_groups, momentum=0.9)
    else:
        raise ValueError(f"不支持的优化器类型: {optimizer_type}")
    
    print(f"🔧 创建差分学习率优化器: {optimizer_type.upper()}")
    print(f"  - ViT骨干网络: LR = {lr * vit_lr_ratio:.6f} (基础学习率的{vit_lr_ratio}倍)")
    print(f"  - 其他模块: LR = {lr:.6f}")
    print(f"  - 权重衰减: {weight_decay}")
    print(f"  - ViT参数数量: {len(vit_params)}")
    print(f"  - 其他参数数量: {len(other_params)}")
    
    return optimizer

def create_scheduler(optimizer, scheduler_type='cosine', epochs=100, step_size=30, gamma=0.1):
    """
    创建学习率调度器
    Args:
        optimizer: 优化器
        scheduler_type: 调度器类型
        epochs: 总训练轮数
        step_size: StepLR的步长
        gamma: StepLR的衰减因子
    """
    if scheduler_type.lower() == 'cosine':
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=epochs,
            eta_min=1e-7
        )
    elif scheduler_type.lower() == 'step':
        scheduler = StepLR(
            optimizer,
            step_size=step_size,
            gamma=gamma
        )
    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")
    
    print(f"📈 创建学习率调度器: {scheduler_type.upper()}")
    return scheduler

def adjust_learning_rate(optimizer, epoch, initial_lr, warmup_epochs=5):
    """
    调整学习率（预热）
    Args:
        optimizer: 优化器
        epoch: 当前轮数
        initial_lr: 初始学习率
        warmup_epochs: 预热轮数
    """
    if epoch < warmup_epochs:
        lr = initial_lr * (epoch + 1) / warmup_epochs
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        return lr
    return None

def count_parameters(model):
    """
    统计模型参数数量
    Args:
        model: 模型
    Returns:
        总参数数量和可训练参数数量
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"📊 模型参数统计:")
    print(f"  - 总参数数量: {total_params:,} ({total_params/1e6:.2f}M)")
    print(f"  - 可训练参数: {trainable_params:,} ({trainable_params/1e6:.2f}M)")
    
    return total_params, trainable_params

def setup_seed(seed=42):
    """
    设置随机种子以确保可复现性
    Args:
        seed: 随机种子
    """
    import random
    import numpy as np
    
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    
    # 确保卷积算法的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    print(f"🎲 设置随机种子: {seed}")

def get_device():
    """获取可用设备"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"🚀 使用GPU: {torch.cuda.get_device_name()}")
        print(f"📊 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    else:
        device = torch.device('cpu')
        print("💻 使用CPU")
    
    return device

def format_time(seconds):
    """
    格式化时间显示
    Args:
        seconds: 秒数
    Returns:
        格式化的时间字符串
    """
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
    else:
        return f"{int(minutes):02d}:{int(seconds):02d}"

def create_experiment_dir(base_dir, experiment_name=None):
    """
    创建实验目录
    Args:
        base_dir: 基础目录
        experiment_name: 实验名称
    Returns:
        实验目录路径
    """
    if experiment_name is None:
        # 使用时间戳作为实验名称
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        experiment_name = f"exp_{timestamp}"
    
    exp_dir = os.path.join(base_dir, experiment_name)
    os.makedirs(exp_dir, exist_ok=True)
    
    # 创建子目录
    subdirs = ['checkpoints', 'logs', 'visualizations']
    for subdir in subdirs:
        os.makedirs(os.path.join(exp_dir, subdir), exist_ok=True)
    
    print(f"📁 创建实验目录: {exp_dir}")
    return exp_dir

def log_metrics(metrics, epoch, log_file=None):
    """
    记录指标到文件
    Args:
        metrics: 指标字典
        epoch: 当前轮数
        log_file: 日志文件路径
    """
    log_str = f"Epoch {epoch:03d}: "
    log_str += ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
    
    print(log_str)
    
    if log_file is not None:
        with open(log_file, 'a') as f:
            f.write(log_str + '\n')

class Timer:
    """计时器工具"""
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.start_time = time.time()
        self.last_time = self.start_time
    
    def elapsed(self):
        """返回总经过时间"""
        return time.time() - self.start_time
    
    def interval(self):
        """返回距离上次调用的间隔时间"""
        current_time = time.time()
        interval = current_time - self.last_time
        self.last_time = current_time
        return interval

# 导出所有函数和类
__all__ = [
    'AverageMeter',
    'ProgressMeter',
    'save_checkpoint',
    'load_checkpoint',
    'create_optimizer',
    'create_scheduler',
    'adjust_learning_rate',
    'count_parameters',
    'setup_seed',
    'get_device',
    'format_time',
    'create_experiment_dir',
    'log_metrics',
    'Timer'
] 