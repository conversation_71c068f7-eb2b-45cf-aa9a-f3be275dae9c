# config_transxnet.py
# 专用于TransXNet/TransXGlassNet训练的配置文件

PROJECT_ROOT = '/home/<USER>/ws/IG_SLAM'
OUTPUT_DIR = '/home/<USER>/ws/IG_SLAM/ProteusGlassDiffusion/checkpoints'
STAGE1_BATCH_SIZE = 8
STAGE1_IMG_SIZE = (420, 420)
STAGE1_LR = 2e-5
STAGE1_WEIGHT_DECAY = 0.01
STAGE1_EPOCHS = 200
CRF_ITER = 3
CRF_TRAINABLE = False
NUM_WORKERS = 4
GLASS_DATALOADER = {
    'split_ratio': 0.9,
    'random_seed': 42,
    'glass_augmentation': 'moderate'
}
SCHEDULER_TYPE = 'cosine'
STEP_SCHEDULER_STEP_SIZE = 30
STEP_SCHEDULER_GAMMA = 0.1
MIXED_PRECISION = True

# TransXNet权重路径（请根据实际权重文件名修改）
TRANSXNET_PRETRAINED_PATH = '/home/<USER>/ws/IG_SLAM/TransXNet/convformer-s.pth' 