# RTGlassNetv2 - 实时玻璃分割网络v2

## 📋 概述

RTGlassNetv2是基于LightLCFI架构的实时玻璃分割网络，使用InceptionNeXt作为骨干网络，结合SCSA注意力机制和CRF后处理，专为实时玻璃检测设计。

## 🏗️ 架构特点

### 核心组件
1. **InceptionNeXt骨干网络** - 高效的视觉Transformer骨干
2. **LightLCFI模块** - 轻量级大上下文特征集成
3. **SCSA注意力机制** - 空间-通道自注意力
4. **LightEdgeEnhancer** - 边缘特征增强
5. **SimplifiedDiffCRF** - 可微分CRF后处理

### 架构优势
- ✅ **轻量化**: 31.9M参数，比ResNeXt-101减少67%
- ✅ **实时性**: 9.75 FPS (224x224输入)
- ✅ **高精度**: 多尺度特征融合 + 边缘增强
- ✅ **稳定性**: 数值稳定性优化，避免梯度爆炸

## 📁 文件结构

```
GlassDiffusion/models/
├── RTGlassNetv2.py          # 主模型文件
├── loss_rtgv2.py           # 损失函数
└── diff_crf.py             # CRF后处理模块

ig_glass/
├── attention_scsa.py       # SCSA注意力机制
└── glass_dataloader.py     # 数据加载器

train_rtgv2.py              # 训练脚本
test_rtgv2.py               # 测试脚本
```

## 🚀 快速开始

### 1. 环境要求
```bash
pip install torch torchvision timm
```

### 2. 模型测试
```bash
python test_rtgv2.py
```

### 3. 训练模型
```bash
python train_rtgv2.py --mode train
```

### 4. 评估模型
```bash
python train_rtgv2.py --mode eval
```

## 📊 模型配置

### 默认配置
```python
model = RTGlassNetv2(
    backbone_type='inceptionnext_tiny',  # 骨干网络类型
    crf_iter=3,                          # CRF迭代次数
    crf_bilateral_weight=10.0            # CRF双边权重
)
```

### 损失函数配置
```python
criterion = RTGlassNetv2Loss(
    focal_weight=0.6,        # Focal Loss权重
    iou_weight=0.4,          # IoU Loss权重
    edge_weight=0.2,         # 边缘Loss权重
    crf_weight=0.1,          # CRF Loss权重
    consistency_weight=0.1   # 一致性Loss权重
)
```

## 🔧 技术细节

### 通道数配置
- **InceptionNeXt-Tiny**: [96, 192, 384, 768]
- **LightLCFI输出**: [24, 48, 96, 192] (输入//4)
- **特征融合**: 336通道 (192+96+48)
- **最终输出**: 280通道 (256+24)

### 性能指标
- **参数量**: 31,969,323
- **模型大小**: 121.95 MB
- **推理速度**: 9.75 FPS (224x224)
- **内存占用**: 低

## 🎯 与原始GDNetSCSA的对比

| 特性 | GDNetSCSA | RTGlassNetv2 |
|------|-----------|--------------|
| 骨干网络 | ResNeXt-101 | InceptionNeXt-Tiny |
| 参数量 | ~97M | 31.9M |
| 推理速度 | ~20-30 FPS | 9.75 FPS |
| 架构 | 传统CNN | 轻量化设计 |
| 特征融合 | FPN | LightLCFI |
| 注意力机制 | SCSA | SCSA |
| CRF后处理 | ✓ | ✓ |

## 📈 训练建议

### 1. 学习率策略
```python
optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100)
```

### 2. 批次大小
- **GPU内存充足**: batch_size=8-16
- **GPU内存有限**: batch_size=4-8

### 3. 数据增强
```python
# 建议的数据增强策略
transforms = [
    RandomHorizontalFlip(p=0.5),
    RandomRotation(degrees=10),
    ColorJitter(brightness=0.2, contrast=0.2),
    RandomResizedCrop(size=(224, 224), scale=(0.8, 1.0))
]
```

### 4. 损失权重调优
```python
# 根据训练情况调整权重
if iou_metric < 0.8:
    criterion.iou_weight *= 1.1  # 增加IoU权重
if edge_loss > 0.5:
    criterion.edge_weight *= 0.9  # 减少边缘权重
```

## 🐛 常见问题

### 1. 内存不足
```python
# 减少批次大小
batch_size = 4

# 使用混合精度训练
from torch.cuda.amp import autocast, GradScaler
scaler = GradScaler()
```

### 2. 训练不稳定
```python
# 降低学习率
optimizer = optim.AdamW(model.parameters(), lr=0.0005)

# 增加权重衰减
optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.02)
```

### 3. 精度不达标
```python
# 增加训练轮数
num_epochs = 200

# 调整损失权重
criterion = RTGlassNetv2Loss(
    focal_weight=0.5,
    iou_weight=0.5,  # 增加IoU权重
    edge_weight=0.15,
    crf_weight=0.1,
    consistency_weight=0.1
)
```

## 📝 更新日志

### v2.0 (当前版本)
- ✅ 使用InceptionNeXt替代ResNeXt
- ✅ 集成LightLCFI架构
- ✅ 优化通道数配置
- ✅ 添加完整的损失函数
- ✅ 提供训练和评估脚本

### 计划功能
- 🔄 支持更多骨干网络
- 🔄 添加知识蒸馏功能
- 🔄 优化推理速度
- 🔄 支持多尺度输入

## 🤝 贡献

欢迎提交Issue和Pull Request来改进RTGlassNetv2！

## 📄 许可证

本项目采用MIT许可证。 