import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# 🔧 修复：使用timm库导入InceptionNeXt模型
try:
    import timm
    INCEPTIONNEXT_AVAILABLE = True
    print("✅ InceptionNeXt models (timm) imported successfully")
except ImportError as e:
    print(f"❌ Warning: timm not available: {e}")
    INCEPTIONNEXT_AVAILABLE = False

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from ig_glass.attention_scsa import SCSA
# 🔧 导入新的TrainableCRF
from trainable_crf import TrainableCRF

# 1. LightLCFI模块（从gdnet_scsa.py复制）
class LightLCFI(nn.Module):
    """Lightweight Large Context Feature Integration module with depthwise separable convolutions"""
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(LightLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = int(input_channels / 4)
        self.channels_double = int(input_channels / 2)
        self.dr1 = dr1
        self.dr2 = dr2
        self.dr3 = dr3
        self.dr4 = dr4
        self.padding1 = 1 * dr1
        self.padding2 = 2 * dr2
        self.padding3 = 3 * dr3
        self.padding4 = 4 * dr4

        # Channel reduction with 1x1 convolutions
        self.p1_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p1
        self.p1_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (3, 1), 1, padding=(self.padding1, 0),
                      dilation=(self.dr1, 1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (1, 3), 1, padding=(0, self.padding1),
                      dilation=(1, self.dr1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p2
        self.p2_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (5, 1), 1, padding=(self.padding2, 0),
                      dilation=(self.dr2, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 5), 1, padding=(0, self.padding2),
                      dilation=(1, self.dr2), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p3
        self.p3_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (7, 1), 1, padding=(self.padding3, 0),
                      dilation=(self.dr3, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 7), 1, padding=(0, self.padding3),
                      dilation=(1, self.dr3), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p4
        self.p4_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (9, 1), 1, padding=(self.padding4, 0),
                      dilation=(self.dr4, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 9), 1, padding=(0, self.padding4),
                      dilation=(1, self.dr4), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Use SCSA for attention instead of CBAM
        self.scsa = SCSA(
            dim=self.input_channels,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )

        # Final channel reduction
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU())

    def forward(self, x):
        p1_input = self.p1_channel_reduction(x)
        p1 = self.p1_fusion(torch.cat((self.p1_d1(p1_input), self.p1_d2(p1_input)), 1))

        p2_input = torch.cat((self.p2_channel_reduction(x), p1), 1)
        p2 = self.p2_fusion(torch.cat((self.p2_d1(p2_input), self.p2_d2(p2_input)), 1))

        p3_input = torch.cat((self.p3_channel_reduction(x), p2), 1)
        p3 = self.p3_fusion(torch.cat((self.p3_d1(p3_input), self.p3_d2(p3_input)), 1))

        p4_input = torch.cat((self.p4_channel_reduction(x), p3), 1)
        p4 = self.p4_fusion(torch.cat((self.p4_d1(p4_input), self.p4_d2(p4_input)), 1))

        # 使用SCSA处理特征
        combined_features = torch.cat((p1, p2, p3, p4), 1)
        attended_features = self.scsa(combined_features)
        channel_reduction = self.channel_reduction(attended_features)

        return channel_reduction

# 2. LightEdgeEnhancer（从gdnet_scsa.py复制）
class LightEdgeEnhancer(nn.Module):
    """Lightweight module to enhance edge features"""
    def __init__(self, in_channels):
        super(LightEdgeEnhancer, self).__init__()
        # Depthwise separable convolution
        self.edge_conv = nn.Sequential(
            # Depthwise
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            # Pointwise
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # Laplacian kernel for edge detection
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
        
    def forward(self, x):
        # Apply edge-aware enhancement
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        
        # Apply edge-aware convolution
        enhanced = self.edge_conv(x)
        
        # Combine with original features with edge attention
        return x + enhanced * edge_features

# 3. FeatureExtractor（支持InceptionNeXt）
class FeatureExtractor(nn.Module):
    """
    多尺度特征提取器，使用timm库并正确加载预训练权重
    """
    def __init__(self, backbone_type='inceptionnext_base', pretrained=True, drop_path_rate=0.1):
        super().__init__()
        self.backbone_type = backbone_type.lower()
        
        if not INCEPTIONNEXT_AVAILABLE:
            raise ImportError("timm library not available!")
        
        # 修正: 使用正确的timm模型名称，并设置pretrained=True
        if 'tiny' in self.backbone_type:
            model_name = 'inception_next_tiny.sail_in1k'
            self.out_channels_list = [96, 192, 384, 768]
        elif 'small' in self.backbone_type:
            model_name = 'inception_next_small.sail_in1k'
            self.out_channels_list = [96, 192, 384, 768]
        elif 'base' in self.backbone_type:
            model_name = 'inception_next_base.sail_in1k'
            self.out_channels_list = [128, 256, 512, 1024]
        else:
            raise ValueError(f"Unknown InceptionNeXt backbone: {backbone_type}")

        print(f"🔧 创建backbone: {model_name} (pretrained={pretrained})")
        try:
            # 使用与RTGlassNet相同的配置
            self.backbone = timm.create_model(
                model_name,
                pretrained=pretrained,
                features_only=True,
                out_indices=(0, 1, 2, 3),  # 对应C2, C3, C4, C5
                drop_path_rate=drop_path_rate
            )
            print("✅ Backbone创建成功")
            
            # 打印每层的输出通道数
            if hasattr(self.backbone, 'feature_info'):
                print("📊 特征层信息:")
                for idx, info in enumerate(self.backbone.feature_info):
                    print(f"   Layer {idx}: {info['num_chs']} channels")
                    
        except Exception as e:
            print(f"❌ Backbone创建失败: {str(e)}")
            raise

    def forward(self, x):
        # 直接返回timm骨干网络提取的特征
        feats = self.backbone(x)
        return feats

    @property
    def out_channels(self):
        """返回backbone各层的输出通道数"""
        if hasattr(self.backbone, 'feature_info'):
            return [info['num_chs'] for info in self.backbone.feature_info]
        return self.out_channels_list

# 4. RTGlassNetv3主干 - 使用新的TrainableCRF
class RTGlassNetv3(nn.Module):
    """
    实时玻璃分割v3架构：Backbone + LightLCFI + SCSA + TrainableCRF
    使用新的TrainableCRF替代SimplifiedDiffCRF，实现真正的端到端训练
    """
    def __init__(self, 
                 backbone_type='inceptionnext_base', 
                 crf_iter=5,
                 crf_initial_bilateral_weight=10.0,
                 crf_initial_gaussian_weight=3.0,
                 crf_initial_bilateral_spatial_sigma=40.0,
                 crf_initial_bilateral_color_sigma=5.0,
                 crf_initial_gaussian_sigma=3.0,
                 drop_path_rate=0.1):
        super().__init__()
        self.feature_extractor = FeatureExtractor(backbone_type=backbone_type, drop_path_rate=drop_path_rate)
        in_channels_list = self.feature_extractor.out_channels
        print(f"✅ 动态获取的Backbone通道数: {in_channels_list}")

        # --- 动态计算解码器各模块的通道数 ---
        # LightLCFI模块将通道数减少4倍
        c5_out = in_channels_list[3] // 4
        c4_out = in_channels_list[2] // 4
        c3_out = in_channels_list[1] // 4
        c2_out = in_channels_list[0] // 4

        # h_fusion模块的输入通道数 = 3个高级特征拼接
        h_fusion_in_dim = c5_out + c4_out + c3_out
        # SCSA模块要求通道数是head_num的倍数，这里统一为8的倍数以保证兼容性
        h_fusion_in_dim = ((h_fusion_in_dim + 7) // 8) * 8

        # final_fusion模块的输入通道数
        h_up_out_dim = h_fusion_in_dim // 2 # h_up_for_final_fusion的输出通道
        final_fusion_in_dim = h_up_out_dim + c2_out
        final_fusion_in_dim = ((final_fusion_in_dim + 7) // 8) * 8

        print("✅ 动态计算的解码器通道数:")
        print(f"   c5_out: {c5_out}, c4_out: {c4_out}, c3_out: {c3_out}, c2_out: {c2_out}")
        print(f"   h_fusion_in_dim: {h_fusion_in_dim}")
        print(f"   final_fusion_in_dim: {final_fusion_in_dim}")
        
        # --- 使用动态通道数初始化所有模块 ---
        
        # 1. LightLCFI模块 (输入通道已是动态)
        self.h5_conv = LightLCFI(in_channels_list[3], dr1=1, dr2=3, dr3=5, dr4=7)
        self.h4_conv = LightLCFI(in_channels_list[2], dr1=1, dr2=2, dr3=4, dr4=6)
        self.h3_conv = LightLCFI(in_channels_list[1], dr1=1, dr2=2, dr3=3, dr4=5)
        self.l2_conv = LightLCFI(in_channels_list[0], dr1=1, dr2=2, dr3=3, dr4=4)

        # 2. H-Fusion
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        self.h_fusion = SCSA(
            dim=h_fusion_in_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.h_fusion_conv = nn.Sequential(
            nn.Conv2d(h_fusion_in_dim, h_fusion_in_dim, 3, 1, 1, groups=h_fusion_in_dim),
            nn.Conv2d(h_fusion_in_dim, h_fusion_in_dim, 1, 1, 0),
            nn.BatchNorm2d(h_fusion_in_dim), nn.ReLU())

        # 3. L-Fusion
        self.l_fusion_conv = nn.Sequential(
            nn.Conv2d(c2_out, c2_out, 3, 1, 1, groups=c2_out),
            nn.Conv2d(c2_out, c2_out, 1, 1, 0),
            nn.BatchNorm2d(c2_out), nn.ReLU())
        self.h2l = nn.ConvTranspose2d(h_fusion_in_dim, 1, 8, 4, 2)

        # 4. Edge Enhancer
        self.edge_enhancer = LightEdgeEnhancer(h_fusion_in_dim)

        # 5. Final Fusion
        self.h_up_for_final_fusion = nn.ConvTranspose2d(h_fusion_in_dim, h_up_out_dim, 8, 4, 2)
        
        self.final_fusion = SCSA(
            dim=final_fusion_in_dim,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.final_fusion_conv = nn.Sequential(
            nn.Conv2d(final_fusion_in_dim, final_fusion_in_dim, 3, 1, 1, groups=final_fusion_in_dim),
            nn.Conv2d(final_fusion_in_dim, final_fusion_in_dim, 1, 1, 0),
            nn.BatchNorm2d(final_fusion_in_dim), nn.ReLU())

        # 6. 预测头 (Prediction Heads)
        self.main_branch = nn.Conv2d(final_fusion_in_dim, 1, 1)
        self.h_predict = nn.Conv2d(h_fusion_in_dim, 1, 3, 1, 1)
        self.l_predict = nn.Conv2d(c2_out, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(final_fusion_in_dim, 1, 3, 1, 1)

        # 🌟 核心改进：使用新的TrainableCRF替代SimplifiedDiffCRF
        print("🔧 使用新的TrainableCRF替代SimplifiedDiffCRF")
        self.crf = TrainableCRF(
            n_iter=crf_iter,
            initial_bilateral_weight=crf_initial_bilateral_weight,
            initial_gaussian_weight=crf_initial_gaussian_weight,
            initial_bilateral_spatial_sigma=crf_initial_bilateral_spatial_sigma,
            initial_bilateral_color_sigma=crf_initial_bilateral_color_sigma,
            initial_gaussian_sigma=crf_initial_gaussian_sigma
        )

        # Class balancing weights for handling imbalanced data
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)

        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True

    def forward(self, x):
        # x: [batch_size, channel=3, h, w]
        input_size = x.shape[2:]
        
        # 特征提取
        c2, c3, c4, c5 = self.feature_extractor(x)
        
        # 使用LightLCFI处理各层特征
        h5_conv = self.h5_conv(c5)
        h4_conv = self.h4_conv(c4)
        h3_conv = self.h3_conv(c3)
        l2_conv = self.l2_conv(c2)

        # h fusion
        h5_up = self.h5_up(h5_conv)
        h3_down = self.h3_down(h3_conv)
        h_fusion_input = torch.cat((h5_up, h4_conv, h3_down), 1)
        h_fusion = self.h_fusion(h_fusion_input)
        h_fusion = self.h_fusion_conv(h_fusion)
        h_fusion = self.edge_enhancer(h_fusion)

        # l fusion
        l_fusion = self.l_fusion_conv(l2_conv)
        h2l = self.h2l(h_fusion)
        l_fusion = torch.sigmoid(h2l) * l_fusion

        # final fusion
        h_up_for_final_fusion = self.h_up_for_final_fusion(h_fusion)
        final_fusion_input = torch.cat((h_up_for_final_fusion, l_fusion), 1)
        final_fusion = self.final_fusion(final_fusion_input)
        final_fusion = self.final_fusion_conv(final_fusion)

        # --- 统一预测 ---
        # 主预测
        main_logits = self.main_branch(final_fusion)

        # 用于深度监督的旧预测
        h_logits = self.h_predict(h_fusion)
        l_logits = self.l_predict(l_fusion)
        final_logits_sup = self.final_predict(final_fusion) # final_predict for supervision

        # --- 上采样所有logits ---
        main_logits = F.interpolate(main_logits, size=input_size, mode='bilinear', align_corners=True)
        h_logits = F.interpolate(h_logits, size=input_size, mode='bilinear', align_corners=True)
        l_logits = F.interpolate(l_logits, size=input_size, mode='bilinear', align_corners=True)
        final_logits_sup = F.interpolate(final_logits_sup, size=input_size, mode='bilinear', align_corners=True)

        # --- 计算所有概率图 ---
        main_pred = torch.sigmoid(main_logits)
        h_pred_sup = torch.sigmoid(h_logits)
        l_pred_sup = torch.sigmoid(l_logits)
        final_pred_sup = torch.sigmoid(final_logits_sup)

        # 🌟 核心改进：使用新的TrainableCRF进行精炼
        # 创建2通道logits用于CRF（背景和前景）
        bg_logits = -main_logits  # 背景logits
        fg_logits = main_logits    # 前景logits
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # 标准化原始图像 (CRF需要[0,1]范围)
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # 🌟 调用新的TrainableCRF - 真正的端到端训练
        try:
            refined_logits = self.crf(combined_logits, normalized_img)
            # 从精炼的logits中提取前景概率
            refined_probs = F.softmax(refined_logits, dim=1)
            refined_pred = refined_probs[:, 1:2, :, :]  # 取前景通道
        except Exception as e:
            print(f"⚠️ CRF精炼失败: {e}，使用原始预测")
            refined_pred = main_pred

        # --- 统一返回字典 ---
        return {
            # 主要预测 (用于最终评估和主损失)
            'main_pred': main_pred,
            'refined_pred': refined_pred,
            'main_logits': main_logits,
            'refined_logits': refined_logits if 'refined_logits' in locals() else combined_logits,
            
            # 深度监督预测 (用于可选的监督损失)
            'sup_h_pred': h_pred_sup,
            'sup_l_pred': l_pred_sup,
            'sup_final_pred': final_pred_sup,
        }
    
    


# 5. 创建训练脚本的便捷函数
def create_rtglassnetv3_model(backbone_type='inceptionnext_base', 
                              crf_iter=5,
                              crf_initial_bilateral_weight=10.0,
                              crf_initial_gaussian_weight=3.0,
                              crf_initial_bilateral_spatial_sigma=40.0,
                              crf_initial_bilateral_color_sigma=5.0,
                              crf_initial_gaussian_sigma=3.0,
                              drop_path_rate=0.1):
    """
    创建RTGlassNetv3模型的便捷函数
    
    Args:
        backbone_type: 骨干网络类型
        crf_iter: CRF迭代次数
        crf_initial_*: CRF初始参数
        drop_path_rate: Dropout率
        
    Returns:
        model: RTGlassNetv3模型实例
    """
    model = RTGlassNetv3(
        backbone_type=backbone_type,
        crf_iter=crf_iter,
        crf_initial_bilateral_weight=crf_initial_bilateral_weight,
        crf_initial_gaussian_weight=crf_initial_gaussian_weight,
        crf_initial_bilateral_spatial_sigma=crf_initial_bilateral_spatial_sigma,
        crf_initial_bilateral_color_sigma=crf_initial_bilateral_color_sigma,
        crf_initial_gaussian_sigma=crf_initial_gaussian_sigma,
        drop_path_rate=drop_path_rate
    )
    
    print(f"🎉 RTGlassNetv3创建成功！")
    print(f"   Backbone: {backbone_type}")
    print(f"   CRF迭代次数: {crf_iter}")
    print(f"   总参数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"   CRF参数量: {sum(p.numel() for p in model.crf.parameters()):,}")
    
    return model


if __name__ == "__main__":
    # 简单测试模型创建
    print("=== RTGlassNetv3 模型测试 ===")
    model = create_rtglassnetv3_model(backbone_type='inceptionnext_tiny', crf_iter=3)
    print("✅ RTGlassNetv3模型创建成功！") 