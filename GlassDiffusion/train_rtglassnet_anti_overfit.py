#!/usr/bin/env python3
"""
🎯 RTGlassNet 抗过拟合训练脚本
专门解决训练集99% IoU但测试集效果差的问题
核心策略：强正则化 + 数据增强 + 早停 + 模型简化
"""

import os
import warnings
import numpy as np
import random

# 抑制警告
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import math
import gc

def set_seed(seed=42):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"🎲 随机种子设置为: {seed}")

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet 抗过拟合训练')
    
    # 🔧 抗过拟合参数
    parser.add_argument('--epochs', default=300, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='🔧 大批次：16，减少方差')
    parser.add_argument('--lr', default=0.0002, type=float, help='🔧 低学习率：0.0002')
    parser.add_argument('--min_lr', default=0.00001, type=float, help='最小学习率')
    parser.add_argument('--warmup_epochs', default=15, type=int, help='🔧 长预热：15轮')
    parser.add_argument('--wd', default=0.001, type=float, help='🔧 强权重衰减：0.001')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--n_worker', default=4, type=int, help='工作进程数')
    
    # 训练控制
    parser.add_argument('--test_interval', default=5, type=int, help='🔧 频繁验证：每2轮')
    parser.add_argument('--save_interval', default=10, type=int, help='保存间隔')
    parser.add_argument('--log_interval', default=50, type=int, help='日志间隔')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='保存路径')
    parser.add_argument('--use_gpu', default=True, type=bool, help='使用GPU')
    
    # 🎯 抗过拟合损失权重
    parser.add_argument('--focal_weight', default=0.4, type=float, help='🔧 平衡：Focal权重0.4')
    parser.add_argument('--iou_weight', default=0.4, type=float, help='🔧 平衡：IoU权重0.4（不过度优化）')
    parser.add_argument('--dt_weight', default=0.2, type=float, help='🔧 增强：DT权重0.2（边界信息）')
    parser.add_argument('--use_focal', default=True, type=bool, help='使用Focal损失')
    
    # 模型简化
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='🔧 Base for best')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='权重路径')
    parser.add_argument('--use_crf', type=bool, default=True, help='🔧 是否使用CRF（默认True启用）')
    
    # 正则化
    parser.add_argument('--dropout_rate', default=0.3, type=float, help='🔧 强Dropout：0.3')
    parser.add_argument('--label_smoothing', default=0.1, type=float, help='🔧 标签平滑：0.1')
    parser.add_argument('--mixup_alpha', default=0.2, type=float, help='🔧 Mixup增强：0.2')
    
    return parser.parse_args()

class AntiOverfitEngine:
    def __init__(self, args):
        set_seed(42)
        
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.min_lr = args.min_lr
        self.warmup_epochs = args.warmup_epochs
        self.wd = args.wd
        self.img_size = args.img_size
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.log_interval = args.log_interval
        self.model_path = args.base_save_path + '/RTGlassNet_AntiOverfit'
        self.use_gpu = args.use_gpu
        self.use_focal = args.use_focal
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        # 🔧 简化的CRF控制逻辑：直接使用布尔值
        self.use_crf = args.use_crf
        
        # 抗过拟合损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        # 正则化参数
        self.dropout_rate = args.dropout_rate
        self.label_smoothing = args.label_smoothing
        self.mixup_alpha = args.mixup_alpha
        
        # 创建目录
        os.makedirs(os.path.join(self.model_path, 'weights'), exist_ok=True)
        self.device = torch.device('cuda' if torch.cuda.is_available() and self.use_gpu else 'cpu')
        
        # 创建模型
        self.model = self.create_model()
        self._load_backbone_weight()
        
        # 抗过拟合损失函数
        self.criterion = self.create_anti_overfit_loss()
        
        # 优化器和调度器
        self.optimizer = self.create_optimizer()
        self.scheduler = self.create_scheduler()
        
        # TensorBoard
        self.writer_train = SummaryWriter(log_dir='runs/rtglassnet_antioverfit_train')
        self.writer_val = SummaryWriter(log_dir='runs/rtglassnet_antioverfit_val')
        
        print(f"🎯 RTGlassNet 抗过拟合训练配置:")
        print(f"   🔧 模型简化: {self.backbone_type} (启用CRF: {self.use_crf})")
        print(f"   🔧 强正则化: Dropout({self.dropout_rate}) + WD({self.wd}) + 标签平滑({self.label_smoothing})")
        print(f"   🔧 数据增强: Mixup({self.mixup_alpha}) + 强增强")
        print(f"   🔧 训练策略: 大批次({self.bs}) + 低学习率({self.lr}) + 长预热({self.warmup_epochs})")
        print(f"   🔧 损失权重: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        
        # 打印模型参数
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"   📊 模型参数: {total_params/1e6:.1f}M总参数, {trainable_params/1e6:.1f}M可训练")

    def create_model(self):
        """创建简化模型"""
        model = RTGlassNet(backbone_type=self.backbone_type)
        
        # 🔧 添加Dropout正则化
        if hasattr(model, 'main_conv'):
            # 在main_conv中添加Dropout
            new_main_conv = nn.Sequential(
                model.main_conv[0],  # Conv2d
                model.main_conv[1],  # BatchNorm2d
                model.main_conv[2],  # LeakyReLU
                nn.Dropout2d(self.dropout_rate)  # 🔧 添加Dropout
            )
            model.main_conv = new_main_conv
        
        # 🔧 在pred_head中添加Dropout
        if hasattr(model, 'pred_head') and hasattr(model.pred_head, 'shared_conv'):
            new_shared_conv = nn.Sequential(
                model.pred_head.shared_conv[0],  # Conv2d
                model.pred_head.shared_conv[1],  # BatchNorm2d
                model.pred_head.shared_conv[2],  # LeakyReLU
                nn.Dropout2d(self.dropout_rate),  # 🔧 添加Dropout
                model.pred_head.shared_conv[3],  # Conv2d
                model.pred_head.shared_conv[4],  # BatchNorm2d
                model.pred_head.shared_conv[5],  # LeakyReLU
                nn.Dropout2d(self.dropout_rate)   # 🔧 添加Dropout
            )
            model.pred_head.shared_conv = new_shared_conv
        
        # 🔧 CRF控制：默认启用，除非明确禁用
        if not self.use_crf:
            print("🔧 禁用CRF以减少模型复杂度")
            model.diff_crf = None
        else:
            print("🔧 启用CRF，参数可学习")
            # 确保CRF参数可训练
            if hasattr(model, 'diff_crf') and model.diff_crf is not None:
                for param in model.diff_crf.parameters():
                    param.requires_grad = True
                print(f"   CRF参数: bilateral_weight={model.diff_crf.bilateral_weight}, gaussian_weight={model.diff_crf.gaussian_weight}")
                print(f"   CRF参数: bilateral_spatial_sigma={model.diff_crf.bilateral_spatial_sigma}, bilateral_color_sigma={model.diff_crf.bilateral_color_sigma}")
                print(f"   CRF参数: gaussian_sigma={model.diff_crf.gaussian_sigma}, n_iter={model.diff_crf.n_iter}")
        
        return model.to(self.device)

    def create_anti_overfit_loss(self):
        """创建抗过拟合损失函数"""
        return CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=self.use_focal
        )

    def create_optimizer(self):
        """创建优化器"""
        # 🔧 使用AdamW + 强权重衰减
        return optim.AdamW(
            self.model.parameters(),
            lr=self.lr,
            weight_decay=self.wd,
            betas=(0.9, 0.999),
            eps=1e-8
        )

    def create_scheduler(self):
        """创建学习率调度器"""
        total_steps = self.epochs * 1000  # 估算总步数
        warmup_steps = self.warmup_epochs * 1000
        
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            else:
                # 余弦退火
                return 0.5 * (1 + math.cos(math.pi * (step - warmup_steps) / (total_steps - warmup_steps)))
        
        return torch.optim.lr_scheduler.LambdaLR(self.optimizer, lr_lambda)

    def _load_backbone_weight(self):
        """加载预训练权重"""
        if self.backbone_weight and os.path.exists(self.backbone_weight):
            print(f"加载权重: {self.backbone_weight}")
            state_dict = torch.load(self.backbone_weight, map_location=self.device)
            model_backbone = self.model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"已加载 {len(filtered_dict)} 个参数")
        else:
            print(f"未找到权重文件: {self.backbone_weight}")

    def mixup_data(self, x, y, alpha=0.2):
        """Mixup数据增强"""
        if alpha > 0:
            lam = np.random.beta(alpha, alpha)
        else:
            lam = 1

        batch_size = x.size()[0]
        index = torch.randperm(batch_size).to(x.device)

        mixed_x = lam * x + (1 - lam) * x[index, :]
        mixed_y = lam * y + (1 - lam) * y[index, :]
        
        return mixed_x, mixed_y, lam, index

    def train(self):
        """训练函数"""
        # 🔧 使用强数据增强
        train_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/',
            split='train',
            target_size=(self.img_size, self.img_size),
            split_ratio=0.8,
            random_seed=42,
            glass_aug_config='heavy'  # 🔧 使用强增强
        )
        
        val_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/',
            split='valid',
            target_size=(self.img_size, self.img_size),
            split_ratio=0.8,
            random_seed=42,
            glass_aug_config=None
        )
        
        train_loader = DataLoader(train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker, pin_memory=True)
        val_loader = DataLoader(val_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker, pin_memory=True)
        
        best_iou = 0.0
        patience = 20  # 🔧 增加早停耐心
        patience_counter = 0
        global_step = 0
        
        print(f"\n🎯 开始抗过拟合训练")
        print(f"   训练样本: {len(train_data)}, 验证样本: {len(val_data)}")
        print(f"   批次数/轮: {len(train_loader)}")
        
        for epoch in range(1, self.epochs + 1):
            self.model.train()
            running_loss = 0.0
            running_train_iou = 0.0
            
            train_tqdm = tqdm(train_loader, desc=f"Epoch {epoch}")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                # 🔧 Mixup数据增强
                if self.mixup_alpha > 0 and np.random.random() < 0.5:
                    inp_imgs, gt_masks, lam, index = self.mixup_data(inp_imgs, gt_masks, self.mixup_alpha)
                    dt_maps = lam * dt_maps + (1 - lam) * dt_maps[index]
                
                self.optimizer.zero_grad()
                outputs = self.model(inp_imgs)
                
                # 🔧 标签平滑
                if self.label_smoothing > 0:
                    gt_masks = gt_masks * (1 - self.label_smoothing) + 0.5 * self.label_smoothing
                
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                # 数值稳定性检查
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失: {loss.item()}, 跳过此批次")
                    continue
                
                loss.backward()
                
                # 🔧 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                self.scheduler.step()
                
                running_loss += loss.item()
                global_step += 1
                
                # 计算训练IoU（用于监控过拟合）
                with torch.no_grad():
                    main_pred = outputs['main_pred']
                    pred_bin = (main_pred > 0.5).float()
                    intersection = (pred_bin * gt_masks).sum(dim=(1,2,3))
                    union = pred_bin.sum(dim=(1,2,3)) + gt_masks.sum(dim=(1,2,3)) - intersection
                    train_iou = (intersection / (union + 1e-7)).mean().item()
                    running_train_iou += train_iou
                
                # 显示进度
                current_lr = self.optimizer.param_groups[0]['lr']
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}",
                    train_iou=f"{train_iou:.4f}",
                    lr=f"{current_lr:.2e}"
                )
                
                # 记录到TensorBoard
                if batch_idx % self.log_interval == 0:
                    self.writer_train.add_scalar('loss/total', loss.item(), global_step)
                    self.writer_train.add_scalar('loss/focal', loss_dict['focal_loss'], global_step)
                    self.writer_train.add_scalar('loss/iou', loss_dict['iou_loss'], global_step)
                    self.writer_train.add_scalar('loss/dt', loss_dict['dt_loss'], global_step)
                    self.writer_train.add_scalar('iou/train', train_iou, global_step)
                    self.writer_train.add_scalar('lr', current_lr, global_step)
            
            epoch_loss = running_loss / len(train_loader)
            epoch_train_iou = running_train_iou / len(train_loader)
            
            print(f"Epoch {epoch}: Loss={epoch_loss:.6f}, Train_IoU={epoch_train_iou:.4f}")
            
            # 🔧 频繁验证
            if epoch % self.test_interval == 0:
                val_loss, val_iou = self.validate(val_loader)
                
                # 过拟合检测
                overfit_gap = epoch_train_iou - val_iou
                print(f"验证: Loss={val_loss:.6f}, IoU={val_iou:.4f}, 过拟合差距={overfit_gap:.4f}")
                
                # 记录到TensorBoard
                self.writer_val.add_scalar('loss/val', val_loss, epoch)
                self.writer_val.add_scalar('iou/val', val_iou, epoch)
                self.writer_val.add_scalar('overfit_gap', overfit_gap, epoch)
                
                # 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    patience_counter = 0
                    self.save_model(epoch, is_best=True, val_iou=val_iou, train_iou=epoch_train_iou)
                else:
                    patience_counter += 1
                
                # 早停检查
                if patience_counter >= patience:
                    print(f"🛑 早停触发! {patience}轮无改善")
                    break
            
            # 定期保存
            if epoch % self.save_interval == 0:
                self.save_model(epoch, is_best=False, val_iou=best_iou, train_iou=epoch_train_iou)
        
        self.writer_train.close()
        self.writer_val.close()
        print(f"🎯 训练完成! 最佳验证IoU: {best_iou:.4f}")

    def validate(self, val_loader):
        """验证函数"""
        self.model.eval()
        running_loss = 0.0
        total_intersection = 0.0
        total_union = 0.0
        
        with torch.no_grad():
            for inp_imgs, gt_masks, dt_maps in val_loader:
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                outputs = self.model(inp_imgs)
                loss, _ = self.criterion(outputs, gt_masks, dt_maps)
                
                running_loss += loss.item()
                
                # 计算IoU
                main_pred = outputs['main_pred']
                pred_bin = (main_pred > 0.5).float()
                intersection = (pred_bin * gt_masks).sum()
                union = pred_bin.sum() + gt_masks.sum() - intersection
                total_intersection += intersection.item()
                total_union += union.item()
        
        val_loss = running_loss / len(val_loader)
        val_iou = total_intersection / (total_union + 1e-7)
        
        return val_loss, val_iou

    def save_model(self, epoch, is_best=False, val_iou=0.0, train_iou=0.0):
        """保存模型"""
        state = {
            'model': self.model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'scheduler': self.scheduler.state_dict(),
            'epoch': epoch,
            'val_iou': val_iou,
            'train_iou': train_iou,
            'config': {
                'backbone_type': self.backbone_type,
                'lr': self.lr,
                'wd': self.wd,
                'dropout_rate': self.dropout_rate,
                'label_smoothing': self.label_smoothing,
                'mixup_alpha': self.mixup_alpha,
                'use_crf': self.use_crf,  # 修正: 使用正确的属性名
                'focal_weight': self.focal_weight,
                'iou_weight': self.iou_weight,
                'dt_weight': self.dt_weight
            }
        }
        
        if is_best:
            filename = f'BEST_epoch{epoch:03d}_val{val_iou:.4f}_train{train_iou:.4f}.pth'
            print(f"🏆 保存最佳模型: {filename}")
        else:
            filename = f'epoch{epoch:03d}_val{val_iou:.4f}_train{train_iou:.4f}.pth'
        
        save_path = os.path.join(self.model_path, 'weights', filename)
        torch.save(state, save_path)

def main():
    args = parse_arguments()
    engine = AntiOverfitEngine(args)
    engine.train()

if __name__ == '__main__':
    main() 