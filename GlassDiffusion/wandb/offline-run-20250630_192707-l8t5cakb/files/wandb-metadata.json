{"os": "Linux-5.15.0-139-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.18", "startedAt": "2025-06-30T11:27:08.199227Z", "program": "train_stage_1.py", "codePath": "ProteusGlassDiffusion/train_stage_1.py", "git": {"remote": "https://github.com/Glennine/IG_SLAM.git", "commit": "a806b79e329d046ae2e0643b4fe8dc96256b0ab5"}, "root": "/home/<USER>/ws/IG_SLAM/ProteusGlassDiffusion", "host": "g-pc", "executable": "/home/<USER>/anaconda3/envs/drl/bin/python", "codePathLocal": "train_stage_1.py", "cpu_count": 16, "cpu_count_logical": 24, "gpu": "NVIDIA GeForce RTX 4080", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33447784448"}, "cpu": {"count": 16, "countLogical": 24}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4080", "memoryTotal": "17171480576", "cudaCores": 9728, "architecture": "Ada", "uuid": "GPU-de073ea2-cb43-c6bd-0ddc-286385db1219"}], "cudaVersion": "12.2"}