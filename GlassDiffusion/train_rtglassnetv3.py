#!/usr/bin/env python3
"""
RTGlassNetv3 训练脚本
- 支持K-Fold、早停、TensorBoard、差分学习率
- 支持drop_path_rate参数防止过拟合
- 默认InceptionNeXt-Base骨干，支持DT监督
"""
import os
import argparse
import numpy as np
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from models.RTGlassNetv3 import RTGlassNetv3
from models.loss_rtglassnetv3 import RTGlassNetv3Loss
from glass_dataloader import GlassDataLoader
from sklearn.model_selection import KFold
import random
import gc
from torchvision import transforms

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    print(f"🎲 随机种子设置为: {seed}")

def clear_cuda_cache():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def parse_arguments():
    parser = argparse.ArgumentParser(description='RTGlassNetv3训练参数')
    parser.add_argument('--epochs', default=250, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='批次大小')
    parser.add_argument('--backbone_lr', default=5e-5, type=float, help='backbone学习率 (稳定版)')
    parser.add_argument('--decoder_lr', default=5e-4, type=float, help='decoder学习率 (稳定版)')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--n_worker', default=4, type=int, help='工作进程数')
    parser.add_argument('--warmup_epochs', default=10, type=int, help='warmup轮数')
    parser.add_argument('--k_folds', default=5, type=int, help='K-Fold交叉验证的K值')
    parser.add_argument('--current_fold', default=None, type=int, help='当前训练的fold')
    parser.add_argument('--focal_weight', default=0.5, type=float, help='Focal损失权重')
    parser.add_argument('--iou_weight', default=0.4, type=float, help='IoU损失权重')
    parser.add_argument('--dt_weight', default=0.1, type=float, help='DT损失权重')
    parser.add_argument('--edge_weight', default=0.1, type=float, help='边缘损失权重')
    parser.add_argument('--val_freq', default=5, type=int, help='验证频率(轮数)')
    parser.add_argument('--patience', default=15, type=int, help='早停耐心值')
    parser.add_argument('--grad_clip', default=0.5, type=float, help='梯度裁剪阈值')
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--aug_config', default='moderate', type=str, help='数据增强配置')
    parser.add_argument('--drop_path_rate', default=0.1, type=float, help='drop_path_rate防止过拟合 (稳定版)')
    parser.add_argument('--weight_decay', default=5e-4, type=float, help='AdamW权重衰减(L2正则化)')
    parser.add_argument('--no_dt', action='store_true', help='禁用距离变换(DT)损失')
    parser.add_argument('--full_train', action='store_true', help='训练整个训练集，无验证集，每20轮保存')
    return parser.parse_args()

def get_history_best_iou(weights_dir):
    best = 0.0
    if os.path.exists(weights_dir):
        for f in os.listdir(weights_dir):
            if f.startswith('BEST_') and f.endswith('.pth'):
                try:
                    iou = float(f.split('_iou')[1].replace('.pth', ''))
                    if iou > best:
                        best = iou
                except Exception:
                    continue
    return best

class KFoldEngineV3:
    def __init__(self, args):
        set_seed(42)
        clear_cuda_cache()
        self.epochs = args.epochs
        self.bs = args.bs
        self.backbone_lr = args.backbone_lr
        self.decoder_lr = args.decoder_lr
        self.img_size = args.img_size
        self.n_worker = args.n_worker
        self.k_folds = args.k_folds
        self.current_fold = args.current_fold
        self.warmup_epochs = args.warmup_epochs
        self.val_freq = args.val_freq
        self.patience = args.patience
        self.grad_clip = args.grad_clip
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = 0.0 if args.no_dt else args.dt_weight
        self.edge_weight = args.edge_weight
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        self.model_path = args.base_save_path + '/RTGlassNetv3'
        self.aug_config = args.aug_config
        self.drop_path_rate = args.drop_path_rate
        self.weight_decay = args.weight_decay
        self.full_train = args.full_train  # 新增：全训练模式
        os.makedirs(os.path.join(self.model_path, 'weights'), exist_ok=True)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.criterion = RTGlassNetv3Loss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight,
            edge_weight=self.edge_weight,
            dt_weight=self.dt_weight
        )
        print(f"🎯 RTGlassNetv3训练配置:")
        if self.full_train:
            print(f"   🚀 全训练模式: 训练整个训练集，无验证集，每20轮保存")
        else:
            print(f"   K-Fold: {self.k_folds}折, 骨干: {self.backbone_type}, drop_path_rate: {self.drop_path_rate}")
        print(f"   损失权重: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight}) + Edge({self.edge_weight})")
        if args.no_dt:
            print("   ⚠️ DT损失已禁用")

    def create_model(self):
        """创建模型 - 每一折都从相同的预训练状态开始"""
        print(f"\n🔧 创建新的RTGlassNetv3模型实例...")
        
        # 创建全新的模型实例，timm会自动加载预训练权重
        model = RTGlassNetv3(
            backbone_type=self.backbone_type,
            drop_path_rate=self.drop_path_rate
        ).to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 模型总参数量: {total_params/1e6:.1f}M")
        
        return model

    def train_fold(self, fold_idx, train_indices, val_indices):
        """训练单个fold - 支持K-Fold和单次训练"""
        print(f"\n🎯 训练 Fold {fold_idx + 1}/{self.k_folds if self.k_folds > 0 else 1}")
        
        clear_cuda_cache()
        
        if self.k_folds == 0:
            # 单次训练模式 - 使用8:2分割
            print(f"📊 单次训练模式 (8:2分割)")
            train_dataset = GlassDataLoader(
                data_dir='/home/<USER>/ws/IG_SLAM/',
                split='train',
                target_size=(self.img_size, self.img_size),
                aug_config=self.aug_config
            )
            
            val_dataset = GlassDataLoader(
                data_dir='/home/<USER>/ws/IG_SLAM/',
                split='valid',
                target_size=(self.img_size, self.img_size),
                aug_config='none'
            )
        else:
            # K-Fold模式 - 使用现有的GlassCrossValidationLoader
            print(f"📊 K-Fold模式 (Fold {fold_idx + 1})")
            
            from glass_dataloader import GlassCrossValidationLoader
            
            # 创建训练数据集
            train_dataset = GlassCrossValidationLoader(
                mode='train',
                fold=fold_idx,
                augment_data=True,
                target_size=self.img_size,
                glass_augmentation=self.aug_config,
                n_folds=self.k_folds
            )
            
            # 创建验证数据集
            val_dataset = GlassCrossValidationLoader(
                mode='test',  # 验证集使用test模式
                fold=fold_idx,
                augment_data=False,
                target_size=self.img_size,
                glass_augmentation='none',
                n_folds=self.k_folds
            )

        print(f"   训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.bs,
            shuffle=True,
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True
        )
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.bs,
            shuffle=False,
            num_workers=self.n_worker,
            pin_memory=True
        )

        model = self.create_model()
        backbone_params = []
        decoder_params = []
        for name, param in model.named_parameters():
            if 'feature_extractor.backbone' in name:
                backbone_params.append(param)
            else:
                decoder_params.append(param)
        optimizer = optim.AdamW([
            {'params': backbone_params, 'lr': self.backbone_lr, 'weight_decay': self.weight_decay},
            {'params': decoder_params, 'lr': self.decoder_lr, 'weight_decay': self.weight_decay}
        ])
        total_steps = len(train_loader) * self.epochs
        warmup_steps = len(train_loader) * self.warmup_epochs
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            else:
                return 0.5 * (1 + np.cos(np.pi * (step - warmup_steps) / (total_steps - warmup_steps)))
        scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
        writer = SummaryWriter(log_dir=f'runs/v3_fold{fold_idx+1}')
        weights_dir = os.path.join(self.model_path, 'weights')
        history_best_iou = get_history_best_iou(weights_dir)
        best_iou = history_best_iou
        print(f'历史最佳IoU: {best_iou:.4f}')
        iou_history = []
        patience_counter = 0
        global_step = 0
        for epoch in range(1, self.epochs + 1):
            model.train()
            running_loss = 0.0
            train_tqdm = tqdm(train_loader, desc=f"Fold{fold_idx+1} Epoch{epoch}")
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失: {loss.item()}, 跳过此批次")
                    continue
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=self.grad_clip)
                optimizer.step()
                scheduler.step()
                
                running_loss += loss.item()
                global_step += 1
                
                # 显示当前学习率
                current_lr_backbone = optimizer.param_groups[0]['lr']
                current_lr_decoder = optimizer.param_groups[1]['lr']
                
                # 计算当前平均损失
                current_avg_loss = running_loss / (batch_idx + 1)
                
                # 🔧 修复：显示详细的训练指标
                train_tqdm.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'avg_loss': f"{current_avg_loss:.4f}",
                    'refined': f"{loss_dict['loss_refined']:.4f}",
                    'main': f"{loss_dict['loss_main']:.4f}",
                    'sup_final': f"{loss_dict['loss_sup_final']:.4f}",
                    'dt': f"{loss_dict['loss_dt']:.4f}",
                    'lr_b': f"{current_lr_backbone:.2e}",
                    'lr_d': f"{current_lr_decoder:.2e}"
                })
            epoch_loss = running_loss / len(train_loader)
            print(f"Fold{fold_idx+1} Epoch{epoch} Avg Loss: {epoch_loss:.6f}")
            writer.add_scalar('Loss/Train', epoch_loss, epoch)
            if epoch % self.val_freq == 0:
                val_loss, val_iou = self.validate(model, val_loader)
                iou_history.append(val_iou)
                print(f"Fold{fold_idx+1} Epoch{epoch} Val Loss: {val_loss:.6f}, Val IoU: {val_iou:.4f}")
                writer.add_scalar('Loss/Val', val_loss, epoch)
                writer.add_scalar('IoU/Val', val_iou, epoch)
                writer.add_scalar('IoU/Best', best_iou, epoch)
                writer.add_scalar('LR/Backbone', current_lr_backbone, epoch)
                writer.add_scalar('LR/Decoder', current_lr_decoder, epoch)
                
                if len(iou_history) >= 3:
                    recent_std = np.std(iou_history[-3:])
                    print(f"   最近3轮IoU标准差: {recent_std:.4f}")
                if val_iou > best_iou:
                    best_iou = val_iou
                    patience_counter = 0
                    self.save_model(fold_idx, epoch, model, optimizer, scheduler, val_iou, is_best=True)
                else:
                    patience_counter += 1
                if patience_counter >= self.patience:
                    print(f"🛑 早停触发! {self.patience}轮无改善")
                    break

        writer.close()
        print(f"🎯 Fold {fold_idx + 1} 完成! 最佳IoU: {best_iou:.4f}")
        del model, optimizer, scheduler
        clear_cuda_cache()
        return best_iou

    def validate(self, model, val_loader):
        """验证函数 - 使用refined_pred（CRF后处理）计算IoU - 修复IoU计算方法"""
        model.eval()
        running_loss = 0.0
        iou_sum = 0.0
        count = 0
        
        # 导入评估指标函数
        from ig_glass.misc import compute_iou
        
        with torch.no_grad():
            for inp_imgs, gt_masks, dt_maps in val_loader:
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                # 获取模型输出
                outputs = model(inp_imgs)
                loss, _ = self.criterion(outputs, gt_masks, dt_maps)
                running_loss += loss.item()
                
                # 使用refined_pred（CRF后处理的结果）
                refined_pred = outputs['refined_pred']
                
                # 数值检查和修正
                if torch.isnan(refined_pred).any() or torch.isinf(refined_pred).any():
                    print("⚠️ 检测到NaN/Inf预测值，使用main_pred回退")
                    refined_pred = outputs['main_pred']
                
                # 确保预测值在[0,1]范围内
                refined_pred = torch.clamp(refined_pred, 0, 1)
                
                # 二值化预测
                pred_binary = (refined_pred > 0.5).float()
                
                # 🔧 修复：使用正确的逐图IoU计算方法（与测试脚本一致）
                batch_size = inp_imgs.size(0)
                for i in range(batch_size):
                    # 单张图像的预测和GT
                    pred_single = pred_binary[i, 0].cpu().numpy()  # [H, W]
                    gt_single = gt_masks[i, 0].cpu().numpy()       # [H, W]
                    
                    # 关键修复：确保GT也是二值化的！
                    gt_single = (gt_single > 0.5).astype(pred_single.dtype)
                    
                    # 计算单张图像的IoU
                    single_iou = compute_iou(pred_single, gt_single)
                    iou_sum += single_iou
                    count += 1
                
        val_loss = running_loss / len(val_loader)
        mean_iou = iou_sum / count if count > 0 else 0.0
        
        
        return val_loss, mean_iou

    def save_model(self, fold_idx, epoch, model, optimizer, scheduler, val_iou, is_best=False):
        """保存模型 - 只保存最佳模型，节省存储空间（学习optimized版本）"""
        #  修复：只保存最佳模型
        if not is_best:
            return  # 非最佳模型直接返回，不保存
        
        state = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'scheduler': scheduler.state_dict(),
            'epoch': epoch,
            'fold': fold_idx,
            'val_iou': val_iou,
            'config': {
                'backbone_type': self.backbone_type,
                'backbone_lr': self.backbone_lr,
                'decoder_lr': self.decoder_lr,
                'k_folds': self.k_folds,
                'focal_weight': self.focal_weight,
                'iou_weight': self.iou_weight,
                'dt_weight': self.dt_weight,
                'warmup_epochs': self.warmup_epochs,
                'grad_clip': self.grad_clip,
                'drop_path_rate': self.drop_path_rate,
                'weight_decay': self.weight_decay # 新增
            }
        }
        
        if self.k_folds == 0:
            # 非K-Fold模式下的命名 - 只保存最佳模型
                filename = f'BEST_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
                print(f"🏆 保存最佳模型: {filename}")
        else:
            # K-Fold模式下的命名 - 只保存最佳模型
                filename = f'BEST_fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
                print(f"🏆 保存最佳模型: {filename}")
        
        save_path = os.path.join(self.model_path, 'weights', filename)
        torch.save(state, save_path)
        
        # 显示保存的文件大小
        file_size_mb = os.path.getsize(save_path) / (1024 * 1024)
        print(f"💾 文件大小: {file_size_mb:.1f} MB")
        print(f"📂 保存路径: {save_path}")

    def train_kfold(self):
        """执行K-Fold交叉验证或全数据训练"""
        if self.full_train:
            # 全训练模式
            print(f"\n🚀 启动全训练模式...")
            best_loss = self.train_full()
            print(f"\n🎯 全训练完成! 最佳损失: {best_loss:.6f}")
            return
            
        if self.k_folds == 0:
            print(f"\n🎯 使用全部数据进行训练 (8:2拆分)...")
            # 单次训练模式
            self.train_fold(0, None, None)
            
        else:
            print(f"\n🎯 执行{self.k_folds}折交叉验证...")
            # K-Fold模式 - 使用现有的GlassCrossValidationLoader
            fold_results = []
            
            for fold_idx in range(self.k_folds):
                print(f"\n{'='*50}")
                print(f"🎯 开始训练 Fold {fold_idx + 1}/{self.k_folds}")
                print(f"{'='*50}")
                
                # 训练当前fold
                best_iou = self.train_fold(fold_idx, None, None)
                fold_results.append(best_iou)
                
                print(f"✅ Fold {fold_idx + 1} 完成，最佳IoU: {best_iou:.4f}")
                
                # 显示当前进度
                if len(fold_results) > 1:
                    avg_iou = np.mean(fold_results)
                    std_iou = np.std(fold_results)
                    print(f"📊 当前平均IoU: {avg_iou:.4f} ± {std_iou:.4f}")
            
            # 显示最终结果
            print(f"\n{'='*60}")
            print(f"🎉 K-Fold交叉验证完成!")
            print(f"{'='*60}")
            print(f"📊 各折结果:")
            for i, iou in enumerate(fold_results):
                print(f"   Fold {i+1}: {iou:.4f}")
            
            final_avg_iou = np.mean(fold_results)
            final_std_iou = np.std(fold_results)
            print(f"\n🏆 最终结果: {final_avg_iou:.4f} ± {final_std_iou:.4f}")
            print(f"📈 最佳单折: {max(fold_results):.4f}")
            print(f"📉 最差单折: {min(fold_results):.4f}")
            print(f"{'='*60}")

    def train_full(self):
        """全训练模式：训练整个训练集，无验证集，每20轮保存"""
        print(f"\n🚀 开始全训练模式...")
        print(f"   训练轮数: {self.epochs}")
        print(f"   批次大小: {self.bs}")
        print(f"   每20轮保存一次检查点")
        print(f"   训练结束时保存最佳模型")
        
        clear_cuda_cache()
        
        # 创建训练数据集 - 使用全部训练数据
        train_dataset = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/',
            split='train',  # 使用训练集
            target_size=(self.img_size, self.img_size),
            aug_config=self.aug_config
        )
        
        print(f"📊 训练数据集: {len(train_dataset)}张图像")
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.bs,
            shuffle=True,
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True
        )
        
        # 创建模型
        model = self.create_model()
        
        # 分离backbone和decoder参数
        backbone_params = []
        decoder_params = []
        for name, param in model.named_parameters():
            if 'feature_extractor.backbone' in name:
                backbone_params.append(param)
            else:
                decoder_params.append(param)
        
        # 创建优化器
        optimizer = optim.AdamW([
            {'params': backbone_params, 'lr': self.backbone_lr, 'weight_decay': self.weight_decay},
            {'params': decoder_params, 'lr': self.decoder_lr, 'weight_decay': self.weight_decay}
        ])
        
        # 学习率调度器
        total_steps = len(train_loader) * self.epochs
        warmup_steps = len(train_loader) * self.warmup_epochs
        
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            else:
                return 0.5 * (1 + np.cos(np.pi * (step - warmup_steps) / (total_steps - warmup_steps)))
        
        scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
        
        # TensorBoard
        writer = SummaryWriter(log_dir=f'runs/full_train_v3')
        
        # 训练状态
        best_loss = float('inf')
        best_epoch = 0
        loss_history = []
        patience_counter = 0
        
        print(f"\n🎯 开始训练...")
        
        for epoch in range(1, self.epochs + 1):
            model.train()
            running_loss = 0.0
            
            train_tqdm = tqdm(train_loader, desc=f"Epoch{epoch}")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失: {loss.item()}, 跳过此批次")
                    continue
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=self.grad_clip)
                optimizer.step()
                scheduler.step()
                
                running_loss += loss.item()
                
                # 显示当前学习率
                current_lr_backbone = optimizer.param_groups[0]['lr']
                current_lr_decoder = optimizer.param_groups[1]['lr']
                
                # 计算当前平均损失
                current_avg_loss = running_loss / (batch_idx + 1)
                
                # 显示训练指标
                train_tqdm.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'avg_loss': f"{current_avg_loss:.4f}",
                    'refined': f"{loss_dict['loss_refined']:.4f}",
                    'main': f"{loss_dict['loss_main']:.4f}",
                    'sup_final': f"{loss_dict['loss_sup_final']:.4f}",
                    'dt': f"{loss_dict['loss_dt']:.4f}",
                    'lr_b': f"{current_lr_backbone:.2e}",
                    'lr_d': f"{current_lr_decoder:.2e}"
                })
            
            epoch_loss = running_loss / len(train_loader)
            loss_history.append(epoch_loss)
            
            print(f"Epoch{epoch} Avg Loss: {epoch_loss:.6f}")
            
            # 记录到TensorBoard
            writer.add_scalar('Loss/Train', epoch_loss, epoch)
            writer.add_scalar('LR/Backbone', current_lr_backbone, epoch)
            writer.add_scalar('LR/Decoder', current_lr_decoder, epoch)
            
            # 每20轮保存一次检查点模型
            if epoch % 20 == 0:
                self.save_full_model(epoch, model, optimizer, scheduler, epoch_loss, is_checkpoint=True)
                print(f"💾 保存检查点模型 (Epoch {epoch})")
            
            # 记录最佳损失（不立即保存）
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                best_epoch = epoch
                patience_counter = 0
                print(f"🏆 新的最佳损失: {best_loss:.6f} (Epoch {epoch})")
            else:
                patience_counter += 1
                if patience_counter >= self.patience:
                    print(f"🛑 早停触发! {self.patience}轮无改善")
                    break
            
            # 显示最近几轮的损失变化
            if len(loss_history) >= 3:
                recent_std = np.std(loss_history[-3:])
                print(f"   最近3轮损失标准差: {recent_std:.6f}")
        
        # 训练结束时保存最佳模型
        if best_epoch > 0:
            print(f"\n💾 训练结束，保存最佳模型 (Epoch {best_epoch}, Loss: {best_loss:.6f})")
            self.save_full_model(best_epoch, model, optimizer, scheduler, best_loss, is_best=True)
        
        writer.close()
        print(f"\n🎯 全训练完成! 最佳损失: {best_loss:.6f} (Epoch {best_epoch})")
        return best_loss

    def save_full_model(self, epoch, model, optimizer, scheduler, loss, is_best=False, is_checkpoint=False):
        """保存全训练模式的模型"""
        state = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'scheduler': scheduler.state_dict(),
            'epoch': epoch,
            'loss': loss,
            'config': {
                'backbone_type': self.backbone_type,
                'backbone_lr': self.backbone_lr,
                'decoder_lr': self.decoder_lr,
                'focal_weight': self.focal_weight,
                'iou_weight': self.iou_weight,
                'dt_weight': self.dt_weight,
                'warmup_epochs': self.warmup_epochs,
                'grad_clip': self.grad_clip,
                'drop_path_rate': self.drop_path_rate,
                'weight_decay': self.weight_decay
            }
        }
        
        if is_best:
            filename = f'BEST_full_train_epoch{epoch:03d}_loss{loss:.6f}.pth'
            print(f"🏆 保存最佳模型: {filename}")
        elif is_checkpoint:
            filename = f'CHECKPOINT_full_train_epoch{epoch:03d}_loss{loss:.6f}.pth'
            print(f"💾 保存检查点: {filename}")
        else:
            filename = f'full_train_epoch{epoch:03d}_loss{loss:.6f}.pth'
        
        save_path = os.path.join(self.model_path, 'weights', filename)
        torch.save(state, save_path)
        
        # 显示保存的文件大小
        file_size_mb = os.path.getsize(save_path) / (1024 * 1024)
        print(f"💾 文件大小: {file_size_mb:.1f} MB")
        print(f"📂 保存路径: {save_path}")

def main():
    args = parse_arguments()
    # 根据用户输入更新超参数
    if args.decoder_lr:
        args.decoder_lr = args.decoder_lr
    if args.backbone_lr:
        args.backbone_lr = args.backbone_lr
    if args.drop_path_rate:
        args.drop_path_rate = args.drop_path_rate
        
    engine = KFoldEngineV3(args)
    engine.train_kfold()

if __name__ == '__main__':
    main()
