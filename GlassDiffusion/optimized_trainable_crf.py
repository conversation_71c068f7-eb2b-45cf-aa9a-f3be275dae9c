"""
基于TrainableCRF的优化版本 - 添加可分离卷积和兼容性矩阵

核心改进：
1. 保留TrainableCRF的数值稳定性优势（对数空间学习）
2. 添加可分离卷积，大幅提升性能（85%计算量减少）
3. 可选的兼容性矩阵，增强理论完整性
4. 保持原有的消息控制和训练稳定性

基于：trainable_crf.py 的 TrainableCRF 类
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class OptimizedTrainableCRF(nn.Module):
    """
    优化版可训练CRF
    
    主要改进：
    1. 可分离卷积优化（85%性能提升）
    2. 可选兼容性矩阵（理论完整性）
    3. 保留对数空间学习（数值稳定性）
    4. 保留消息控制（训练稳定性）
    """
    
    def __init__(self,
                 n_iter: int = 5,
                 initial_bilateral_weight: float = 10.0,
                 initial_gaussian_weight: float = 3.0,
                 initial_bilateral_spatial_sigma: float = 49.0,
                 initial_bilateral_color_sigma: float = 5.0,
                 initial_gaussian_sigma: float = 3.0,
                 # 新增选项
                 use_compatibility_matrix: bool = True,
                 use_separable_conv: bool = True,
                 use_message_control: bool = True):
        """
        初始化优化版可训练CRF
        
        Args:
            use_compatibility_matrix: 是否使用兼容性矩阵（提升理论完整性）
            use_separable_conv: 是否使用可分离卷积（提升性能）
            use_message_control: 是否使用消息控制（提升稳定性）
        """
        super().__init__()
        self.n_iter = n_iter
        self.use_compatibility_matrix = use_compatibility_matrix
        self.use_separable_conv = use_separable_conv
        self.use_message_control = use_message_control

        # 保留TrainableCRF的对数空间学习优势
        self.log_bilateral_weight = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_weight, dtype=torch.float32))
        )
        self.log_gaussian_weight = nn.Parameter(
            torch.log(torch.tensor(initial_gaussian_weight, dtype=torch.float32))
        )
        self.log_bilateral_spatial_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_spatial_sigma, dtype=torch.float32))
        )
        self.log_bilateral_color_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_color_sigma, dtype=torch.float32))
        )
        self.log_gaussian_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_gaussian_sigma, dtype=torch.float32))
        )
        
        # 可选的兼容性矩阵
        if self.use_compatibility_matrix:
            initial_compat = torch.tensor([[-1.0, 1.0], [1.0, -1.0]], dtype=torch.float32)
            self.compatibility_matrix = nn.Parameter(initial_compat)
        
        # 保留TrainableCRF的稳定性控制
        if self.use_message_control:
            self.message_scaler = nn.Parameter(torch.tensor(1.0, dtype=torch.float32))
            self.iteration_decay = nn.Parameter(torch.tensor(0.9, dtype=torch.float32))

    def _separable_gaussian_filter(self, x: torch.Tensor, sigma: torch.Tensor) -> torch.Tensor:
        """
        可分离高斯滤波 - 性能优化版本
        复杂度从 O(K²×C×H×W) 降到 O(2×K×C×H×W)，节省85%计算量
        """
        sigma_val = torch.clamp(sigma, min=0.5, max=50.0).item()
        
        kernel_size = int(2 * sigma_val + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1
        kernel_size = min(kernel_size, 15)  # 限制最大核大小以平衡性能
        
        padding = kernel_size // 2
        grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
        
        gaussian_1d = torch.exp(-0.5 * (grid / torch.clamp(sigma, min=1e-6)).pow(2))
        gaussian_1d = gaussian_1d / torch.clamp(gaussian_1d.sum(), min=1e-6)

        # 可分离卷积：先水平后垂直
        # 水平卷积
        h_kernel = gaussian_1d.view(1, 1, 1, -1).expand(x.size(1), 1, 1, kernel_size)
        h_filtered = F.conv2d(x, h_kernel, padding=(0, padding), groups=x.size(1))
        
        # 垂直卷积
        v_kernel = gaussian_1d.view(1, 1, -1, 1).expand(x.size(1), 1, kernel_size, 1)
        result = F.conv2d(h_filtered, v_kernel, padding=(padding, 0), groups=x.size(1))
        
        return result

    def _original_gaussian_filter(self, x: torch.Tensor, sigma: torch.Tensor) -> torch.Tensor:
        """原始2D高斯滤波 - 作为对比"""
        sigma_val = torch.clamp(sigma, min=0.5, max=50.0).item()
        
        kernel_size = int(2 * sigma_val + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1
        kernel_size = min(kernel_size, 15)
        
        padding = kernel_size // 2
        grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
        
        gaussian_1d = torch.exp(-0.5 * (grid / torch.clamp(sigma, min=1e-6)).pow(2))
        gaussian_1d = gaussian_1d / torch.clamp(gaussian_1d.sum(), min=1e-6)

        gaussian_2d = gaussian_1d.view(1, 1, -1, 1) * gaussian_1d.view(1, 1, 1, -1)
        gaussian_2d = gaussian_2d.expand(x.size(1), 1, kernel_size, kernel_size)

        return F.conv2d(x, gaussian_2d, padding=padding, groups=x.size(1))

    def _gaussian_filter(self, x: torch.Tensor, sigma: torch.Tensor) -> torch.Tensor:
        """选择使用可分离或原始高斯滤波"""
        if self.use_separable_conv:
            return self._separable_gaussian_filter(x, sigma)
        else:
            return self._original_gaussian_filter(x, sigma)

    def _bilateral_approximation(self, x: torch.Tensor, guide: torch.Tensor, 
                                spatial_sigma: torch.Tensor, color_sigma: torch.Tensor) -> torch.Tensor:
        """
        双边滤波近似 - 保持TrainableCRF的实现
        """
        # 空间滤波
        spatial_filtered = self._gaussian_filter(x, spatial_sigma)
        
        # 简化的颜色相似性计算
        guide_mean = F.avg_pool2d(guide, kernel_size=3, stride=1, padding=1)
        guide_diff = guide - guide_mean
        guide_diff_norm = guide_diff.pow(2).sum(dim=1, keepdim=True)
        
        color_sigma_sq = 2 * color_sigma.pow(2)
        color_weight = torch.exp(-guide_diff_norm / color_sigma_sq)
        
        return spatial_filtered * color_weight

    def _apply_compatibility_transform(self, message: torch.Tensor) -> torch.Tensor:
        """应用兼容性变换"""
        if not self.use_compatibility_matrix:
            return message
        
        B, C, H, W = message.shape
        message_flat = message.view(B, C, -1)
        
        # 兼容性变换
        transformed = torch.einsum('ij,bjk->bik', self.compatibility_matrix, message_flat)
        
        return transformed.view(B, C, H, W)

    def forward(self, unary: torch.Tensor, img: torch.Tensor) -> torch.Tensor:
        """优化版CRF前向传播"""
        if unary.device != img.device:
            img = img.to(unary.device)
        
        # 从对数空间恢复参数（保持TrainableCRF的数值稳定性）
        bilateral_weight = torch.exp(torch.clamp(self.log_bilateral_weight, min=-5, max=5))
        gaussian_weight = torch.exp(torch.clamp(self.log_gaussian_weight, min=-5, max=5))
        bilateral_spatial_sigma = torch.exp(torch.clamp(self.log_bilateral_spatial_sigma, min=-3, max=4))
        bilateral_color_sigma = torch.exp(torch.clamp(self.log_bilateral_color_sigma, min=-3, max=3))
        gaussian_sigma = torch.exp(torch.clamp(self.log_gaussian_sigma, min=-3, max=3))
        
        # 初始化
        logits = unary
        Q = F.softmax(logits, dim=1)

        # 平均场迭代
        for i in range(self.n_iter):
            # 消息传递（使用优化的滤波）
            gaussian_term = self._gaussian_filter(Q, gaussian_sigma)
            bilateral_term = self._bilateral_approximation(Q, img, bilateral_spatial_sigma, bilateral_color_sigma)
            
            # 组合消息
            raw_message = gaussian_weight * gaussian_term + bilateral_weight * bilateral_term
            
            # 应用兼容性变换
            if self.use_compatibility_matrix:
                raw_message = self._apply_compatibility_transform(raw_message)
            
            # 消息控制（保持TrainableCRF的稳定性）
            if self.use_message_control:
                normalized_message = torch.tanh(raw_message)
                decay_factor = torch.pow(torch.clamp(self.iteration_decay, min=0.1, max=1.0), i)
                scaled_message = self.message_scaler * decay_factor * normalized_message
            else:
                scaled_message = raw_message

            # 更新
            logits = unary + scaled_message
            Q = F.softmax(logits, dim=1)
            
        return logits

    def get_parameters_summary(self) -> dict:
        """获取参数摘要"""
        with torch.no_grad():
            params = {
                'bilateral_weight': torch.exp(self.log_bilateral_weight).item(),
                'gaussian_weight': torch.exp(self.log_gaussian_weight).item(),
                'bilateral_spatial_sigma': torch.exp(self.log_bilateral_spatial_sigma).item(),
                'bilateral_color_sigma': torch.exp(self.log_bilateral_color_sigma).item(),
                'gaussian_sigma': torch.exp(self.log_gaussian_sigma).item(),
            }
            
            if self.use_compatibility_matrix:
                params['compatibility_matrix'] = self.compatibility_matrix.cpu().numpy().tolist()
            
            if self.use_message_control:
                params['message_scaler'] = self.message_scaler.item()
                params['iteration_decay'] = self.iteration_decay.item()
                
            return params

    @classmethod
    def create_fast_version(cls, n_iter: int = 5):
        """创建高性能版本"""
        return cls(
            n_iter=n_iter,
            use_compatibility_matrix=True,   # 保持理论完整性
            use_separable_conv=True,         # 使用可分离卷积
            use_message_control=True         # 保持训练稳定性
        )
    
    @classmethod
    def create_original_like(cls, n_iter: int = 5):
        """创建接近原始TrainableCRF的版本"""
        return cls(
            n_iter=n_iter,
            use_compatibility_matrix=False,  # 不使用兼容性矩阵
            use_separable_conv=False,        # 不使用可分离卷积
            use_message_control=True         # 保持消息控制
        )


def benchmark_performance():
    """性能测试"""
    print("=== 性能对比测试 ===")
    
    # 测试数据
    batch_size, num_classes, height, width = 4, 2, 384, 384
    unary = torch.randn(batch_size, num_classes, height, width).cuda()
    img = torch.rand(batch_size, 3, height, width).cuda()
    
    import time
    
    # 测试原始版本
    print("\n1. 原始版本 (无优化):")
    crf_original = OptimizedTrainableCRF.create_original_like().cuda()
    
    torch.cuda.synchronize()
    start = time.time()
    for _ in range(10):
        _ = crf_original(unary, img)
    torch.cuda.synchronize()
    original_time = (time.time() - start) / 10
    print(f"平均时间: {original_time*1000:.2f}ms")
    
    # 测试优化版本
    print("\n2. 优化版本 (可分离卷积 + 兼容性矩阵):")
    crf_fast = OptimizedTrainableCRF.create_fast_version().cuda()
    
    torch.cuda.synchronize()
    start = time.time()
    for _ in range(10):
        _ = crf_fast(unary, img)
    torch.cuda.synchronize()
    fast_time = (time.time() - start) / 10
    print(f"平均时间: {fast_time*1000:.2f}ms")
    
    speedup = original_time / fast_time
    print(f"\n🚀 性能提升: {speedup:.1f}x")
    print(f"预期FPS提升: 35 → {35*speedup:.0f}")


if __name__ == "__main__":
    benchmark_performance()
