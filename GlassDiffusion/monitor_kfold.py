#!/usr/bin/env python3
"""
🎯 K-Fold训练监控脚本
实时监控训练进度和IoU稳定性
"""

import time
import json
import os
import glob
import psutil
import subprocess
import numpy as np
from datetime import datetime

class KFoldMonitor:
    def __init__(self):
        self.kfold_path = './ckpt/RTGlassNet_KFold'
        self.runs_path = './runs'
        self.process_name = 'train_kfold_stable'
        
    def get_training_processes(self):
        """获取训练进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if self.process_name in ' '.join(proc.info['cmdline']):
                    processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def get_gpu_usage(self):
        """获取GPU使用情况"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,utilization.gpu', '--format=csv,nounits,noheader'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                gpu_info = result.stdout.strip().split('\n')[0].split(', ')
                memory_used = int(gpu_info[0])
                memory_total = int(gpu_info[1])
                gpu_util = int(gpu_info[2])
                return memory_used, memory_total, gpu_util
        except:
            pass
        return None, None, None
    
    def get_saved_models(self):
        """获取已保存的模型"""
        if not os.path.exists(os.path.join(self.kfold_path, 'weights')):
            return []
        
        models = []
        for file in glob.glob(os.path.join(self.kfold_path, 'weights', '*.pth')):
            filename = os.path.basename(file)
            # 解析文件名：BEST_fold1_epoch020_iou0.7680.pth
            if 'BEST_fold' in filename:
                parts = filename.split('_')
                try:
                    fold = int(parts[1].replace('fold', ''))
                    epoch = int(parts[2].replace('epoch', ''))
                    iou = float(parts[3].replace('iou', '').replace('.pth', ''))
                    models.append({
                        'fold': fold,
                        'epoch': epoch,
                        'iou': iou,
                        'filename': filename,
                        'is_best': True
                    })
                except:
                    pass
        
        return sorted(models, key=lambda x: (x['fold'], x['epoch']))
    
    def get_tensorboard_runs(self):
        """获取TensorBoard运行信息"""
        runs = []
        if not os.path.exists(self.runs_path):
            return runs
        
        for run_dir in glob.glob(os.path.join(self.runs_path, 'kfold_fold*')):
            fold_name = os.path.basename(run_dir)
            if os.path.exists(run_dir):
                runs.append({
                    'fold': fold_name,
                    'path': run_dir,
                    'last_modified': os.path.getmtime(run_dir)
                })
        
        return sorted(runs, key=lambda x: x['fold'])
    
    def get_kfold_summary(self):
        """获取K-Fold汇总结果"""
        summary_file = os.path.join(self.kfold_path, 'kfold_summary.json')
        if os.path.exists(summary_file):
            with open(summary_file, 'r') as f:
                return json.load(f)
        return None
    
    def analyze_iou_stability(self, models):
        """分析IoU稳定性"""
        if not models:
            return None
        
        # 按fold分组
        fold_ious = {}
        for model in models:
            fold = model['fold']
            if fold not in fold_ious:
                fold_ious[fold] = []
            fold_ious[fold].append(model['iou'])
        
        # 计算每个fold的稳定性
        stability_analysis = {}
        for fold, ious in fold_ious.items():
            if len(ious) >= 2:
                std = np.std(ious)
                mean = np.mean(ious)
                cv = std / mean if mean > 0 else 0
                stability_analysis[fold] = {
                    'ious': ious,
                    'mean': mean,
                    'std': std,
                    'cv': cv,
                    'stability': 'stable' if cv < 0.1 else 'unstable'
                }
        
        return stability_analysis
    
    def print_status(self):
        """打印训练状态"""
        print(f"\n{'='*60}")
        print(f"🎯 K-Fold训练监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        # 检查训练进程
        processes = self.get_training_processes()
        if processes:
            print(f"✅ 训练进程运行中: {len(processes)} 个进程")
            for i, proc in enumerate(processes):
                try:
                    cpu_percent = proc.cpu_percent()
                    memory_mb = proc.memory_info().rss / 1024 / 1024
                    print(f"   进程 {i+1}: PID={proc.pid}, CPU={cpu_percent:.1f}%, 内存={memory_mb:.0f}MB")
                except:
                    print(f"   进程 {i+1}: PID={proc.pid}, 状态未知")
        else:
            print("❌ 没有找到训练进程")
        
        # GPU使用情况
        memory_used, memory_total, gpu_util = self.get_gpu_usage()
        if memory_used is not None:
            print(f"🔥 GPU状态: 使用率{gpu_util}%, 内存{memory_used}MB/{memory_total}MB ({memory_used/memory_total*100:.1f}%)")
        else:
            print("❌ GPU状态获取失败")
        
        # 已保存的模型
        models = self.get_saved_models()
        if models:
            print(f"\n📊 已保存模型 ({len(models)} 个):")
            for model in models[-5:]:  # 显示最近5个
                print(f"   Fold {model['fold']}: Epoch {model['epoch']}, IoU {model['iou']:.4f} {'🏆' if model['is_best'] else ''}")
        else:
            print("\n📊 暂无已保存模型")
        
        # IoU稳定性分析
        stability = self.analyze_iou_stability(models)
        if stability:
            print(f"\n🎯 IoU稳定性分析:")
            for fold, stats in stability.items():
                status = "✅ 稳定" if stats['stability'] == 'stable' else "⚠️ 不稳定"
                print(f"   Fold {fold}: 均值{stats['mean']:.4f}, 标准差{stats['std']:.4f}, 变异系数{stats['cv']:.4f} {status}")
        
        # TensorBoard运行信息
        runs = self.get_tensorboard_runs()
        if runs:
            print(f"\n📈 TensorBoard运行:")
            for run in runs:
                last_update = datetime.fromtimestamp(run['last_modified']).strftime('%H:%M:%S')
                print(f"   {run['fold']}: 最后更新 {last_update}")
            print(f"   启动命令: tensorboard --logdir runs/ --port 6006")
        
        # K-Fold汇总结果
        summary = self.get_kfold_summary()
        if summary:
            print(f"\n🎯 K-Fold汇总结果:")
            print(f"   平均IoU: {summary['mean_iou']:.4f} ± {summary['std_iou']:.4f}")
            print(f"   各Fold: {[f'{x:.4f}' for x in summary['fold_results']]}")
            print(f"   稳定性: {summary['stability']:.4f}")
        
        print(f"\n{'='*60}")
    
    def monitor_loop(self, interval=30):
        """监控循环"""
        print("🎯 K-Fold训练监控启动...")
        print(f"   监控间隔: {interval}秒")
        print("   按 Ctrl+C 停止监控")
        
        try:
            while True:
                self.print_status()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n🎯 监控已停止")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='K-Fold训练监控')
    parser.add_argument('--interval', type=int, default=30, help='监控间隔（秒）')
    parser.add_argument('--once', action='store_true', help='只显示一次状态')
    args = parser.parse_args()
    
    monitor = KFoldMonitor()
    
    if args.once:
        monitor.print_status()
    else:
        monitor.monitor_loop(args.interval)

if __name__ == '__main__':
    main() 