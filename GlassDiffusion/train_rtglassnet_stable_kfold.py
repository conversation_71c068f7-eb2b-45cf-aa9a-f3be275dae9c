#!/usr/bin/env python3
"""
🎯 RTGlassNet稳定性优化训练脚本 - K-Fold版本
支持80:20数据分割和k-fold交叉验证，专门解决IoU起伏问题
"""

import os
import warnings
import numpy as np
import random
from sklearn.model_selection import KFold

# 抑制各种警告信息
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import math
import gc

def set_seed(seed=42):
    """设置随机种子，确保可复现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"🎲 随机种子设置为: {seed}")

def clear_cuda_cache():
    """清理CUDA缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet K-Fold稳定性优化训练 - 80:20分割')
    
    # 🔧 稳定性优化参数
    parser.add_argument('--epochs', default=100, type=int, help='🔧 减少轮数：200→100（k-fold需要）')
    parser.add_argument('--bs', default=8, type=int, help='🔧 批次大小优化：减小到8以节省内存')
    parser.add_argument('--lr', default=0.0003, type=float, help='🔧 更保守学习率：0.0005→0.0003')
    parser.add_argument('--min_lr', default=0.00001, type=float, help='最小学习率')
    parser.add_argument('--warmup_epochs', default=5, type=int, help='🔧 减少预热：10→5轮（总轮数减少）')
    parser.add_argument('--wd', default=0.0001, type=float, help='🔧 减小权重衰减：0.0005→0.0001')
    parser.add_argument('--img_size', default=384, type=int, help='🔧 减小图像尺寸：416→384以节省内存')
    parser.add_argument('--aug', default=True, type=bool, help='数据增强')
    parser.add_argument('--n_worker', default=2, type=int, help='🔧 减少工作进程：4→2以节省内存')
    
    # K-Fold交叉验证参数
    parser.add_argument('--k_folds', default=5, type=int, help='K-Fold交叉验证的K值')
    parser.add_argument('--current_fold', default=None, type=int, help='当前训练的fold（如果指定，只训练这一个fold）')
    
    # 训练控制参数
    parser.add_argument('--test_interval', default=5, type=int, help='验证间隔')
    parser.add_argument('--save_interval', default=20, type=int, help='保存间隔')
    parser.add_argument('--log_interval', default=50, type=int, help='日志记录间隔')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--use_gpu', default=True, type=bool, help='使用GPU')
    
    # 🎯 稳定性优化的损失权重配置
    parser.add_argument('--focal_weight', default=0.25, type=float, help='🔧 稳定化：Focal损失权重 0.3→0.25')
    parser.add_argument('--iou_weight', default=0.65, type=float, help='🔧 稳定化：IoU损失权重 0.6→0.65（绝对主导）')
    parser.add_argument('--dt_weight', default=0.10, type=float, help='🔧 稳定化：DT损失权重 0.15→0.10（最小化）')
    parser.add_argument('--use_focal', default=True, type=bool, help='使用Focal损失')
    
    # 骨干网络参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    
    # 🔧 稳定性配置
    parser.add_argument('--ema_decay', default=0.9999, type=float, help='EMA衰减系数')
    parser.add_argument('--use_ema', default=True, type=bool, help='使用EMA稳定训练')
    parser.add_argument('--stable_lr', default=True, type=bool, help='使用稳定学习率调度')
    
    # 💾 内存优化配置
    parser.add_argument('--mixed_precision', default=True, type=bool, help='🔧 启用混合精度训练以节省内存')
    parser.add_argument('--gradient_checkpointing', default=True, type=bool, help='🔧 启用梯度检查点以节省内存')
    
    return parser.parse_args()

class StableLRScheduler:
    """稳定学习率调度器 - 避免余弦退火的陷阱"""
    def __init__(self, optimizer, warmup_epochs, max_epochs, base_lr, min_lr):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.base_lr = base_lr
        self.min_lr = min_lr
        
    def step(self, epoch):
        if epoch < self.warmup_epochs:
            # 线性预热
            lr = self.base_lr * (epoch + 1) / self.warmup_epochs
        else:
            # 🔧 使用更稳定的指数衰减而非余弦退火
            progress = (epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            # 指数衰减，避免突变
            lr = self.min_lr + (self.base_lr - self.min_lr) * (0.95 ** (progress * 30))
        
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr
        
        return lr

class EMA:
    """指数移动平均，稳定训练"""
    def __init__(self, model, decay=0.9999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        
    def register(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
                
    def update(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()
                
    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                self.backup[name] = param.data
                param.data = self.shadow[name]
                
    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.backup
                param.data = self.backup[name]
        self.backup = {}

class KFoldStableEngine:
    def __init__(self, args):
        # 设置随机种子
        set_seed(42)
        
        # 清理CUDA缓存
        clear_cuda_cache()
        
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.min_lr = args.min_lr
        self.warmup_epochs = args.warmup_epochs
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.log_interval = args.log_interval
        self.model_path = args.base_save_path + '/RTGlassNet_KFold_Stable'
        self.use_gpu = args.use_gpu
        self.use_focal = args.use_focal
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        
        # K-Fold配置
        self.k_folds = args.k_folds
        self.current_fold = args.current_fold
        
        # 🎯 稳定性优化的损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        # 稳定性配置
        self.use_ema = args.use_ema
        self.ema_decay = args.ema_decay
        self.stable_lr = args.stable_lr
        
        # 💾 内存优化配置
        self.mixed_precision = args.mixed_precision
        self.gradient_checkpointing = args.gradient_checkpointing
        
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))
        self.device = torch.device('cuda' if torch.cuda.is_available() and self.use_gpu else 'cpu')
        
        # 💾 设置内存优化
        if self.mixed_precision and torch.cuda.is_available():
            from torch.cuda.amp import GradScaler, autocast
            self.scaler = GradScaler()
            self.autocast = autocast
            print("✅ 混合精度训练已启用")
        else:
            self.scaler = None
            self.autocast = None
        
        # 打印稳定性配置信息
        print(f"🎯 RTGlassNet K-Fold稳定性优化训练配置:")
        print(f"   🔧 数据分割: 80%训练 + 20%验证")
        print(f"   🔧 K-Fold交叉验证: {self.k_folds}折")
        print(f"   🔧 批次大小优化: {self.bs} (内存优化)")
        print(f"   🔧 图像尺寸优化: {self.img_size}x{self.img_size} (内存优化)")
        print(f"   🔧 学习率优化: {self.lr} (更保守)")
        print(f"   🔧 损失权重稳定化: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔧 IoU权重占比: {self.iou_weight/(self.focal_weight+self.iou_weight+self.dt_weight)*100:.1f}% - 绝对主导")
        print(f"   🔧 预热策略: {self.warmup_epochs}轮预热 → 稳定指数衰减")
        print(f"   🔧 EMA稳定: {'启用' if self.use_ema else '禁用'}")
        print(f"   🔧 混合精度: {'启用' if self.mixed_precision else '禁用'}")
        print(f"   🔧 梯度检查点: {'启用' if self.gradient_checkpointing else '禁用'}")
        print(f"   🚀 骨干网络: {self.backbone_type}")

    def create_model(self):
        """创建模型实例"""
        model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        
        # 🔧 启用梯度检查点以节省内存
        if self.gradient_checkpointing:
            try:
                model.gradient_checkpointing_enable()
                print("✅ 梯度检查点已启用")
            except:
                print("⚠️ 梯度检查点启用失败，继续训练")
        
        self._load_backbone_weight(model)
        
        # 打印模型参数统计
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"   📊 模型参数: {total_params/1e6:.1f}M总参数, {trainable_params/1e6:.1f}M可训练")
        
        return model

    def _load_backbone_weight(self, model):
        """加载骨干网络权重"""
        weight_path = self.backbone_weight
        if weight_path and os.path.exists(weight_path):
            print(f"加载InceptionNeXt权重: {weight_path}")
            state_dict = torch.load(weight_path, map_location=self.device)
            model_backbone = model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"已加载 {len(filtered_dict)} 个backbone参数.")
        else:
            print(f"未找到权重文件: {weight_path}，跳过加载。")

    def create_data_splits(self):
        """创建80:20数据分割和K-Fold交叉验证"""
        print(f"\n🔄 创建K-Fold数据分割...")
        
        # 创建完整数据集（80%用于K-Fold，20%用于最终测试）
        full_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='all',  # 获取全部数据
            target_size=(self.img_size, self.img_size), 
            split_ratio=1.0,  # 使用全部数据
            random_seed=42, 
            glass_aug_config=None  # 先不增强，后面按需加
        )
        
        total_samples = len(full_data)
        indices = list(range(total_samples))
        np.random.seed(42)
        np.random.shuffle(indices)
        
        # 80:20分割
        split_idx = int(0.8 * total_samples)
        train_val_indices = indices[:split_idx]  # 80%用于K-Fold交叉验证
        test_indices = indices[split_idx:]       # 20%用于最终测试
        
        print(f"📊 数据分割完成:")
        print(f"   总样本: {total_samples}")
        print(f"   K-Fold训练验证: {len(train_val_indices)} (80%)")
        print(f"   最终测试: {len(test_indices)} (20%)")
        
        # 创建K-Fold分割
        kfold = KFold(n_splits=self.k_folds, shuffle=True, random_state=42)
        fold_splits = list(kfold.split(train_val_indices))
        
        return full_data, fold_splits, train_val_indices, test_indices

    def train_fold(self, fold_idx, full_data, train_indices, val_indices):
        """训练单个fold"""
        print(f"\n🎯 开始训练 Fold {fold_idx + 1}/{self.k_folds}")
        print(f"   训练样本: {len(train_indices)}, 验证样本: {len(val_indices)}")
        
        # 清理内存
        clear_cuda_cache()
        
        # 创建模型
        model = self.create_model()
        
        # 🔧 初始化EMA
        ema = None
        if self.use_ema:
            ema = EMA(model, decay=self.ema_decay)
            ema.register()
            print(f"✅ EMA已启用，衰减系数: {self.ema_decay}")
        
        # 🎯 稳定化损失函数
        criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=self.use_focal
        )
        
        # 🔧 稳定性优化的优化器配置
        optimizer = optim.AdamW(
            model.parameters(), 
            lr=self.lr, 
            weight_decay=self.wd,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 🔧 稳定学习率调度器
        scheduler = None
        if self.stable_lr:
            scheduler = StableLRScheduler(
                optimizer, 
                warmup_epochs=self.warmup_epochs,
                max_epochs=self.epochs,
                base_lr=self.lr,
                min_lr=self.min_lr
            )
        
        # 创建数据加载器
        train_dataset = Subset(full_data, train_indices)
        val_dataset = Subset(full_data, val_indices)
        
        # 🔧 为训练集添加数据增强
        if self.aug:
            for idx in range(len(train_dataset)):
                train_dataset.dataset.set_augmentation('light')  # 轻量增强
        
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.bs, 
            shuffle=True, 
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True  # 🔧 丢弃最后一个不完整批次
        )
        val_loader = DataLoader(
            val_dataset, 
            batch_size=self.bs, 
            shuffle=False, 
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=False
        )
        
        # TensorBoard记录
        writer_train = SummaryWriter(log_dir=f'runs/kfold_stable_train_fold{fold_idx+1}')
        writer_val = SummaryWriter(log_dir=f'runs/kfold_stable_val_fold{fold_idx+1}')
        
        best_iou = 0.0
        iou_history = []  # 🔧 IoU历史记录
        stability_threshold = 0.05  # 🔧 稳定性阈值
        
        for epoch in range(1, self.epochs + 1):
            # 🔧 更新学习率
            if scheduler:
                current_lr = scheduler.step(epoch - 1)
            else:
                current_lr = optimizer.param_groups[0]['lr']
            
            model.train()
            running_loss = 0.0
            train_tqdm = tqdm(train_loader, desc=f"Fold{fold_idx+1} Epoch{epoch}/{self.epochs} [LR:{current_lr:.6f}]")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device, non_blocking=True)
                gt_masks = gt_masks.to(self.device, non_blocking=True)
                dt_maps = dt_maps.to(self.device, non_blocking=True)
                
                optimizer.zero_grad()
                
                # 🔧 混合精度前向传播
                if self.autocast:
                    with self.autocast():
                        outputs = model(inp_imgs)
                        loss, loss_dict = criterion(outputs, gt_masks, dt_maps)
                else:
                    outputs = model(inp_imgs)
                    loss, loss_dict = criterion(outputs, gt_masks, dt_maps)
                
                # 🔧 数值稳定性检查
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失值，跳过此批次")
                    continue
                
                # 🔧 混合精度反向传播
                if self.scaler:
                    self.scaler.scale(loss).backward()
                    self.scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                    self.scaler.step(optimizer)
                    self.scaler.update()
                else:
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                    optimizer.step()
                
                # 🔧 EMA更新
                if ema:
                    ema.update()
                
                running_loss += loss.item()
                
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}", 
                    avg_loss=f"{running_loss/(batch_idx+1):.4f}",
                    focal=f"{loss_dict['focal_loss']:.4f}",
                    iou=f"{loss_dict['iou_loss']:.4f}",
                    dt=f"{loss_dict['dt_loss']:.4f}",
                    lr=f"{current_lr:.6f}"
                )
                
                if batch_idx % self.log_interval == 0:
                    global_step = (epoch - 1) * len(train_loader) + batch_idx
                    writer_train.add_scalar('loss/total', loss.item(), global_step)
                    writer_train.add_scalar('loss/focal', loss_dict['focal_loss'], global_step)
                    writer_train.add_scalar('loss/iou', loss_dict['iou_loss'], global_step)
                    writer_train.add_scalar('loss/dt', loss_dict['dt_loss'], global_step)
                    writer_train.add_scalar('lr', current_lr, global_step)
                    
            epoch_loss = running_loss / len(train_loader)
            print(f"Fold{fold_idx+1} 训练轮次: {epoch} 损失: {epoch_loss:.6f} 学习率: {current_lr:.6f}")
            
            # 验证
            if epoch % self.test_interval == 0:
                val_loss, val_iou = self.validate(model, val_loader, ema, epoch)
                iou_history.append(val_iou)
                
                # 🔧 稳定性分析
                if len(iou_history) >= 3:
                    recent_ious = iou_history[-3:]
                    iou_std = np.std(recent_ious)
                    print(f"📊 Fold{fold_idx+1} 最近3轮IoU标准差: {iou_std:.4f} (稳定阈值: {stability_threshold})")
                    
                    if iou_std < stability_threshold:
                        print("✅ 训练已稳定！")
                    else:
                        print("⚠️ 训练仍有波动")
                
                # 🏆 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    self.save_fold_model(fold_idx, epoch, model, optimizer, ema, val_iou, is_best=True)
                    
                    if val_iou > 0.8:  # 达到80%目标
                        print(f"🎉 Fold{fold_idx+1} 达到目标IoU 80%+! 当前IoU: {val_iou:.4f}")
                
                # 记录验证指标
                writer_val.add_scalar('loss/total', val_loss, epoch)
                writer_val.add_scalar('metrics/iou', val_iou, epoch)
                writer_val.add_scalar('metrics/best_iou', best_iou, epoch)
                
                # 🔧 保存IoU历史
                if len(iou_history) >= 5:
                    iou_history = iou_history[-5:]  # 只保留最近5轮
                    
            if self.save_interval and epoch % self.save_interval == 0:
                self.save_fold_model(fold_idx, epoch, model, optimizer, ema, best_iou, is_best=False)
                
        writer_train.close()
        writer_val.close()
        
        print(f"\n🎯 Fold {fold_idx + 1} 训练完成！")
        print(f"   最佳IoU: {best_iou:.4f}")
        if len(iou_history) >= 3:
            final_std = np.std(iou_history[-3:])
            print(f"   最终稳定性: {final_std:.4f} (目标: <{stability_threshold})")
            
        # 清理内存
        del model, optimizer, ema, criterion
        clear_cuda_cache()
        
        return best_iou

    def validate(self, model, val_loader, ema, epoch):
        """验证函数"""
        model.eval()
        
        # 🔧 使用EMA进行验证
        if ema:
            ema.apply_shadow()
            
        running_loss = 0.0
        iou_list = []
        dt_iou_list = []
        
        with torch.no_grad():
            val_tqdm = tqdm(val_loader, desc="验证")
            
            # 🔧 限制验证样本数量，减少随机性
            total_batches = min(len(val_loader), 30)  # 最多30个批次
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(val_tqdm):
                if batch_idx >= total_batches:
                    break
                    
                inp_imgs = inp_imgs.to(self.device, non_blocking=True)
                gt_masks = gt_masks.to(self.device, non_blocking=True)
                dt_maps = dt_maps.to(self.device, non_blocking=True)
                
                # 🔧 混合精度验证
                if self.autocast:
                    with self.autocast():
                        outputs = model(inp_imgs)
                        loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                else:
                    outputs = model(inp_imgs)
                    loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                running_loss += loss.item()
                
                # 主预测IoU计算
                main_pred = outputs['main_pred']
                pred_bin = (main_pred > 0.5).float()
                intersection = (pred_bin * gt_masks).sum(dim=(1,2,3))
                union = pred_bin.sum(dim=(1,2,3)) + gt_masks.sum(dim=(1,2,3)) - intersection
                iou = (intersection / (union + 1e-7)).mean().item()
                iou_list.append(iou)
                
                # DT预测质量评估
                dt_pred = outputs['dt_pred']
                dt_mae = F.l1_loss(dt_pred, dt_maps).item()
                dt_iou_list.append(1.0 - dt_mae)
                
        val_loss = running_loss / total_batches
        mean_iou = sum(iou_list) / len(iou_list)
        mean_dt_quality = sum(dt_iou_list) / len(dt_iou_list)
        
        # 🔧 恢复EMA
        if ema:
            ema.restore()
        
        print(f'🎯 验证结果 :: IoU: {mean_iou:.4f} | DT质量: {mean_dt_quality:.4f} | 损失: {val_loss:.4f}')
        
        return val_loss, mean_iou

    def save_fold_model(self, fold_idx, epoch, model, optimizer, ema, val_iou, is_best=False):
        """保存fold模型"""
        
        # 如果使用EMA，保存EMA权重
        if ema:
            ema.apply_shadow()
            
        state = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'epoch': epoch,
            'fold': fold_idx,
            'val_iou': val_iou,
            'backbone_type': self.backbone_type,
            'ema_shadow': ema.shadow if ema else None,
            'model_info': {
                'backbone': self.backbone_type,
                'architecture': 'RTGlassNet_KFold_Stable',
                'features': ['InceptionNeXt', 'FPN', 'LightEdgeEnhancer', 'SCSA', 'DT_Prediction', 'EMA'],
                'k_folds': self.k_folds,
                'current_fold': fold_idx
            }
        }
        
        if is_best:
            save_path = os.path.join(self.model_path, 'weights', f'🎯KFOLD_BEST_fold{fold_idx+1}_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"🏆 🎯K-Fold最佳模型🎯 已保存: Fold{fold_idx+1} epoch {epoch}, IoU {val_iou:.4f}")
            print(f"   优化特征: K-Fold+80:20分割+EMA稳定+混合精度")
        else:
            save_path = os.path.join(self.model_path, 'weights', f'kfold_checkpoint_fold{fold_idx+1}_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"📦 K-Fold检查点已保存: Fold{fold_idx+1} epoch {epoch}, IoU {val_iou:.4f}")
            
        # 恢复EMA
        if ema:
            ema.restore()

    def train_kfold(self):
        """执行K-Fold交叉验证训练"""
        print(f"\n🎯 开始K-Fold交叉验证训练...")
        
        # 创建数据分割
        full_data, fold_splits, train_val_indices, test_indices = self.create_data_splits()
        
        fold_results = []
        
        # 如果指定了current_fold，只训练这一个fold
        if self.current_fold is not None:
            if 0 <= self.current_fold < self.k_folds:
                fold_idx = self.current_fold
                train_fold_indices, val_fold_indices = fold_splits[fold_idx]
                train_indices = [train_val_indices[i] for i in train_fold_indices]
                val_indices = [train_val_indices[i] for i in val_fold_indices]
                
                best_iou = self.train_fold(fold_idx, full_data, train_indices, val_indices)
                fold_results.append(best_iou)
                print(f"\n🎯 单Fold训练完成: Fold {fold_idx + 1}, IoU: {best_iou:.4f}")
            else:
                print(f"❌ 错误：current_fold {self.current_fold} 超出范围 [0, {self.k_folds-1}]")
                return
        else:
            # 训练所有fold
            for fold_idx, (train_fold_indices, val_fold_indices) in enumerate(fold_splits):
                train_indices = [train_val_indices[i] for i in train_fold_indices]
                val_indices = [train_val_indices[i] for i in val_fold_indices]
                
                best_iou = self.train_fold(fold_idx, full_data, train_indices, val_indices)
                fold_results.append(best_iou)
        
        # 汇总K-Fold结果
        if len(fold_results) > 1:
            mean_iou = np.mean(fold_results)
            std_iou = np.std(fold_results)
            
            print(f"\n🎯 K-Fold交叉验证结果汇总:")
            print(f"   平均IoU: {mean_iou:.4f} ± {std_iou:.4f}")
            print(f"   各Fold IoU: {[f'{iou:.4f}' for iou in fold_results]}")
            print(f"   最佳Fold IoU: {max(fold_results):.4f}")
            print(f"   最差Fold IoU: {min(fold_results):.4f}")
            
            # 🔧 稳定性评估
            cv_score = std_iou / mean_iou  # 变异系数
            print(f"   变异系数: {cv_score:.4f} ({'稳定' if cv_score < 0.1 else '不稳定'})")
            
            # 保存K-Fold汇总结果
            summary = {
                'k_folds': self.k_folds,
                'fold_results': fold_results,
                'mean_iou': mean_iou,
                'std_iou': std_iou,
                'cv_score': cv_score,
                'config': {
                    'lr': self.lr,
                    'bs': self.bs,
                    'img_size': self.img_size,
                    'focal_weight': self.focal_weight,
                    'iou_weight': self.iou_weight,
                    'dt_weight': self.dt_weight
                }
            }
            
            summary_path = os.path.join(self.model_path, f'kfold_summary_{self.k_folds}fold.json')
            import json
            with open(summary_path, 'w') as f:
                json.dump(summary, f, indent=2)
            print(f"📊 K-Fold汇总结果已保存: {summary_path}")

def main():
    args = parse_arguments()
    engine = KFoldStableEngine(args)
    engine.train_kfold()

if __name__ == '__main__':
    main() 