"""
专门针对玻璃检测的数据增强模块
考虑玻璃的物理特质：透明度、反射、折射、光学变形等
"""

import cv2
import numpy as np
import torch
import random
from scipy import ndimage
from skimage import filters, exposure
from typing import Dict, Optional, Tuple, Union


def glass_aware_brightness_contrast(inp_img, preserve_transparency=True):
    """
    玻璃感知的亮度对比度调整
    保持玻璃区域的透明度特征
    """
    # 检测可能的玻璃区域（低饱和度、高明度变化）
    hsv = cv2.cvtColor(inp_img.astype(np.uint8), cv2.COLOR_RGB2HSV)
    saturation = hsv[:, :, 1] / 255.0
    value = hsv[:, :, 2] / 255.0
    
    # 玻璃区域通常饱和度低
    glass_mask = (saturation < 0.3).astype(np.float32)
    
    if preserve_transparency:
        # 对非玻璃区域进行更强的增强
        contrast_factor = np.random.uniform(0.7, 1.3)
        brightness_offset = np.random.randint(-15, 15)
        
        # 对玻璃区域进行更温和的增强
        glass_contrast = np.random.uniform(0.9, 1.1)
        glass_brightness = np.random.randint(-5, 5)
        
        # 分别处理
        non_glass_enhanced = contrast_factor * inp_img + brightness_offset
        glass_enhanced = glass_contrast * inp_img + glass_brightness
        
        # 融合结果
        glass_mask_3d = np.stack([glass_mask] * 3, axis=2)
        result = glass_mask_3d * glass_enhanced + (1 - glass_mask_3d) * non_glass_enhanced
    else:
        # 传统方法
        contrast = np.random.uniform(0.8, 1.2)
        brightness = np.random.randint(-10, 10)
        result = contrast * inp_img + brightness
    
    return np.clip(result, 0, 255)


def simulate_glass_reflection(inp_img, intensity=0.3):
    """
    模拟玻璃反射效果
    在图像上添加微弱的反射光斑
    """
    h, w = inp_img.shape[:2]
    
    # 创建随机反射光斑
    num_reflections = np.random.randint(1, 4)
    reflection_mask = np.zeros((h, w), dtype=np.float32)
    
    for _ in range(num_reflections):
        # 随机位置和大小
        center_x = np.random.randint(w // 4, 3 * w // 4)
        center_y = np.random.randint(h // 4, 3 * h // 4)
        radius = np.random.randint(min(h, w) // 20, min(h, w) // 8)
        
        # 创建高斯光斑
        y, x = np.ogrid[:h, :w]
        mask = ((x - center_x) ** 2 + (y - center_y) ** 2) <= radius ** 2
        
        # 添加高斯衰减
        distances = np.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)
        gaussian_mask = np.exp(-(distances ** 2) / (2 * (radius / 3) ** 2))
        gaussian_mask[~mask] = 0
        
        reflection_mask += gaussian_mask * np.random.uniform(0.1, intensity)
    
    # 应用反射效果
    reflection_mask = np.clip(reflection_mask, 0, 1)
    reflection_mask_3d = np.stack([reflection_mask] * 3, axis=2)
    
    # 反射通常增加亮度
    reflected_img = inp_img + reflection_mask_3d * 50
    
    return np.clip(reflected_img, 0, 255)


def simulate_glass_distortion(inp_img, out_img, strength=0.02):
    """
    模拟玻璃的光学扭曲效果
    轻微的非线性变形，模拟玻璃的折射
    """
    h, w = inp_img.shape[:2]
    
    # 创建扭曲场
    x, y = np.meshgrid(np.arange(w), np.arange(h))
    
    # 添加随机波动
    wave_x = strength * w * np.sin(2 * np.pi * y / (h / np.random.uniform(2, 6)))
    wave_y = strength * h * np.sin(2 * np.pi * x / (w / np.random.uniform(2, 6)))
    
    # 添加随机噪声
    noise_x = np.random.normal(0, strength * w / 100, (h, w))
    noise_y = np.random.normal(0, strength * h / 100, (h, w))
    
    # 组合扭曲
    map_x = (x + wave_x + noise_x).astype(np.float32)
    map_y = (y + wave_y + noise_y).astype(np.float32)
    
    # 应用扭曲
    distorted_img = cv2.remap(inp_img, map_x, map_y, cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
    distorted_mask = cv2.remap(out_img, map_x, map_y, cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
    
    return distorted_img, distorted_mask


def glass_aware_rotation(inp_img, out_img, max_angle=15):
    """
    玻璃感知的旋转增强
    考虑到玻璃通常有规则的几何形状，限制旋转角度
    """
    # 玻璃通常是矩形或规则形状，大角度旋转不现实
    angle = np.random.uniform(-max_angle, max_angle)
    
    h, w = out_img.shape
    center = (w / 2, h / 2)
    
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    cos = np.abs(M[0, 0])
    sin = np.abs(M[0, 1])
    
    # 计算新的图像尺寸
    new_w = int((h * sin) + (w * cos))
    new_h = int((h * cos) + (w * sin))
    M[0, 2] += (new_w / 2) - center[0]
    M[1, 2] += (new_h / 2) - center[1]
    
    rotated_img = cv2.warpAffine(inp_img, M, (new_w, new_h), borderMode=cv2.BORDER_REFLECT)
    rotated_mask = cv2.warpAffine(out_img, M, (new_w, new_h), borderMode=cv2.BORDER_REFLECT)
    
    return rotated_img, rotated_mask


def simulate_lighting_variation(inp_img, glass_sensitive=True):
    """
    模拟光照变化，考虑玻璃对光照的敏感性
    """
    if glass_sensitive:
        # 检测玻璃区域
        hsv = cv2.cvtColor(inp_img.astype(np.uint8), cv2.COLOR_RGB2HSV)
        saturation = hsv[:, :, 1] / 255.0
        glass_mask = (saturation < 0.3).astype(np.float32)
        
        # 创建不均匀光照
        h, w = inp_img.shape[:2]
        
        # 随机光源位置
        light_x = np.random.uniform(0.2, 0.8) * w
        light_y = np.random.uniform(0.2, 0.8) * h
        
        # 创建径向光照梯度
        y, x = np.ogrid[:h, :w]
        distances = np.sqrt((x - light_x) ** 2 + (y - light_y) ** 2)
        max_distance = np.sqrt(w ** 2 + h ** 2)
        
        # 光照强度随距离衰减
        light_intensity = 1 - (distances / max_distance) * np.random.uniform(0.3, 0.7)
        light_intensity = np.clip(light_intensity, 0.3, 1.0)
        
        # 玻璃区域对光照更敏感
        glass_sensitivity = 1 + glass_mask * np.random.uniform(0.2, 0.5)
        final_lighting = light_intensity * glass_sensitivity
        
        # 应用光照
        final_lighting_3d = np.stack([final_lighting] * 3, axis=2)
        result = inp_img * final_lighting_3d
    else:
        # 简单的全局光照变化
        factor = np.random.uniform(0.7, 1.3)
        result = inp_img * factor
    
    return np.clip(result, 0, 255)


def add_environmental_reflections(inp_img, reflection_prob=0.3):
    """
    添加环境反射，模拟玻璃表面的环境映像
    """
    if np.random.random() < reflection_prob:
        h, w = inp_img.shape[:2]
        
        # 创建简单的环境反射模式
        patterns = ['horizontal_lines', 'vertical_lines', 'grid', 'clouds']
        pattern = np.random.choice(patterns)
        
        reflection_intensity = np.random.uniform(0.1, 0.3)
        
        if pattern == 'horizontal_lines':
            # 水平线条（如百叶窗反射）
            line_spacing = np.random.randint(10, 30)
            reflection_mask = np.zeros((h, w))
            for y in range(0, h, line_spacing):
                if y + 2 < h:
                    reflection_mask[y:y+2, :] = 1
                    
        elif pattern == 'vertical_lines':
            # 垂直线条
            line_spacing = np.random.randint(15, 40)
            reflection_mask = np.zeros((h, w))
            for x in range(0, w, line_spacing):
                if x + 2 < w:
                    reflection_mask[:, x:x+2] = 1
                    
        elif pattern == 'grid':
            # 网格模式
            grid_size = np.random.randint(20, 50)
            reflection_mask = np.zeros((h, w))
            for y in range(0, h, grid_size):
                reflection_mask[y:y+1, :] = 1
            for x in range(0, w, grid_size):
                reflection_mask[:, x:x+1] = 1
                
        else:  # clouds
            # 云状反射
            reflection_mask = np.random.random((h//4, w//4))
            reflection_mask = cv2.resize(reflection_mask, (w, h))
            reflection_mask = filters.gaussian(reflection_mask, sigma=2)
            reflection_mask = (reflection_mask > 0.6).astype(np.float32)
        
        # 应用反射
        reflection_mask = reflection_mask * reflection_intensity
        reflection_mask_3d = np.stack([reflection_mask] * 3, axis=2)
        
        # 反射通常是加性的
        result = inp_img + reflection_mask_3d * 30
        return np.clip(result, 0, 255)
    
    return inp_img


def glass_aware_crop_flip(inp_img, out_img, preserve_aspect=True):
    """
    玻璃感知的裁剪和翻转
    考虑玻璃的几何特性
    """
    h, w = out_img.shape
    
    if preserve_aspect:
        # 保持长宽比的裁剪，适合矩形玻璃
        crop_ratio = np.random.uniform(0.8, 0.95)
        new_h = int(h * crop_ratio)
        new_w = int(w * crop_ratio)
        
        start_h = np.random.randint(0, h - new_h + 1)
        start_w = np.random.randint(0, w - new_w + 1)
        
        cropped_img = inp_img[start_h:start_h+new_h, start_w:start_w+new_w]
        cropped_mask = out_img[start_h:start_h+new_h, start_w:start_w+new_w]
    else:
        # 传统随机裁剪
        rand_h = np.random.randint(h//8) if h//8 > 0 else 0
        rand_w = np.random.randint(w//8) if w//8 > 0 else 0
        offset_h = 0 if rand_h == 0 else np.random.randint(rand_h)
        offset_w = 0 if rand_w == 0 else np.random.randint(rand_w)
        p0, p1, p2, p3 = offset_h, h+offset_h-rand_h, offset_w, w+offset_w-rand_w
        
        cropped_img = inp_img[p0:p1, p2:p3]
        cropped_mask = out_img[p0:p1, p2:p3]
    
    # 翻转（玻璃的水平翻转是合理的）
    if np.random.random() > 0.5:
        cropped_img = cropped_img[:, ::-1, :]
        cropped_mask = cropped_mask[:, ::-1]
    
    return cropped_img, cropped_mask


def apply_glass_augmentation(inp_img, out_img, augmentation_config=None):
    """
    应用玻璃特定的数据增强
    
    Args:
        inp_img: 输入图像 (H, W, C)
        out_img: 输出掩码 (H, W)
        augmentation_config: 增强配置字典
    
    Returns:
        增强后的图像和掩码
    """
    if augmentation_config is None:
        augmentation_config = {
            'brightness_contrast': True,
            'glass_reflection': True,
            'glass_distortion': True,
            'lighting_variation': True,
            'environmental_reflection': True,
            'rotation': True,
            'crop_flip': True,
            'preserve_glass_properties': True
        }
    
    aug_img = inp_img.copy()
    aug_mask = out_img.copy()
    
    # 1. 裁剪和翻转
    if augmentation_config.get('crop_flip', True):
        aug_img, aug_mask = glass_aware_crop_flip(
            aug_img, aug_mask, 
            preserve_aspect=augmentation_config.get('preserve_glass_properties', True)
        )
    
    # 2. 旋转
    if augmentation_config.get('rotation', True):
        if np.random.random() > 0.3:  # 70%概率应用旋转
            aug_img, aug_mask = glass_aware_rotation(aug_img, aug_mask, max_angle=15)
    
    # 3. 光学扭曲
    if augmentation_config.get('glass_distortion', True):
        if np.random.random() > 0.6:  # 40%概率应用扭曲
            aug_img, aug_mask = simulate_glass_distortion(
                aug_img, aug_mask, 
                strength=np.random.uniform(0.01, 0.03)
            )
    
    # 4. 亮度对比度调整
    if augmentation_config.get('brightness_contrast', True):
        aug_img = glass_aware_brightness_contrast(
            aug_img, 
            preserve_transparency=augmentation_config.get('preserve_glass_properties', True)
        )
    
    # 5. 光照变化
    if augmentation_config.get('lighting_variation', True):
        if np.random.random() > 0.4:  # 60%概率应用光照变化
            aug_img = simulate_lighting_variation(
                aug_img, 
                glass_sensitive=augmentation_config.get('preserve_glass_properties', True)
            )
    
    # 6. 玻璃反射
    if augmentation_config.get('glass_reflection', True):
        if np.random.random() > 0.5:  # 50%概率应用反射
            aug_img = simulate_glass_reflection(aug_img, intensity=np.random.uniform(0.2, 0.4))
    
    # 7. 环境反射
    if augmentation_config.get('environmental_reflection', True):
        aug_img = add_environmental_reflections(aug_img, reflection_prob=0.3)
    
    return aug_img, aug_mask


# 玻璃增强配置
GLASS_AUGMENTATION_CONFIGS = {
    'none': {
        'enabled': False
    },
    'light': {
        'enabled': True,
        'blur_prob': 0.3,
        'blur_kernel': (3, 3),
        'transparency_range': (0.7, 0.9),
        'reflection_prob': 0.3,
        'reflection_alpha_range': (0.1, 0.3)
    },
    'moderate': {
        'enabled': True,
        'blur_prob': 0.5,
        'blur_kernel': (5, 5),
        'transparency_range': (0.5, 0.8),
        'reflection_prob': 0.5,
        'reflection_alpha_range': (0.2, 0.4)
    },
    'heavy': {
        'enabled': True,
        'blur_prob': 0.7,
        'blur_kernel': (7, 7),
        'transparency_range': (0.3, 0.7),
        'reflection_prob': 0.7,
        'reflection_alpha_range': (0.3, 0.5)
    }
}

class GlassAugmentation:
    """
    玻璃效果增强器 - 模拟真实玻璃的视觉特性
    """
    def __init__(self, config_name: str = 'moderate'):
        """
        初始化玻璃增强器
        Args:
            config_name: 增强配置名称，可选 'none', 'light', 'moderate', 'heavy'
        """
        if config_name not in GLASS_AUGMENTATION_CONFIGS:
            raise ValueError(f"无效的配置名称: {config_name}")
            
        self.config = GLASS_AUGMENTATION_CONFIGS[config_name]
        
    def _apply_blur(self, image: np.ndarray) -> np.ndarray:
        """应用高斯模糊"""
        if np.random.random() < self.config['blur_prob']:
            kernel = self.config['blur_kernel']
            return cv2.GaussianBlur(image, kernel, 0)
        return image
    
    def _apply_transparency(self, image: np.ndarray) -> np.ndarray:
        """应用透明度效果"""
        if not self.config['enabled']:
            return image
            
        alpha = np.random.uniform(*self.config['transparency_range'])
        white_bg = np.ones_like(image) * 255
        return cv2.addWeighted(image, alpha, white_bg, 1-alpha, 0)
    
    def _apply_reflection(self, image: np.ndarray) -> np.ndarray:
        """应用反射效果"""
        if not self.config['enabled'] or np.random.random() > self.config['reflection_prob']:
            return image
            
        # 创建反射图像（水平翻转）
        reflection = cv2.flip(image, 1)
        
        # 随机反射强度
        alpha = np.random.uniform(*self.config['reflection_alpha_range'])
        
        # 添加反射
        return cv2.addWeighted(image, 1-alpha, reflection, alpha, 0)
    
    def __call__(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        应用玻璃效果增强
        Args:
            image: [H, W, 3] RGB图像
            mask: [H, W] 二值掩码，可选
        Returns:
            augmented_image: [H, W, 3] 增强后的图像
        """
        if not self.config['enabled']:
            return image
            
        # 仅在玻璃区域应用增强
        if mask is not None:
            glass_region = mask > 0
            non_glass = image.copy()
            
            # 对玻璃区域应用增强
            glass_area = image.copy()
            glass_area = self._apply_blur(glass_area)
            glass_area = self._apply_transparency(glass_area)
            glass_area = self._apply_reflection(glass_area)
            
            # 合并结果
            glass_region = np.stack([glass_region] * 3, axis=-1)
            augmented = np.where(glass_region, glass_area, non_glass)
            return augmented
            
        # 如果没有掩码，对整个图像应用增强
        augmented = self._apply_blur(image)
        augmented = self._apply_transparency(augmented)
        augmented = self._apply_reflection(augmented)
        return augmented

def apply_glass_augmentation(
    image: np.ndarray,
    mask: Optional[np.ndarray] = None,
    config_name: str = 'moderate'
) -> np.ndarray:
    """
    应用玻璃效果增强的便捷函数
    Args:
        image: [H, W, 3] RGB图像
        mask: [H, W] 二值掩码，可选
        config_name: 增强配置名称
    Returns:
        augmented_image: [H, W, 3] 增强后的图像
    """
    augmentor = GlassAugmentation(config_name)
    return augmentor(image, mask) 