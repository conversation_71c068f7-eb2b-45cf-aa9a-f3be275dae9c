#!/usr/bin/env python3
"""
🎯 RTGlassNet K-Fold优化训练脚本
包含差分学习率、warmup、早停、数值稳定性检查等所有改进
"""

import os
import warnings
import numpy as np
import random
from sklearn.model_selection import KFold

# 抑制警告
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import json
import gc
from torchvision import transforms

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    print(f"🎲 随机种子设置为: {seed}")

def clear_cuda_cache():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet K-Fold优化训练')
    
    # 基础参数
    parser.add_argument('--epochs', default=800, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='批次大小')
    parser.add_argument('--backbone_lr', default=1e-5, type=float, help='backbone学习率')
    parser.add_argument('--decoder_lr', default=3e-4, type=float, help='decoder学习率')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--n_worker', default=4, type=int, help='工作进程数')
    parser.add_argument('--warmup_epochs', default=10, type=int, help='warmup轮数')
    
    # K-Fold参数
    parser.add_argument('--k_folds', default=0, type=int, help='K-Fold交叉验证的K值')
    parser.add_argument('--current_fold', default=None, type=int, help='当前训练的fold')
    
    # 损失权重 - 修正后的权重
    parser.add_argument('--focal_weight', default=0.3, type=float, help='Focal损失权重')
    parser.add_argument('--iou_weight', default=0.5, type=float, help='IoU损失权重')
    parser.add_argument('--dt_weight', default=0.2, type=float, help='DT损失权重')
    
    # 训练策略
    parser.add_argument('--val_freq', default=5, type=int, help='验证频率(轮数)')
    parser.add_argument('--patience', default=15, type=int, help='早停耐心值')
    parser.add_argument('--grad_clip', default=0.5, type=float, help='梯度裁剪阈值')
    
    # 其他参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--aug_config', default='moderate', type=str, help='数据增强配置')
    
    return parser.parse_args()

class OptimizedKFoldEngine:
    def __init__(self, args):
        set_seed(42)
        clear_cuda_cache()
        
        self.epochs = args.epochs
        self.bs = args.bs
        self.backbone_lr = args.backbone_lr
        self.decoder_lr = args.decoder_lr
        self.img_size = args.img_size
        self.n_worker = args.n_worker
        self.k_folds = args.k_folds
        self.current_fold = args.current_fold
        self.warmup_epochs = args.warmup_epochs
        self.val_freq = args.val_freq
        self.patience = args.patience
        self.grad_clip = args.grad_clip
        
        # 损失权重 - 修正后的权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        # 其他配置
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        self.model_path = args.base_save_path + '/RTGlassNet_Optimized'
        self.aug_config = args.aug_config
        
        # 创建保存目录
        os.makedirs(os.path.join(self.model_path, 'weights'), exist_ok=True)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化损失函数
        self.criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=True
        )
        
        print(f"🎯 RTGlassNet K-Fold优化训练配置:")
        print(f"   🔧 数据分割: 80%训练+20%验证")
        print(f"   🔧 K-Fold: {self.k_folds}折")
        print(f"   🔧 批次大小: {self.bs}")
        print(f"   🔧 图像尺寸: {self.img_size}x{self.img_size}")
        print(f"   🔧 差分学习率: Backbone({self.backbone_lr}) + Decoder({self.decoder_lr})")
        print(f"   🔧 Warmup轮数: {self.warmup_epochs}")
        print(f"   🔧 验证频率: 每{self.val_freq}轮")
        print(f"   🔧 早停耐心: {self.patience}轮")
        print(f"   🔧 梯度裁剪: {self.grad_clip}")
        print(f"   🔧 数据增强: {self.aug_config} (可选: none/light/moderate/heavy)")
        print(f"   🔧 损失权重: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔧 IoU权重占比: {self.iou_weight*100:.1f}%")

    def create_model(self):
        """创建模型 - 每一折都从相同的预训练状态开始"""
        print(f"\n🔧 创建新的RTGlassNet模型实例...")
        
        # 创建全新的模型实例，直接使用pretrained=True
        model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        
        # 打印模型总参数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 模型总参数量: {total_params/1e6:.1f}M")
        
        return model

    def _load_backbone_weight(self, model):
        """加载预训练权重"""
        if self.backbone_weight and os.path.exists(self.backbone_weight):
            print(f"加载权重: {self.backbone_weight}")
            state_dict = torch.load(self.backbone_weight, map_location=self.device)
            model_backbone = model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"已加载 {len(filtered_dict)} 个参数")

    def create_data_splits(self):
        """创建数据分割 - 修复版本：避免重复分割"""
        print(f"\n🔄 创建K-Fold数据分割...")
        
        # 🔧 修复：直接使用全部数据进行K-Fold，避免重复分割
        full_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='all',  # 使用全部数据
            target_size=(self.img_size, self.img_size), 
            split_ratio=1.0,  # 使用100%数据
            random_seed=42,
            glass_aug_config=self.aug_config  # 启用玻璃增强
        )
        
        print(f"📊 使用全部数据: {len(full_data)}张图像")
        
        # 创建K-Fold分割
        indices = list(range(len(full_data)))
        kfold = KFold(n_splits=self.k_folds, shuffle=True, random_state=42)
        fold_splits = list(kfold.split(indices))
        
        return full_data, fold_splits

    def train_fold(self, fold_idx, full_data, train_indices, val_indices):
        """训练单个fold - 修复版本"""
        print(f"\n🎯 训练 Fold {fold_idx + 1}/{self.k_folds}")
        print(f"   训练样本: {len(train_indices)}, 验证样本: {len(val_indices)}")
        
        clear_cuda_cache()
        
        # 创建数据加载器 - 从全部数据中分割
        train_dataset = Subset(full_data, train_indices)
        val_dataset = Subset(full_data, val_indices)
        
        # 🔧 修复：为训练集添加数据增强
        class AugmentedSubset(Subset):
            def __init__(self, dataset, indices, augment=True):
                super().__init__(dataset, indices)
                self.augment = augment
                self.original_dataset = dataset
                
            def __getitem__(self, idx):
                item = super().__getitem__(idx)
                if self.augment and hasattr(self.original_dataset, 'glass_aug'):
                    image, mask, dt_map = item
                    # 应用玻璃增强
                    image = self.original_dataset.glass_aug(image.permute(1, 2, 0).numpy() * 255)
                    # 转回tensor并重新标准化
                    image = torch.from_numpy(image).float().permute(2, 0, 1) / 255.0
                    # 重新应用ImageNet标准化
                    image = transforms.Normalize(
                        mean=[0.485, 0.456, 0.406],
                        std=[0.229, 0.224, 0.225]
                    )(image)
                    item = (image, mask, dt_map)
                return item
        
        # 训练集使用增强，验证集不使用
        train_dataset = AugmentedSubset(full_data, train_indices, augment=True)
        val_dataset = Subset(full_data, val_indices)  # 验证集不使用增强
        
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.bs, 
            shuffle=True, 
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset, 
            batch_size=self.bs, 
            shuffle=False, 
            num_workers=self.n_worker,
            pin_memory=True
        )
        
        # 创建模型和差分学习率优化器
        model = self.create_model()
        
        # 分离backbone和decoder参数
        backbone_params = []
        decoder_params = []
        for name, param in model.named_parameters():
            if 'backbone' in name:
                backbone_params.append(param)
            else:
                decoder_params.append(param)
        
        optimizer = optim.AdamW([
            {'params': backbone_params, 'lr': self.backbone_lr, 'weight_decay': 1e-4},
            {'params': decoder_params, 'lr': self.decoder_lr, 'weight_decay': 1e-4}
        ])
        
        # 创建warmup调度器
        total_steps = len(train_loader) * self.epochs
        warmup_steps = len(train_loader) * self.warmup_epochs
        
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            else:
                return 0.5 * (1 + np.cos(np.pi * (step - warmup_steps) / (total_steps - warmup_steps)))
        
        scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
        
        # 创建TensorBoard记录
        writer = SummaryWriter(log_dir=f'runs/optimized_fold{fold_idx+1}')
        
        best_iou = 0.0
        iou_history = []
        patience_counter = 0
        global_step = 0
        
        for epoch in range(1, self.epochs + 1):
            model.train()
            running_loss = 0.0
            
            train_tqdm = tqdm(train_loader, desc=f"Fold{fold_idx+1} Epoch{epoch}")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                # 数值稳定性检查
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失: {loss.item()}, 跳过此批次")
                    continue
                
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=self.grad_clip)
                
                optimizer.step()
                scheduler.step()
                
                running_loss += loss.item()
                global_step += 1
                
                # 显示当前学习率
                current_lr_backbone = optimizer.param_groups[0]['lr']
                current_lr_decoder = optimizer.param_groups[1]['lr']
                
                # 计算当前平均损失
                current_avg_loss = running_loss / (batch_idx + 1)
                
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}",
                    avg_loss=f"{current_avg_loss:.4f}",
                    focal=f"{loss_dict['focal_loss']:.4f}",
                    iou=f"{loss_dict['iou_loss']:.4f}",
                    dt=f"{loss_dict['dt_loss']:.4f}",
                    lr_b=f"{current_lr_backbone:.2e}",
                    lr_d=f"{current_lr_decoder:.2e}"
                )
                
            epoch_loss = running_loss / len(train_loader)
            print(f"Fold{fold_idx+1} Epoch{epoch} Avg Loss: {epoch_loss:.6f}")
            
            # 记录训练损失到TensorBoard
            writer.add_scalar('Loss/Train', epoch_loss, epoch)
            
            # 验证 - 提高验证频率
            if epoch % self.val_freq == 0:
                val_loss, val_iou = self.validate(model, val_loader)
                iou_history.append(val_iou)
                
                print(f"Fold{fold_idx+1} Epoch{epoch} Val Loss: {val_loss:.6f}, Val IoU: {val_iou:.4f}")
                
                # 稳定性分析
                if len(iou_history) >= 3:
                    recent_std = np.std(iou_history[-3:])
                    print(f"   最近3轮IoU标准差: {recent_std:.4f}")
                
                # 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    patience_counter = 0
                    self.save_model(fold_idx, epoch, model, optimizer, scheduler, val_iou, is_best=True)
                else:
                    patience_counter += 1
                
                # 早停检查
                if patience_counter >= self.patience:
                    print(f"🛑 早停触发! {self.patience}轮无改善")
                    break
                
                # 记录到TensorBoard
                writer.add_scalar('Loss/Val', val_loss, epoch)
                writer.add_scalar('IoU/Val', val_iou, epoch)
                writer.add_scalar('IoU/Best', best_iou, epoch)
                writer.add_scalar('LR/Backbone', current_lr_backbone, epoch)
                writer.add_scalar('LR/Decoder', current_lr_decoder, epoch)
                
        writer.close()
        
        print(f"🎯 Fold {fold_idx + 1} 完成! 最佳IoU: {best_iou:.4f}")
        
        # 清理内存
        del model, optimizer, scheduler
        clear_cuda_cache()
        
        return best_iou

    def validate(self, model, val_loader):
        """验证函数 - 同时评估main_pred和refined_pred"""
        model.eval()
        running_loss = 0.0
        
        # 全局累积变量
        total_intersection_main = 0.0
        total_union_main = 0.0
        total_intersection_refined = 0.0
        total_union_refined = 0.0
        
        with torch.no_grad():
            for inp_imgs, gt_masks, dt_maps in val_loader:
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                outputs = model(inp_imgs)
                loss, _ = self.criterion(outputs, gt_masks, dt_maps)
                
                running_loss += loss.item()
                
                # 评估main_pred
                main_pred = outputs['main_pred']
                main_bin = (main_pred > 0.5).float()
                intersection_main = (main_bin * gt_masks).sum()
                union_main = main_bin.sum() + gt_masks.sum() - intersection_main
                total_intersection_main += intersection_main.item()
                total_union_main += union_main.item()
                
                # 评估refined_pred（CRF后处理）
                refined_pred = outputs['refined_pred']
                # 确保refined_pred是单通道前景概率
                if refined_pred.size(1) == 2:
                    # 如果是2通道[bg, fg]，选择前景通道
                    refined_pred = refined_pred[:, 1:2, :, :]
                
                # 如果refined_pred无效，回退到main_pred
                if refined_pred.max() - refined_pred.min() < 1e-5 or refined_pred.mean() < 1e-4:
                    refined_pred = main_pred
                
                refined_bin = (refined_pred > 0.5).float()
                intersection_refined = (refined_bin * gt_masks).sum()
                union_refined = refined_bin.sum() + gt_masks.sum() - intersection_refined
                total_intersection_refined += intersection_refined.item()
                total_union_refined += union_refined.item()
        
        val_loss = running_loss / len(val_loader)
        
        # 计算两种IoU
        mean_iou_main = total_intersection_main / (total_union_main + 1e-7)
        mean_iou_refined = total_intersection_refined / (total_union_refined + 1e-7)
        
        # 使用refined_pred的IoU作为主要指标（与测试时一致）
        mean_iou = mean_iou_refined
        
        # 打印CRF效果
        iou_improvement = mean_iou_refined - mean_iou_main
        print(f"   📊 CRF效果: Main({mean_iou_main:.4f}) → Refined({mean_iou_refined:.4f}) [Δ{iou_improvement:+.4f}]")
        
        return val_loss, mean_iou

    def save_model(self, fold_idx, epoch, model, optimizer, scheduler, val_iou, is_best=False):
        """保存模型 - 支持非K-Fold模式"""
        state = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'scheduler': scheduler.state_dict(),
            'epoch': epoch,
            'fold': fold_idx,
            'val_iou': val_iou,
            'config': {
                'backbone_type': self.backbone_type,
                'backbone_lr': self.backbone_lr,
                'decoder_lr': self.decoder_lr,
                'k_folds': self.k_folds,
                'focal_weight': self.focal_weight,
                'iou_weight': self.iou_weight,
                'dt_weight': self.dt_weight,
                'warmup_epochs': self.warmup_epochs,
                'grad_clip': self.grad_clip
            }
        }
        
        if self.k_folds == 0:
            # 非K-Fold模式下的命名
            if is_best:
                filename = f'BEST_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
                print(f"🏆 保存最佳模型: {filename}")
            else:
                filename = f'epoch{epoch:03d}_iou{val_iou:.4f}.pth'
        else:
            # K-Fold模式下的命名
            if is_best:
                filename = f'BEST_fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
                print(f"🏆 保存最佳模型: {filename}")
            else:
                filename = f'fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
        
        save_path = os.path.join(self.model_path, 'weights', filename)
        torch.save(state, save_path)

    def train_kfold(self):
        """执行K-Fold交叉验证或全数据训练"""
        if self.k_folds == 0:
            print(f"\n🎯 使用全部数据进行训练 (9:1拆分)...")
            # 创建全数据集
            full_data = GlassDataLoader(
                data_dir='/home/<USER>/ws/IG_SLAM/',
                split='all',  # 使用全部数据
                target_size=(self.img_size, self.img_size),
                split_ratio=1.0,  # 使用100%数据
                random_seed=42,
                glass_aug_config=self.aug_config
            )
            
            # 使用固定种子进行9:1拆分
            np.random.seed(42)
            indices = np.arange(len(full_data))
            np.random.shuffle(indices)
            
            # 计算训练集大小
            train_size = int(len(full_data) * 0.9)
            train_indices = indices[:train_size]
            val_indices = indices[train_size:]
            
            print(f"📊 数据集拆分:")
            print(f"   总数据: {len(full_data)}张图像")
            print(f"   训练集: {len(train_indices)}张图像 (90%)")
            print(f"   验证集: {len(val_indices)}张图像 (10%)")
            
            # 训练模型
            best_iou = self.train_fold(0, full_data, train_indices, val_indices)
            print(f"\n🎯 训练完成! 最佳IoU: {best_iou:.4f}")
            
        else:
            print(f"\n🎯 开始{self.k_folds}折交叉验证...")
            full_data, fold_splits = self.create_data_splits()
            fold_results = []
            folds_to_train = [self.current_fold] if self.current_fold is not None else range(self.k_folds)
            
            for fold_idx in folds_to_train:
                if fold_idx >= self.k_folds:
                    print(f"❌ Fold {fold_idx} 超出范围")
                    continue
                train_indices, val_indices = fold_splits[fold_idx]
                best_iou = self.train_fold(fold_idx, full_data, train_indices, val_indices)
                fold_results.append(best_iou)
            
            if len(fold_results) > 1:
                mean_iou = np.mean(fold_results)
                std_iou = np.std(fold_results)
                print(f"\n🎯 K-Fold结果汇总:")
                print(f"   平均IoU: {mean_iou:.4f} ± {std_iou:.4f}")
                print(f"   各Fold: {[f'{x:.4f}' for x in fold_results]}")
                print(f"   最佳: {max(fold_results):.4f}")
                print(f"   最差: {min(fold_results):.4f}")
                print(f"   稳定性: {std_iou/mean_iou:.4f}")
            elif len(fold_results) == 1:
                print(f"\n🎯 单Fold结果: {fold_results[0]:.4f}")

def main():
    args = parse_arguments()
    engine = OptimizedKFoldEngine(args)
    engine.train_kfold()

if __name__ == '__main__':
    main() 