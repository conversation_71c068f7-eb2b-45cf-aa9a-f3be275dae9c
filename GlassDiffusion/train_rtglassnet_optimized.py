#!/usr/bin/env python3
"""
🎯 RTGlassNet优化训练脚本
基于IG_SLAM成功经验，修复学习率和损失权重配置问题
目标：从36% IoU提升到80%+ IoU
"""

import os
import warnings

# 抑制各种警告信息
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import math

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet优化训练 - 修复学习率和损失权重')
    
    # 🔧 优化后的基础训练参数
    parser.add_argument('--epochs', default=200, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='批次大小')
    parser.add_argument('--lr', default=0.0005, type=float, help='🔧 优化学习率：从0.002降到0.0005')
    parser.add_argument('--min_lr', default=0.00005, type=float, help='余弦退火最小学习率')
    parser.add_argument('--warmup_epochs', default=5, type=int, help='预热轮数')
    parser.add_argument('--wd', default=0.0005, type=float, help='权重衰减')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--aug', default=True, type=bool, help='数据增强')
    parser.add_argument('--n_worker', default=4, type=int, help='数据加载器工作进程数')
    
    # 训练控制参数
    parser.add_argument('--test_interval', default=5, type=int, help='验证间隔（更频繁验证）')
    parser.add_argument('--save_interval', default=20, type=int, help='保存间隔')
    parser.add_argument('--log_interval', default=100, type=int, help='日志记录间隔')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--use_gpu', default=True, type=bool, help='使用GPU')
    
    # 🎯 优化后的损失权重配置（基于记忆23534的成功经验）
    parser.add_argument('--focal_weight', default=0.3, type=float, help='🔧 优化：Focal损失权重 0.4→0.3')
    parser.add_argument('--iou_weight', default=0.6, type=float, help='🔧 优化：IoU损失权重 0.4→0.6（成为主导）')
    parser.add_argument('--dt_weight', default=0.15, type=float, help='🔧 优化：DT损失权重 0.2→0.15（轻量化）')
    parser.add_argument('--use_focal', default=True, type=bool, help='使用Focal损失')
    
    # 骨干网络参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    
    return parser.parse_args()

class CosineWarmupScheduler:
    """余弦退火 + 预热调度器"""
    def __init__(self, optimizer, warmup_epochs, max_epochs, base_lr, min_lr):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.max_epochs = max_epochs
        self.base_lr = base_lr
        self.min_lr = min_lr
        
    def step(self, epoch):
        if epoch < self.warmup_epochs:
            # 线性预热
            lr = self.base_lr * (epoch + 1) / self.warmup_epochs
        else:
            # 余弦退火
            progress = (epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
            lr = self.min_lr + (self.base_lr - self.min_lr) * 0.5 * (1 + math.cos(math.pi * progress))
        
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr
        
        return lr

class OptimizedEngine:
    def __init__(self, args):
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.min_lr = args.min_lr
        self.warmup_epochs = args.warmup_epochs
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.log_interval = args.log_interval
        self.model_path = args.base_save_path + '/RTGlassNet_Optimized'
        self.use_gpu = args.use_gpu
        self.use_focal = args.use_focal
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        
        # 🎯 优化后的损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))
        self.device = torch.device('cuda' if torch.cuda.is_available() and self.use_gpu else 'cpu')
        
        # 初始化RTGlassNet双分支模型
        self.model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        self._load_backbone_weight()
        
        # 🎯 优化后的王牌组合损失函数
        self.criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=self.use_focal
        )
        
        # 🔧 优化后的优化器配置
        self.optimizer = optim.AdamW(  # 改用AdamW，更好的正则化
            self.model.parameters(), 
            lr=self.lr, 
            weight_decay=self.wd,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 🔧 添加学习率调度器
        self.scheduler = CosineWarmupScheduler(
            self.optimizer, 
            warmup_epochs=self.warmup_epochs,
            max_epochs=self.epochs,
            base_lr=self.lr,
            min_lr=self.min_lr
        )
        
        self.writer_train = SummaryWriter(log_dir='runs/rtglassnet_optimized_train')
        self.writer_val = SummaryWriter(log_dir='runs/rtglassnet_optimized_val')
        
        # 打印优化配置信息
        print(f"🎯 RTGlassNet优化训练配置:")
        print(f"   🔧 学习率优化: {self.lr} (原0.002 → 优化0.0005)")
        print(f"   🔧 损失权重优化: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔧 权重分配: IoU占{self.iou_weight/(self.focal_weight+self.iou_weight+self.dt_weight)*100:.1f}% - 成为绝对主导")
        print(f"   🔧 预热策略: {self.warmup_epochs}轮预热 → 余弦退火")
        print(f"   🔧 优化器: AdamW (更好的正则化)")
        print(f"   🚀 骨干网络: {self.backbone_type}")
        
        # 打印模型参数统计
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"   📊 模型参数: {total_params/1e6:.1f}M总参数, {trainable_params/1e6:.1f}M可训练")

    def _load_backbone_weight(self):
        weight_path = self.backbone_weight
        if weight_path and os.path.exists(weight_path):
            print(f"加载InceptionNeXt权重: {weight_path}")
            state_dict = torch.load(weight_path, map_location=self.device)
            model_backbone = self.model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"已加载 {len(filtered_dict)} 个backbone参数.")
        else:
            print(f"未找到权重文件: {weight_path}，跳过加载。")

    def train(self):
        train_data = GlassDataLoader(data_dir='/home/<USER>/ws/IG_SLAM/', split='train', target_size=(self.img_size, self.img_size), split_ratio=0.9, random_seed=42, glass_aug_config='moderate' if self.aug else None)
        val_data = GlassDataLoader(data_dir='/home/<USER>/ws/IG_SLAM/', split='valid', target_size=(self.img_size, self.img_size), split_ratio=0.9, random_seed=42, glass_aug_config=None)
        train_loader = DataLoader(train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker)
        val_loader = DataLoader(val_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker)
        
        best_iou = 0.0
        patience = 10  # 早停耐心值
        patience_counter = 0
        
        print(f"\n🎯 开始优化训练 - 目标：将IoU从36%提升到80%+")
        print(f"   训练样本: {len(train_data)}, 验证样本: {len(val_data)}")
        print(f"   批次数/轮: {len(train_loader)}")
        
        for epoch in range(1, self.epochs + 1):
            # 🔧 更新学习率
            current_lr = self.scheduler.step(epoch - 1)
            
            self.model.train()
            running_loss = 0.0
            train_tqdm = tqdm(train_loader, desc=f"Epoch {epoch}/{self.epochs} [LR:{current_lr:.6f}]")
            
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                self.optimizer.zero_grad()
                outputs = self.model(inp_imgs)
                
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                loss.backward()
                
                # 🔧 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                running_loss += loss.item()
                
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}", 
                    avg_loss=f"{running_loss/(batch_idx+1):.4f}",
                    focal=f"{loss_dict['focal_loss']:.4f}",
                    iou=f"{loss_dict['iou_loss']:.4f}",
                    dt=f"{loss_dict['dt_loss']:.4f}",
                    lr=f"{current_lr:.6f}"
                )
                
                if batch_idx % self.log_interval == 0:
                    global_step = (epoch - 1) * len(train_loader) + batch_idx
                    self.writer_train.add_scalar('loss/total', loss.item(), global_step)
                    self.writer_train.add_scalar('loss/focal', loss_dict['focal_loss'], global_step)
                    self.writer_train.add_scalar('loss/iou', loss_dict['iou_loss'], global_step)
                    self.writer_train.add_scalar('loss/dt', loss_dict['dt_loss'], global_step)
                    self.writer_train.add_scalar('lr', current_lr, global_step)
                    
            epoch_loss = running_loss / len(train_loader)
            print(f"训练轮次: {epoch} 损失: {epoch_loss:.6f} 学习率: {current_lr:.6f}")
            
            # 验证
            if epoch % self.test_interval == 0:
                val_loss, val_iou = self.validate(val_loader, epoch)
                
                # 🏆 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    patience_counter = 0
                    self.save_model(epoch, is_best=True, val_iou=val_iou)
                    
                    if val_iou > 0.8:  # 达到80%目标
                        print(f"🎉 达到目标IoU 80%+! 当前IoU: {val_iou:.4f}")
                else:
                    patience_counter += 1
                    
                # 记录验证指标
                self.writer_val.add_scalar('loss/total', val_loss, epoch)
                self.writer_val.add_scalar('metrics/iou', val_iou, epoch)
                self.writer_val.add_scalar('metrics/best_iou', best_iou, epoch)
                
                # 早停检查
                if patience_counter >= patience:
                    print(f"🛑 早停触发: {patience}轮无改善, 最佳IoU: {best_iou:.4f}")
                    break
                    
            # 定期保存
            if self.save_interval and epoch % self.save_interval == 0:
                self.save_model(epoch, is_best=False, val_iou=val_iou if 'val_iou' in locals() else 0.0)
                
        self.writer_train.close()
        self.writer_val.close()
        
        print(f"\n🎯 训练完成!")
        print(f"   最佳IoU: {best_iou:.4f}")
        if best_iou > 0.8:
            print(f"   🎉 成功达到80%+目标!")
        else:
            print(f"   📈 相比初始36%，提升了{(best_iou-0.36)*100:.1f}个百分点")

    def validate(self, val_loader, epoch):
        self.model.eval()
        running_loss = 0.0
        iou_list = []
        dt_quality_list = []
        
        with torch.no_grad():
            val_tqdm = tqdm(val_loader, desc="验证中...")
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(val_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                outputs = self.model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                running_loss += loss.item()
                
                # 主预测IoU计算
                main_pred = outputs['main_pred']
                pred_bin = (main_pred > 0.5).float()
                intersection = (pred_bin * gt_masks).sum(dim=(1,2,3))
                union = pred_bin.sum(dim=(1,2,3)) + gt_masks.sum(dim=(1,2,3)) - intersection
                iou = (intersection / (union + 1e-7)).mean().item()
                iou_list.append(iou)
                
                # DT预测质量评估
                dt_pred = outputs['dt_pred']
                dt_mae = F.l1_loss(dt_pred, dt_maps).item()
                dt_quality_list.append(1.0 - dt_mae)
                
        val_loss = running_loss / len(val_loader)
        mean_iou = sum(iou_list) / len(iou_list)
        mean_dt_quality = sum(dt_quality_list) / len(dt_quality_list)
        
        print(f'🎯 验证结果 :: IoU: {mean_iou:.4f} | DT质量: {mean_dt_quality:.4f} | 损失: {val_loss:.4f}')
        
        # 显示改善情况
        if hasattr(self, '_last_iou'):
            improvement = mean_iou - self._last_iou
            print(f'   📈 IoU变化: {improvement:+.4f} ({improvement*100:+.2f}%)')
        self._last_iou = mean_iou
        
        return val_loss, mean_iou

    def save_model(self, epoch, is_best=False, val_iou=0.0):
        """保存优化后的模型"""
        state = {
            'model': self.model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'epoch': epoch,
            'val_iou': val_iou,
            'backbone_type': self.backbone_type,
            'model_info': {
                'backbone': self.backbone_type,
                'architecture': 'RTGlassNet_Optimized',
                'features': ['InceptionNeXt', 'FPN', 'LightEdgeEnhancer', 'SCSA', 'DT_Prediction', 'CRF'],
                'optimization': 'LR_WeightDecay_LossWeights_Scheduler'
            }
        }
        
        if is_best:
            save_path = os.path.join(self.model_path, 'weights', f'🎯OPTIMIZED_BEST_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"🏆 🎯优化版最佳模型🎯 已保存: epoch {epoch}, IoU {val_iou:.4f}")
            print(f"   优化特征: 学习率0.0005 + IoU主导(60%) + 预热策略")
        else:
            save_path = os.path.join(self.model_path, 'weights', f'optimized_checkpoint_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"📦 优化检查点已保存: epoch {epoch}, IoU {val_iou:.4f}")

def main():
    args = parse_arguments()
    engine = OptimizedEngine(args)
    engine.train()

if __name__ == '__main__':
    main() 