"""
 @Time    : 2024
 <AUTHOR> <PERSON><PERSON>

 @Project : IG_SLAM
 @File    : gdnet_scsa.py
 @Function: Lightweight GDNet with SCSA for real-time glass detection

"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

from ig_glass.backbone.resnext.resnext101_regular import ResNeXt101
from ig_glass.diff_crf import SimplifiedDiffCRF
from ig_glass.attention_scsa import SCSA  # 使用本地实现的SCSA模块


###################################################################
# ########################## LightLCFI #############################
###################################################################
class LightLCFI(nn.Module):
    """Lightweight Large Context Feature Integration module with depthwise separable convolutions"""
    def __init__(self, input_channels, dr1=1, dr2=2, dr3=3, dr4=4):
        super(LightLCFI, self).__init__()
        self.input_channels = input_channels
        self.channels_single = int(input_channels / 4)
        self.channels_double = int(input_channels / 2)
        self.dr1 = dr1
        self.dr2 = dr2
        self.dr3 = dr3
        self.dr4 = dr4
        self.padding1 = 1 * dr1
        self.padding2 = 2 * dr2
        self.padding3 = 3 * dr3
        self.padding4 = 4 * dr4

        # Channel reduction with 1x1 convolutions
        self.p1_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p1
        self.p1_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (3, 1), 1, padding=(self.padding1, 0),
                      dilation=(self.dr1, 1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_single, self.channels_single, (1, 3), 1, padding=(0, self.padding1),
                      dilation=(1, self.dr1), groups=self.channels_single),
            # Pointwise
            nn.Conv2d(self.channels_single, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p1_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p2
        self.p2_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (5, 1), 1, padding=(self.padding2, 0),
                      dilation=(self.dr2, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 5), 1, padding=(0, self.padding2),
                      dilation=(1, self.dr2), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p2_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p3
        self.p3_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (7, 1), 1, padding=(self.padding3, 0),
                      dilation=(self.dr3, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 7), 1, padding=(0, self.padding3),
                      dilation=(1, self.dr3), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p3_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Depthwise separable convolutions for p4
        self.p4_d1 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (9, 1), 1, padding=(self.padding4, 0),
                      dilation=(self.dr4, 1), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_d2 = nn.Sequential(
            # Depthwise
            nn.Conv2d(self.channels_double, self.channels_double, (1, 9), 1, padding=(0, self.padding4),
                      dilation=(1, self.dr4), groups=self.channels_double),
            # Pointwise
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())
        self.p4_fusion = nn.Sequential(
            nn.Conv2d(self.channels_double, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single), nn.ReLU())

        # Use SCSA for attention instead of CBAM
        self.scsa = SCSA(
            dim=self.input_channels,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )

        # Final channel reduction
        self.channel_reduction = nn.Sequential(
            nn.Conv2d(self.input_channels, self.channels_single, 1, 1, 0),
            nn.BatchNorm2d(self.channels_single),
            nn.ReLU())

    def forward(self, x):
        p1_input = self.p1_channel_reduction(x)
        p1 = self.p1_fusion(torch.cat((self.p1_d1(p1_input), self.p1_d2(p1_input)), 1))

        p2_input = torch.cat((self.p2_channel_reduction(x), p1), 1)
        p2 = self.p2_fusion(torch.cat((self.p2_d1(p2_input), self.p2_d2(p2_input)), 1))

        p3_input = torch.cat((self.p3_channel_reduction(x), p2), 1)
        p3 = self.p3_fusion(torch.cat((self.p3_d1(p3_input), self.p3_d2(p3_input)), 1))

        p4_input = torch.cat((self.p4_channel_reduction(x), p3), 1)
        p4 = self.p4_fusion(torch.cat((self.p4_d1(p4_input), self.p4_d2(p4_input)), 1))

        # 使用SCSA处理特征
        combined_features = torch.cat((p1, p2, p3, p4), 1)
        attended_features = self.scsa(combined_features)
        channel_reduction = self.channel_reduction(attended_features)

        return channel_reduction


###################################################################
# ###################### LightEdgeEnhancer #######################
###################################################################
class LightEdgeEnhancer(nn.Module):
    """Lightweight module to enhance edge features"""
    def __init__(self, in_channels):
        super(LightEdgeEnhancer, self).__init__()
        # Depthwise separable convolution
        self.edge_conv = nn.Sequential(
            # Depthwise
            nn.Conv2d(in_channels, in_channels, kernel_size=3, padding=1, groups=in_channels),
            # Pointwise
            nn.Conv2d(in_channels, in_channels, kernel_size=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # Laplacian kernel for edge detection
        self.register_buffer('laplacian', torch.tensor([
            [-1, -1, -1],
            [-1,  8, -1],
            [-1, -1, -1]
        ]).float().view(1, 1, 3, 3))
        
    def forward(self, x):
        # Apply edge-aware enhancement
        edge_features = F.conv2d(x.mean(dim=1, keepdim=True), self.laplacian, padding=1)
        edge_features = torch.sigmoid(edge_features)
        
        # Apply edge-aware convolution
        enhanced = self.edge_conv(x)
        
        # Combine with original features with edge attention
        return x + enhanced * edge_features


###################################################################
# ########################## NETWORK ##############################
###################################################################
class GDNetSCSA(nn.Module):
    """Lightweight GDNet with SCSA attention and simplified CRF for real-time application"""
    def __init__(self, backbone='resnext101', backbone_path=None, crf_iter=3, trainable_crf=True):
        super(GDNetSCSA, self).__init__()
        # params

        # backbone
        if backbone == 'resnext101':
            resnext = ResNeXt101(backbone_path)
            self.layer0 = resnext.layer0
            self.layer1 = resnext.layer1
            self.layer2 = resnext.layer2
            self.layer3 = resnext.layer3
            self.layer4 = resnext.layer4
        elif backbone == 'resnet50':
            resnet = models.resnet50(pretrained=False) # 不再从网上下载
            if backbone_path:
                resnet.load_state_dict(torch.load(backbone_path))
            self.layer0 = nn.Sequential(resnet.conv1, resnet.bn1, resnet.relu, resnet.maxpool)
            self.layer1 = resnet.layer1
            self.layer2 = resnet.layer2
            self.layer3 = resnet.layer3
            self.layer4 = resnet.layer4
        elif backbone == 'resnext50':
            resnext50 = models.resnext50_32x4d(pretrained=False) # 不再从网上下载
            if backbone_path:
                resnext50.load_state_dict(torch.load(backbone_path))
            self.layer0 = nn.Sequential(resnext50.conv1, resnext50.bn1, resnext50.relu, resnext50.maxpool)
            self.layer1 = resnext50.layer1
            self.layer2 = resnext50.layer2
            self.layer3 = resnext50.layer3
            self.layer4 = resnext50.layer4
        else:
            raise ValueError(f"Backbone '{backbone}' not supported.")

        # Use lightweight LCFI modules
        self.h5_conv = LightLCFI(2048, 1, 2, 3, 4)
        self.h4_conv = LightLCFI(1024, 1, 2, 3, 4)
        self.h3_conv = LightLCFI(512, 1, 2, 3, 4)
        self.l2_conv = LightLCFI(256, 1, 2, 3, 4)

        # h fusion
        self.h5_up = nn.UpsamplingBilinear2d(scale_factor=2)
        self.h3_down = nn.AvgPool2d((2, 2), stride=2)
        
        # 使用SCSA替代CBAM
        self.h_fusion = SCSA(
            dim=896,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.h_fusion_conv = nn.Sequential(
            # Depthwise separable conv
            nn.Conv2d(896, 896, 3, 1, 1, groups=896),
            nn.Conv2d(896, 896, 1, 1, 0),
            nn.BatchNorm2d(896), nn.ReLU())

        # l fusion
        self.l_fusion_conv = nn.Sequential(
            # Depthwise separable conv
            nn.Conv2d(64, 64, 3, 1, 1, groups=64),
            nn.Conv2d(64, 64, 1, 1, 0),
            nn.BatchNorm2d(64), nn.ReLU())
        self.h2l = nn.ConvTranspose2d(896, 1, 8, 4, 2)

        # Edge enhancer
        self.edge_enhancer = LightEdgeEnhancer(896)

        # final fusion
        self.h_up_for_final_fusion = nn.ConvTranspose2d(896, 256, 8, 4, 2)
        
        # 使用SCSA替代CBAM
        self.final_fusion = SCSA(
            dim=320,
            head_num=8,
            window_size=7,
            group_kernel_sizes=[3, 5, 7, 9],
            qkv_bias=False,
            gate_layer='sigmoid'
        )
        
        self.final_fusion_conv = nn.Sequential(
            # Depthwise separable conv
            nn.Conv2d(320, 320, 3, 1, 1, groups=320),
            nn.Conv2d(320, 320, 1, 1, 0),
            nn.BatchNorm2d(320), nn.ReLU())

        # predict conv
        self.h_predict = nn.Conv2d(896, 1, 3, 1, 1)
        self.l_predict = nn.Conv2d(64, 1, 3, 1, 1)
        self.final_predict = nn.Conv2d(320, 1, 3, 1, 1)

        # CRF layer with parameters from grid search [1.5, 40, 3, 5, 10]
        # Reduced iterations for real-time performance
        self.crf = SimplifiedDiffCRF(
            n_iter=crf_iter,
            bilateral_weight=10.0,  # From compat=10
            gaussian_weight=5.0,     # From compat=5
            bilateral_spatial_sigma=40.0,  # From sxy=40
            bilateral_color_sigma=3.0,     # From srgb=3
            gaussian_sigma=1.5,            # From sxy=1.5
            trainable=trainable_crf
        )

        # Class balancing weights for handling imbalanced data
        self.class_weights = nn.Parameter(torch.tensor([1.0, 1.5]), requires_grad=True)

        for m in self.modules():
            if isinstance(m, nn.ReLU):
                m.inplace = True

    def forward(self, x):
        # x: [batch_size, channel=3, h, w]
        layer0 = self.layer0(x)  # [-1, 64, h/2, w/2]
        layer1 = self.layer1(layer0)  # [-1, 256, h/4, w/4]
        layer2 = self.layer2(layer1)  # [-1, 512, h/8, w/8]
        layer3 = self.layer3(layer2)  # [-1, 1024, h/16, w/16]
        layer4 = self.layer4(layer3)  # [-1, 2048, h/32, w/32]

        h5_conv = self.h5_conv(layer4)
        h4_conv = self.h4_conv(layer3)
        h3_conv = self.h3_conv(layer2)
        l2_conv = self.l2_conv(layer1)

        # h fusion
        h5_up = self.h5_up(h5_conv)
        h3_down = self.h3_down(h3_conv)
        h_fusion_input = torch.cat((h5_up, h4_conv, h3_down), 1)
        h_fusion = self.h_fusion(h_fusion_input)
        h_fusion = self.h_fusion_conv(h_fusion)
        
        # Apply edge enhancement
        h_fusion = self.edge_enhancer(h_fusion)

        # l fusion
        l_fusion = self.l_fusion_conv(l2_conv)
        h2l = self.h2l(h_fusion)
        l_fusion = F.sigmoid(h2l) * l_fusion

        # final fusion
        h_up_for_final_fusion = self.h_up_for_final_fusion(h_fusion)
        final_fusion_input = torch.cat((h_up_for_final_fusion, l_fusion), 1)
        final_fusion = self.final_fusion(final_fusion_input)
        final_fusion = self.final_fusion_conv(final_fusion)

        # h predict
        h_predict = self.h_predict(h_fusion)

        # l predict
        l_predict = self.l_predict(l_fusion)

        # final predict
        final_predict = self.final_predict(final_fusion)

        # rescale to original size
        h_predict = F.interpolate(h_predict, size=x.size()[2:], mode='bilinear', align_corners=True)
        l_predict = F.interpolate(l_predict, size=x.size()[2:], mode='bilinear', align_corners=True)
        final_predict = F.interpolate(final_predict, size=x.size()[2:], mode='bilinear', align_corners=True)

        # Apply sigmoid to get probabilities
        h_predict_prob = torch.sigmoid(h_predict)
        l_predict_prob = torch.sigmoid(l_predict)
        final_predict_prob = torch.sigmoid(final_predict)

        # Convert final prediction to 2-channel logits for CRF
        # Ensure predictions are in valid range
        final_predict_prob = torch.clamp(final_predict_prob, min=1e-7, max=1.0 - 1e-7)

        # Check for NaN or Inf values
        if torch.isnan(final_predict_prob).any() or torch.isinf(final_predict_prob).any():
            print("警告: 最终预测中检测到NaN或Inf值")
            final_predict_prob = torch.nan_to_num(final_predict_prob, nan=0.5, posinf=1.0, neginf=0.0)

        # Create logits for background and foreground with class weighting
        bg_logits = torch.log((1 - final_predict_prob) * self.class_weights[0])
        fg_logits = torch.log(final_predict_prob * self.class_weights[1])
        combined_logits = torch.cat([bg_logits, fg_logits], dim=1)

        # Normalize input image for CRF
        # The input image should be in [0,1] range for the bilateral filter
        normalized_img = x.clone()
        if normalized_img.max() > 1.0 or normalized_img.min() < 0.0:
            # If the image is not in [0,1] range, normalize it
            # Assuming the image is in the range used by the ImageNet normalization
            mean = torch.tensor([0.485, 0.456, 0.406], device=x.device).view(1, 3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225], device=x.device).view(1, 3, 1, 1)
            normalized_img = normalized_img * std + mean
            normalized_img = torch.clamp(normalized_img, 0.0, 1.0)

        # Apply CRF refinement
        try:
            refined_predict = self.crf(combined_logits, normalized_img)
            # Check if refined_predict has valid shape
            if refined_predict.size(1) == 0:
                print("警告: CRF返回了通道数为0的张量。创建备用输出。")
                # Create a dummy tensor with 2 channels (bg, fg)
                refined_predict = torch.cat([
                    1 - final_predict_prob,  # background
                    final_predict_prob       # foreground
                ], dim=1)
        except Exception as e:
            print(f"CRF错误: {e}")
            # Create a fallback prediction
            refined_predict = torch.cat([
                1 - final_predict_prob,  # background
                final_predict_prob       # foreground
            ], dim=1)

        return h_predict_prob, l_predict_prob, final_predict_prob, refined_predict 