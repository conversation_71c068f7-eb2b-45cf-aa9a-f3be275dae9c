# RTGlassNetv2 最终性能测试脚本 (V-Final-Strict)
# - 严格、逐行地复刻 test_scsa.py 的测试逻辑和流程。
# - 确保使用完全相同的计时方法、IO流程、预处理和后处理步骤。
# - 目标：获得与gdnet_scsa在同等测试标准下的FPS结果。

import os
import cv2
import numpy as np
import torch
from tqdm import tqdm
import sys

# 确保可以从父目录导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ig_glass.misc import compute_iou, compute_acc, compute_precision_recall, compute_fmeasure, compute_mae, compute_ber, compute_aber
from GlassDiffusion.models.RTGlassNetv2 import RTGlassNetv2

def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, args, device):
    """复刻自test_scsa.py的评估函数"""
    os.makedirs(output_folder, exist_ok=True)

    image_files = sorted([f for f in os.listdir(image_folder) if f.endswith(('.jpg', '.png'))])
    total_files = len(image_files)
    print(f"在 {total_files} 张图像上进行测试...")

    metrics = {'iou': 0, 'acc': 0, 'fm': 0, 'mae': 0, 'ber': 0, 'aber': 0}
    count = 0
    inference_times = []

    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True

    progress = tqdm(image_files, desc="正在评估")
    for image_file in progress:
        image_path = os.path.join(image_folder, image_file)
        
        img = cv2.imread(image_path)
        if img is None: continue
        orig_h, orig_w = img.shape[:2]
        img_resized = cv2.resize(img, (args['scale'], args['scale']), interpolation=cv2.INTER_AREA)
        img_rgb = cv2.cvtColor(img_resized, cv2.COLOR_BGR2RGB)
        
        img_tensor = torch.from_numpy(img_rgb.transpose((2, 0, 1))).float().div(255.0)
        mean = torch.tensor([0.485, 0.456, 0.406], device=device).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225], device=device).view(3, 1, 1)
        img_tensor = (img_tensor.to(device) - mean) / std
        img_var = img_tensor.unsqueeze(0)

        start_time = torch.cuda.Event(enable_timing=True)
        end_time = torch.cuda.Event(enable_timing=True)
        
        with torch.no_grad():
            if count == 0: # 预热
                for _ in range(10):
                    _ = model(img_var)
            
            torch.cuda.synchronize()
            start_time.record()
            
            predictions = model(img_var)
            refined_pred = predictions['refined_pred']

            end_time.record()
            torch.cuda.synchronize()
            inference_times.append(start_time.elapsed_time(end_time))

            pred_prob = torch.nn.functional.interpolate(refined_pred, size=(orig_h, orig_w), mode='bilinear', align_corners=False)
            pred_binary = (pred_prob.squeeze() > args['threshold']).cpu().numpy().astype(np.uint8)

        cv2.imwrite(os.path.join(output_folder, os.path.splitext(image_file)[0] + '.png'), pred_binary * 255)
        
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        if os.path.exists(gt_path):
            gt_mask = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            if gt_mask is not None:
                gt_binary = (gt_mask > 127).astype(np.float32)
                pred_binary_float = pred_binary.astype(np.float32)
                metrics['iou'] += compute_iou(pred_binary, gt_binary.astype(np.uint8))
                metrics['acc'] += compute_acc(pred_binary, gt_binary.astype(np.uint8))
                precision, recall = compute_precision_recall(pred_binary_float, gt_binary)
                metrics['fm'] += compute_fmeasure(precision, recall)
                metrics['mae'] += compute_mae(pred_binary_float, gt_binary)
                metrics['ber'] += compute_ber(pred_binary, gt_binary.astype(np.uint8))
                metrics['aber'] += compute_aber(pred_binary, gt_binary.astype(np.uint8))
                count += 1

    if count > 0:
        for key in metrics:
            metrics[key] /= count
    
    stable_times_ms = inference_times[5:] if len(inference_times) > 5 else inference_times
    avg_inference_ms = np.mean(stable_times_ms)
    fps = 1000.0 / avg_inference_ms

    return metrics, avg_inference_ms, fps

def main():
    args = {
        'scale': 416,
        'threshold': 0.5,
        'backbone': 'inceptionnext_base'
    }
    MODEL_PATH = './ckpt/RTGlassNetv2/weights/BEST_epoch235_iou0.9686.pth'
    DATA_DIR = '/home/<USER>/Documents/ig_slam_maskdata/test_GDD'
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"--- 运行在: {torch.cuda.get_device_name(device)} ---")

    model = RTGlassNetv2(backbone_type=args['backbone']).to(device)
    
    # 加载checkpoint并提取model权重
    checkpoint = torch.load(MODEL_PATH, map_location=device)
    if isinstance(checkpoint, dict) and 'model' in checkpoint:
        state_dict = checkpoint['model']
        print(f"从checkpoint中提取模型权重 (epoch: {checkpoint.get('epoch', 'N/A')}, IoU: {checkpoint.get('val_iou', 'N/A'):.4f})")
    else:
        state_dict = checkpoint
        print("直接加载state_dict")
    
    model.load_state_dict(state_dict)
    model.eval()
    print(f"✅ 成功加载模型: {os.path.basename(MODEL_PATH)}")

    image_folder = os.path.join(DATA_DIR, "image")
    gt_folder = os.path.join(DATA_DIR, "mask")
    output_folder = os.path.join(os.path.dirname(DATA_DIR), "rtglassnetv2_final_output_strict")

    metrics, avg_inference_ms, fps = detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, args, device)

    print("\n" + "="*60)
    print("            RTGlassNetv2 最终性能评估 (严格复刻版)")
    print("="*60)
    print(f"  测试模型: {os.path.basename(MODEL_PATH)}")
    print(f"  测试数据集: {os.path.basename(DATA_DIR)}")
    print("\n  🎯 精度指标 (使用CRF)")
    print(f"    平均 IoU: {metrics['iou']:.4f}")
    print(f"    F-measure: {metrics['fm']:.4f}")
    print(f"    MAE:       {metrics['mae']:.4f}")
    print("\n⚡ 性能指标 (严格复刻 test_scsa.py 的测试流程)")
    print(f"    纯净推理时间: {avg_inference_ms:.2f} ms")
    print(f"    纯净推理 FPS: {fps:.2f}")
    print("="*60)

if __name__ == '__main__':
    main()