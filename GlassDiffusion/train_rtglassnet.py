import os
import warnings

# 抑制各种警告信息
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'  # 禁用albumentations更新检查
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')  # 忽略timm警告
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')  # 忽略模型注册冲突

import argparse
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F

def parse_arguments():
    parser = argparse.ArgumentParser(description='🌟 RTGlassNet王牌组合训练参数')
    
    # 基础训练参数
    parser.add_argument('--epochs', default=200, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='批次大小')
    parser.add_argument('--lr', default=0.002, type=float, help='学习率')
    parser.add_argument('--wd', default=0.0005, type=float, help='权重衰减')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--aug', default=True, type=bool, help='数据增强')
    parser.add_argument('--n_worker', default=4, type=int, help='数据加载器工作进程数')
    
    # 训练控制参数
    parser.add_argument('--test_interval', default=10, type=int, help='验证间隔')
    parser.add_argument('--save_interval', default=20, type=int, help='保存间隔')
    parser.add_argument('--log_interval', default=250, type=int, help='日志记录间隔')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--use_gpu', default=True, type=bool, help='使用GPU')
    
    # 🌟 王牌组合损失权重配置
    parser.add_argument('--focal_weight', default=0.4, type=float, help='主预测Focal损失权重')
    parser.add_argument('--iou_weight', default=0.4, type=float, help='IoU损失权重')
    parser.add_argument('--dt_weight', default=0.2, type=float, help='🌟 DT损失权重 - 王牌组合核心')
    parser.add_argument('--use_focal', default=True, type=bool, help='使用Focal损失')
    
    # 骨干网络参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    
    
    return parser.parse_args()

class Engine:
    def __init__(self, args):
        self.epochs = args.epochs
        self.bs = args.bs
        self.lr = args.lr
        self.wd = args.wd
        self.img_size = args.img_size
        self.aug = args.aug
        self.n_worker = args.n_worker
        self.test_interval = args.test_interval
        self.save_interval = args.save_interval
        self.log_interval = args.log_interval
        self.model_path = args.base_save_path + '/RTGlassNet'
        self.use_gpu = args.use_gpu
        self.use_focal = args.use_focal
        self.backbone_type = args.backbone_type
        
        # 🌟 王牌组合损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        if not os.path.exists(os.path.join(self.model_path, 'weights')):
            os.makedirs(os.path.join(self.model_path, 'weights'))
        if not os.path.exists(os.path.join(self.model_path, 'optimizers')):
            os.makedirs(os.path.join(self.model_path, 'optimizers'))
        self.device = torch.device('cuda' if torch.cuda.is_available() and self.use_gpu else 'cpu')
        
        # 🌟 初始化RTGlassNet双分支模型
        self.model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        self._load_backbone_weight()
        
        # 🌟 初始化王牌组合损失函数
        self.criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=self.use_focal
        )
        
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.lr, weight_decay=self.wd)
        self.writer_train = SummaryWriter(log_dir='runs/rtglassnet_train')
        self.writer_val = SummaryWriter(log_dir='runs/rtglassnet_valid')
        
        # 打印王牌组合信息
        print(f"🌟 RTGlassNet王牌组合已初始化!")
        print(f"   🎯 损失权重配置: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔥 核心特征: LightEdgeEnhancer + Distance Transform Loss")
        print(f"   🚀 骨干网络: {self.backbone_type}")
        
        # 打印模型参数统计
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"   📊 模型参数: {total_params/1e6:.1f}M总参数, {trainable_params/1e6:.1f}M可训练")

    def _load_backbone_weight(self):
        # 根据backbone_type自动推断权重路径
        weight_path = f'./inceptionnext/{self.backbone_type}.pth'
        if os.path.exists(weight_path):
            print(f"自动加载 {self.backbone_type} 的预训练权重: {weight_path}")
            state_dict = torch.load(weight_path, map_location=self.device)
            model_backbone = self.model.feature_extractor.backbone
            model_dict = model_backbone.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items() if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model_backbone.load_state_dict(model_dict, strict=False)
            print(f"成功加载 {len(filtered_dict)} 个 {self.backbone_type} 的参数.")
        else:
            print(f"未找到预训练权重: {weight_path}。将使用随机初始化的 {self.backbone_type} 骨干网络。")

    def train(self):
        train_data = GlassDataLoader(data_dir='/home/<USER>/ws/IG_SLAM/', split='train', target_size=(self.img_size, self.img_size), split_ratio=0.9, random_seed=42, glass_aug_config='moderate' if self.aug else None)
        val_data = GlassDataLoader(data_dir='/home/<USER>/ws/IG_SLAM/', split='valid', target_size=(self.img_size, self.img_size), split_ratio=0.9, random_seed=42, glass_aug_config=None)
        train_loader = DataLoader(train_data, batch_size=self.bs, shuffle=True, num_workers=self.n_worker)
        val_loader = DataLoader(val_data, batch_size=self.bs, shuffle=False, num_workers=self.n_worker)
        best_iou = 0.0
        for epoch in range(1, self.epochs + 1):
            self.model.train()
            running_loss = 0.0
            train_tqdm = tqdm(train_loader, desc=f"Epoch {epoch}/{self.epochs}")
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                self.optimizer.zero_grad()
                outputs = self.model(inp_imgs)
                
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)
                
                loss.backward()
                self.optimizer.step()
                running_loss += loss.item()
                
                train_tqdm.set_postfix(
                    loss=loss.item(), 
                    avg_loss=running_loss/(batch_idx+1),
                    focal=loss_dict['focal_loss'],
                    iou=loss_dict['iou_loss'],
                    dt=loss_dict['dt_loss']
                )
                
                if batch_idx % self.log_interval == 0:
                    global_step = (epoch - 1) * len(train_loader) + batch_idx
                    self.writer_train.add_scalar('loss/total', loss.item(), global_step)
                    self.writer_train.add_scalar('loss/focal', loss_dict['focal_loss'], global_step)
                    self.writer_train.add_scalar('loss/iou', loss_dict['iou_loss'], global_step)
                    self.writer_train.add_scalar('loss/dt', loss_dict['dt_loss'], global_step)
            epoch_loss = running_loss / len(train_loader)
            print(f"训练轮次: {epoch} 损失: {epoch_loss:.6f}")
            if epoch % self.test_interval == 0:
                val_loss, val_iou = self.validate(val_loader, epoch)
                if val_iou > best_iou:
                    best_iou = val_iou
                    self.save_model(epoch, is_best=True, val_iou=val_iou)
            if self.save_interval and epoch % self.save_interval == 0:
                self.save_model(epoch, is_best=False, val_iou=best_iou)
        self.writer_train.close()
        self.writer_val.close()

    def validate(self, val_loader, epoch):
        self.model.eval()
        running_loss = 0.0
        iou_list = []
        dt_iou_list = []  # 添加DT IoU统计
        
        with torch.no_grad():
            val_tqdm = tqdm(val_loader, desc="验证")
            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(val_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)  # 🌟 使用dt_maps
                
                outputs = self.model(inp_imgs)  # 返回字典
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)  # 🌟 王牌损失
                
                running_loss += loss.item()
                
                # 主预测IoU计算
                main_pred = outputs['main_pred']  # 已经sigmoid过的预测
                pred_bin = (main_pred > 0.5).float()
                intersection = (pred_bin * gt_masks).sum(dim=(1,2,3))
                union = pred_bin.sum(dim=(1,2,3)) + gt_masks.sum(dim=(1,2,3)) - intersection
                iou = (intersection / (union + 1e-7)).mean().item()
                iou_list.append(iou)
                
                # DT预测质量评估
                dt_pred = outputs['dt_pred']
                dt_mae = F.l1_loss(dt_pred, dt_maps).item()
                dt_iou_list.append(1.0 - dt_mae)  # 将MAE转换为质量指标
                
                # 可视化前几个样本
                if batch_idx == 0 and epoch % 10 == 0:
                    for i in range(min(4, inp_imgs.size(0))):
                        self.writer_val.add_image(f'input/{i}', inp_imgs[i], epoch)
                        self.writer_val.add_image(f'gt_mask/{i}', gt_masks[i], epoch)
                        self.writer_val.add_image(f'gt_dt/{i}', dt_maps[i], epoch)
                        self.writer_val.add_image(f'pred_main/{i}', main_pred[i], epoch)
                        self.writer_val.add_image(f'pred_dt/{i}', dt_pred[i], epoch)
                        self.writer_val.add_image(f'pred_refined/{i}', outputs['refined_pred'][i], epoch)
                        
        val_loss = running_loss / len(val_loader)
        mean_iou = sum(iou_list) / len(iou_list)
        mean_dt_quality = sum(dt_iou_list) / len(dt_iou_list)
        
        print(f'验证 :: 主IoU: {mean_iou:.4f} | DT质量: {mean_dt_quality:.4f} | 损失: {val_loss:.4f}')
        
        # 记录详细指标
        self.writer_val.add_scalar('loss/total', val_loss, epoch)
        self.writer_val.add_scalar('metrics/main_iou', mean_iou, epoch)
        self.writer_val.add_scalar('metrics/dt_quality', mean_dt_quality, epoch)
        
        return val_loss, mean_iou

    def save_model(self, epoch, is_best=False, val_iou=0.0):
        """
        保存模型 - 支持双分支RTGlassNet
        """
        state = {
            'model': self.model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'epoch': epoch,
            'val_iou': val_iou,
            'backbone_type': self.backbone_type,
            'model_info': {
                'backbone': self.backbone_type,
                'architecture': 'RTGlassNet_DualBranch',
                'features': ['InceptionNeXt', 'FPN', 'LightEdgeEnhancer', 'SCSA', 'DT_Prediction', 'CRF']
            }
        }
        
        if is_best:
            save_path = os.path.join(self.model_path, 'weights', f'🌟BEST_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"🏆 🌟王牌组合🌟 最佳模型已保存: epoch {epoch}, IoU {val_iou:.4f}")
            print(f"   特征: LightEdgeEnhancer + DT Loss 协同优化")
        else:
            save_path = os.path.join(self.model_path, 'weights', f'checkpoint_epoch-{epoch:03d}_iou-{val_iou:.4f}.pth')
            torch.save(state, save_path)
            print(f"📦 检查点已保存: epoch {epoch}, IoU {val_iou:.4f}")

def main():
    args = parse_arguments()
    engine = Engine(args)
    engine.train()

if __name__ == '__main__':
    main() 