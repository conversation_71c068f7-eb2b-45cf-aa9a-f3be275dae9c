#!/usr/bin/env python3
"""
🎯 RTGlassNet K-Fold改进版训练脚本
修正了发现的主要问题，目标突破90 IoU
"""

import os
import warnings
import numpy as np
import random
from sklearn.model_selection import KFold

# 抑制警告
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, Subset
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from GlassDiffusion.models.loss_rtglassnet import CompositeLoss
from GlassDiffusion.glass_dataloader import GlassDataLoader
import torch.nn.functional as F
import json
import gc
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR, SequentialLR

def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"🎲 随机种子设置为: {seed}")

def clear_cuda_cache():
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def create_differential_optimizer(model, backbone_lr=1e-5, decoder_lr=3e-4, weight_decay=1e-4):
    """创建差分学习率优化器"""
    backbone_params = []
    decoder_params = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            if 'backbone' in name or 'feature_extractor' in name:
                backbone_params.append(param)
            else:
                decoder_params.append(param)
    
    param_groups = [
        {'params': backbone_params, 'lr': backbone_lr, 'weight_decay': weight_decay},
        {'params': decoder_params, 'lr': decoder_lr, 'weight_decay': weight_decay}
    ]
    
    optimizer = optim.AdamW(param_groups, betas=(0.9, 0.999), eps=1e-8)
    
    print(f"🔧 差分学习率优化器:")
    print(f"   - Backbone参数: {len(backbone_params)}, LR: {backbone_lr}")
    print(f"   - Decoder参数: {len(decoder_params)}, LR: {decoder_lr}")
    
    return optimizer

def create_scheduler(optimizer, epochs, warmup_epochs=10):
    """创建带warmup的余弦退火调度器"""
    warmup_scheduler = LinearLR(optimizer, start_factor=0.1, total_iters=warmup_epochs)
    cosine_scheduler = CosineAnnealingLR(optimizer, T_max=epochs-warmup_epochs, eta_min=1e-7)
    scheduler = SequentialLR(optimizer, [warmup_scheduler, cosine_scheduler], [warmup_epochs])
    return scheduler

def parse_arguments():
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet K-Fold改进版训练')
    
    # 基础参数
    parser.add_argument('--epochs', default=100, type=int, help='训练轮数')
    parser.add_argument('--bs', default=6, type=int, help='批次大小')
    parser.add_argument('--backbone_lr', default=1e-5, type=float, help='backbone学习率')
    parser.add_argument('--decoder_lr', default=3e-4, type=float, help='decoder学习率')
    parser.add_argument('--img_size', default=416, type=int, help='图像尺寸')
    parser.add_argument('--n_worker', default=2, type=int, help='工作进程数')
    parser.add_argument('--warmup_epochs', default=10, type=int, help='warmup轮数')
    
    # K-Fold参数
    parser.add_argument('--k_folds', default=5, type=int, help='K-Fold交叉验证的K值')
    parser.add_argument('--current_fold', default=None, type=int, help='当前训练的fold')
    
    # 改进的损失权重
    parser.add_argument('--focal_weight', default=0.3, type=float, help='Focal损失权重')
    parser.add_argument('--iou_weight', default=0.5, type=float, help='IoU损失权重')
    parser.add_argument('--dt_weight', default=0.2, type=float, help='DT损失权重')
    
    # 其他参数
    parser.add_argument('--backbone_type', default='inceptionnext_base', type=str, help='骨干网络类型')
    parser.add_argument('--backbone_weight', default='./inceptionnext/inceptionnext_base.pth', type=str, help='预训练权重路径')
    parser.add_argument('--base_save_path', default='./ckpt', type=str, help='模型保存路径')
    parser.add_argument('--val_interval', default=2, type=int, help='验证间隔')
    parser.add_argument('--glass_aug', default='medium', type=str, help='玻璃增强强度')
    
    return parser.parse_args()

class ImprovedKFoldEngine:
    def __init__(self, args):
        set_seed(42)
        clear_cuda_cache()
        
        self.epochs = args.epochs
        self.bs = args.bs
        self.backbone_lr = args.backbone_lr
        self.decoder_lr = args.decoder_lr
        self.img_size = args.img_size
        self.n_worker = args.n_worker
        self.warmup_epochs = args.warmup_epochs
        self.k_folds = args.k_folds
        self.current_fold = args.current_fold
        self.val_interval = args.val_interval
        self.glass_aug = args.glass_aug
        
        # 改进的损失权重
        self.focal_weight = args.focal_weight
        self.iou_weight = args.iou_weight
        self.dt_weight = args.dt_weight
        
        # 其他配置
        self.backbone_type = args.backbone_type
        self.backbone_weight = args.backbone_weight
        self.model_path = args.base_save_path + '/RTGlassNet_Improved'
        
        # 创建保存目录
        os.makedirs(os.path.join(self.model_path, 'weights'), exist_ok=True)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化损失函数
        self.criterion = CompositeLoss(
            focal_weight=self.focal_weight,
            iou_weight=self.iou_weight, 
            dt_weight=self.dt_weight,
            use_focal=True
        )
        
        print(f"🎯 RTGlassNet 改进版训练配置:")
        print(f"   🔧 数据分割: 80%训练+20%验证")
        print(f"   🔧 K-Fold: {self.k_folds}折")
        print(f"   🔧 批次大小: {self.bs}")
        print(f"   🔧 图像尺寸: {self.img_size}x{self.img_size}")
        print(f"   🔧 Backbone学习率: {self.backbone_lr}")
        print(f"   🔧 Decoder学习率: {self.decoder_lr}")
        print(f"   🔧 Warmup轮数: {self.warmup_epochs}")
        print(f"   🔧 损失权重: Focal({self.focal_weight}) + IoU({self.iou_weight}) + DT({self.dt_weight})")
        print(f"   🔧 玻璃增强: {self.glass_aug}")
        print(f"   🔧 验证间隔: 每{self.val_interval}轮")

    def create_model(self):
        """创建模型"""
        model = RTGlassNet(backbone_type=self.backbone_type).to(self.device)
        self._load_backbone_weight(model)
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"   📊 模型参数: {total_params/1e6:.1f}M (可训练: {trainable_params/1e6:.1f}M)")
        
        return model

    def _load_backbone_weight(self, model):
        """改进的权重加载"""
        if self.backbone_weight and os.path.exists(self.backbone_weight):
            print(f"🔄 加载权重: {self.backbone_weight}")
            try:
                state_dict = torch.load(self.backbone_weight, map_location=self.device)
                model_backbone = model.feature_extractor.backbone
                model_dict = model_backbone.state_dict()
                
                # 过滤匹配的权重
                filtered_dict = {}
                for k, v in state_dict.items():
                    if k in model_dict and v.shape == model_dict[k].shape:
                        filtered_dict[k] = v
                
                model_dict.update(filtered_dict)
                model_backbone.load_state_dict(model_dict, strict=False)
                print(f"✅ 成功加载 {len(filtered_dict)}/{len(model_dict)} 个参数")
                
                # 验证权重加载
                loaded_ratio = len(filtered_dict) / len(model_dict)
                if loaded_ratio < 0.5:
                    print(f"⚠️ 权重加载比例较低: {loaded_ratio:.2%}")
                    
            except Exception as e:
                print(f"❌ 权重加载失败: {e}")
        else:
            print(f"⚠️ 权重文件不存在: {self.backbone_weight}")

    def create_data_splits(self):
        """创建数据分割"""
        print(f"\n🔄 创建数据分割...")
        
        # 创建完整数据集 - 使用改进的增强
        full_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='train',
            target_size=(self.img_size, self.img_size), 
            split_ratio=0.8,
            random_seed=42,
            glass_aug_config=self.glass_aug  # 使用更强的增强
        )
        
        # 创建验证数据集
        val_data = GlassDataLoader(
            data_dir='/home/<USER>/ws/IG_SLAM/', 
            split='valid',
            target_size=(self.img_size, self.img_size), 
            split_ratio=0.8,
            random_seed=42,
            glass_aug_config=None  # 验证不增强
        )
        
        print(f"📊 数据分割: 训练{len(full_data)}，验证{len(val_data)}")
        
        # 创建K-Fold分割
        indices = list(range(len(full_data)))
        kfold = KFold(n_splits=self.k_folds, shuffle=True, random_state=42)
        fold_splits = list(kfold.split(indices))
        
        return full_data, val_data, fold_splits

    def validate(self, model, val_loader):
        """改进的验证函数"""
        model.eval()
        running_loss = 0.0
        iou_list = []
        
        with torch.no_grad():
            for inp_imgs, gt_masks, dt_maps in val_loader:
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)
                
                outputs = model(inp_imgs)
                loss, _ = self.criterion(outputs, gt_masks, dt_maps)
                
                # 数值稳定性检查
                if torch.isnan(loss) or torch.isinf(loss):
                    print("⚠️ 验证中检测到异常损失")
                    continue
                    
                running_loss += loss.item()
                
                # 正确的IoU计算 - 逐样本计算
                main_pred = outputs['main_pred']
                pred_bin = (main_pred > 0.5).float()
                
                batch_size = pred_bin.shape[0]
                for i in range(batch_size):
                    intersection = (pred_bin[i] * gt_masks[i]).sum()
                    union = pred_bin[i].sum() + gt_masks[i].sum() - intersection
                    iou = intersection / (union + 1e-7)
                    iou_list.append(iou.item())
        
        val_loss = running_loss / len(val_loader) if len(val_loader) > 0 else 0.0
        mean_iou = sum(iou_list) / len(iou_list) if len(iou_list) > 0 else 0.0
        
        return val_loss, mean_iou

    def train_fold(self, fold_idx, full_data, val_data, train_indices, val_indices):
        """改进的fold训练"""
        print(f"\n🎯 训练 Fold {fold_idx + 1}/{self.k_folds}")
        print(f"   训练样本: {len(train_indices)}, 验证样本: {len(val_indices)}")

        clear_cuda_cache()

        # 创建模型和优化器
        model = self.create_model()
        optimizer = create_differential_optimizer(
            model,
            backbone_lr=self.backbone_lr,
            decoder_lr=self.decoder_lr
        )
        scheduler = create_scheduler(optimizer, self.epochs, self.warmup_epochs)

        # 创建数据加载器
        train_dataset = Subset(full_data, train_indices)
        val_dataset = Subset(full_data, val_indices)

        train_loader = DataLoader(
            train_dataset,
            batch_size=self.bs,
            shuffle=True,
            num_workers=self.n_worker,
            pin_memory=True,
            drop_last=True
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=self.bs,
            shuffle=False,
            num_workers=self.n_worker,
            pin_memory=True
        )

        # 创建TensorBoard记录
        writer = SummaryWriter(log_dir=f'runs/improved_fold{fold_idx+1}')

        best_iou = 0.0
        patience_counter = 0
        patience = 15  # 早停patience

        for epoch in range(1, self.epochs + 1):
            model.train()
            running_loss = 0.0

            train_tqdm = tqdm(train_loader, desc=f"Fold{fold_idx+1} Epoch{epoch}")

            for batch_idx, (inp_imgs, gt_masks, dt_maps) in enumerate(train_tqdm):
                inp_imgs = inp_imgs.to(self.device)
                gt_masks = gt_masks.to(self.device)
                dt_maps = dt_maps.to(self.device)

                optimizer.zero_grad()
                outputs = model(inp_imgs)
                loss, loss_dict = self.criterion(outputs, gt_masks, dt_maps)

                # 数值稳定性检查
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️ 检测到异常损失，跳过批次")
                    continue

                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)

                optimizer.step()
                running_loss += loss.item()

                # 更新进度条
                train_tqdm.set_postfix(
                    loss=f"{loss.item():.4f}",
                    focal=f"{loss_dict['focal_loss']:.4f}",
                    iou=f"{loss_dict['iou_loss']:.4f}",
                    dt=f"{loss_dict['dt_loss']:.4f}",
                    lr=f"{optimizer.param_groups[0]['lr']:.6f}"
                )

            # 更新学习率
            scheduler.step()

            epoch_loss = running_loss / len(train_loader)
            current_lr = optimizer.param_groups[0]['lr']
            print(f"Fold{fold_idx+1} Epoch{epoch} Loss: {epoch_loss:.6f} LR: {current_lr:.6f}")

            # 验证
            if epoch % self.val_interval == 0:
                val_loss, val_iou = self.validate(model, val_loader)

                print(f"Fold{fold_idx+1} Epoch{epoch} Val IoU: {val_iou:.4f}")

                # 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    patience_counter = 0
                    self.save_model(fold_idx, epoch, model, optimizer, val_iou, is_best=True)

                    if val_iou > 0.9:  # 达到90%目标
                        print(f"🎉 达到目标IoU 90%+! 当前IoU: {val_iou:.4f}")
                else:
                    patience_counter += 1

                # 记录到TensorBoard
                writer.add_scalar('Loss/Val', val_loss, epoch)
                writer.add_scalar('IoU/Val', val_iou, epoch)
                writer.add_scalar('IoU/Best', best_iou, epoch)
                writer.add_scalar('LR/Backbone', optimizer.param_groups[0]['lr'], epoch)
                writer.add_scalar('LR/Decoder', optimizer.param_groups[1]['lr'], epoch)

                # 早停检查
                if patience_counter >= patience:
                    print(f"🛑 早停触发: {patience}轮无改善, 最佳IoU: {best_iou:.4f}")
                    break

        writer.close()

        print(f"🎯 Fold {fold_idx + 1} 完成! 最佳IoU: {best_iou:.4f}")

        # 清理内存
        del model, optimizer, scheduler
        clear_cuda_cache()

        return best_iou

    def save_model(self, fold_idx, epoch, model, optimizer, val_iou, is_best=False):
        """保存模型"""
        state = {
            'model': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'epoch': epoch,
            'fold': fold_idx,
            'val_iou': val_iou,
            'config': {
                'backbone_type': self.backbone_type,
                'k_folds': self.k_folds,
                'focal_weight': self.focal_weight,
                'iou_weight': self.iou_weight,
                'dt_weight': self.dt_weight,
                'backbone_lr': self.backbone_lr,
                'decoder_lr': self.decoder_lr
            }
        }

        if is_best:
            filename = f'BEST_improved_fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'
            print(f"🏆 保存最佳模型: {filename}")
        else:
            filename = f'improved_fold{fold_idx+1}_epoch{epoch:03d}_iou{val_iou:.4f}.pth'

        save_path = os.path.join(self.model_path, 'weights', filename)
        torch.save(state, save_path)

    def train_kfold(self):
        """执行改进的K-Fold交叉验证"""
        print(f"\n🎯 开始改进版K-Fold交叉验证...")

        # 创建数据分割
        full_data, val_data, fold_splits = self.create_data_splits()

        fold_results = []

        # 训练每个fold
        folds_to_train = [self.current_fold] if self.current_fold is not None else range(self.k_folds)

        for fold_idx in folds_to_train:
            if fold_idx >= self.k_folds:
                print(f"❌ Fold {fold_idx} 超出范围")
                continue

            train_indices, val_indices = fold_splits[fold_idx]
            best_iou = self.train_fold(fold_idx, full_data, val_data, train_indices, val_indices)
            fold_results.append(best_iou)

        # 汇总结果
        if len(fold_results) > 1:
            mean_iou = np.mean(fold_results)
            std_iou = np.std(fold_results)

            print(f"\n🎯 改进版K-Fold结果汇总:")
            print(f"   平均IoU: {mean_iou:.4f} ± {std_iou:.4f}")
            print(f"   各Fold: {[f'{x:.4f}' for x in fold_results]}")
            print(f"   最佳: {max(fold_results):.4f}")
            print(f"   最差: {min(fold_results):.4f}")
            print(f"   稳定性: {std_iou/mean_iou:.4f}")

            # 保存结果
            summary = {
                'k_folds': self.k_folds,
                'fold_results': fold_results,
                'mean_iou': mean_iou,
                'std_iou': std_iou,
                'stability': std_iou/mean_iou,
                'improvements': {
                    'differential_lr': True,
                    'improved_augmentation': self.glass_aug,
                    'balanced_loss_weights': True,
                    'gradient_clipping': True,
                    'early_stopping': True
                }
            }

            with open(os.path.join(self.model_path, 'improved_kfold_summary.json'), 'w') as f:
                json.dump(summary, f, indent=2)

        elif len(fold_results) == 1:
            print(f"\n🎯 单Fold结果: {fold_results[0]:.4f}")

def main():
    args = parse_arguments()
    engine = ImprovedKFoldEngine(args)
    engine.train_kfold()

if __name__ == '__main__':
    main()
