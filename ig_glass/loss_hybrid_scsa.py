"""
 @Time    : 2024
 <AUTHOR> Glennine
 @Project : IG_SLAM
 @File    : loss_hybrid_scsa.py
 @Function: 混合SCSA模型专用损失函数

针对GDNetHybridSCSA模型的特殊输出结构设计：
1. 处理物理特性增强的多输出
2. 物理一致性损失
3. 轻量化物理特性的专门优化
4. 与增强SCSA损失的差异化设计

目标：在SCSA基础上通过轻量化物理特性突破90% IoU
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pytorch_msssim import ssim

class PhysicsConsistencyLoss(nn.Module):
    """
    物理一致性损失 - 专门针对轻量化物理特性
    只保留透明度特性的一致性
    """
    def __init__(self, transparency_weight=1.0):
        super(PhysicsConsistencyLoss, self).__init__()
        self.transparency_weight = transparency_weight
    
    def forward(self, physics_maps, target):
        """
        Args:
            physics_maps: 物理响应图字典
            target: 真值标签 [B, 1, H, W]
        """
        total_loss = 0.0
        
        # 透明度一致性损失
        if 'h_transparency' in physics_maps and 'l_transparency' in physics_maps:
            h_transparency = physics_maps['h_transparency']
            l_transparency = physics_maps['l_transparency']
            
            # 调整尺寸到相同
            if h_transparency.shape != l_transparency.shape:
                h_transparency = F.interpolate(h_transparency, size=l_transparency.shape[2:], 
                                             mode='bilinear', align_corners=True)
            
            # 透明度一致性，使用MSE损失（更稳定的数值范围）
            transparency_consistency_loss = F.mse_loss(h_transparency, l_transparency)
            # 为了增强一致性约束，添加L1损失
            transparency_consistency_loss += 0.5 * F.l1_loss(h_transparency, l_transparency)
            total_loss += self.transparency_weight * transparency_consistency_loss
        
        # 如果没有透明度特性，返回0损失
        if total_loss == 0.0:
            return torch.tensor(0.0, device=target.device, requires_grad=True)

        return total_loss


class LightweightPhysicsLoss(nn.Module):
    """
    轻量化物理特性损失
    专门针对透明度特性的优化
    """
    def __init__(self, transparency_weight=1.0):
        super(LightweightPhysicsLoss, self).__init__()
        self.transparency_weight = transparency_weight
    
    def forward(self, physics_maps, target):
        """
        Args:
            physics_maps: 物理响应图字典
            target: 真值标签 [B, 1, H, W]
        """
        total_loss = 0.0
        
        # 透明度损失 - 玻璃区域透明度高，所以目标与玻璃标签一致
        # 如果target中玻璃区域为1，透明度目标也应该为1（高透明度）
        transparency_target = target  # 玻璃区域透明度高，与target保持一致
        
        transparency_losses = []
        for key in ['h_transparency', 'l_transparency']:
            if key in physics_maps:
                pred_transparency = physics_maps[key]
                
                # 调整尺寸
                if pred_transparency.shape != transparency_target.shape:
                    pred_transparency = F.interpolate(pred_transparency, size=transparency_target.shape[2:], 
                                                    mode='bilinear', align_corners=True)
                
                # MSE损失用于透明度回归
                transparency_loss = F.mse_loss(pred_transparency, transparency_target)
                transparency_losses.append(transparency_loss)
        
        if transparency_losses:
            total_loss += self.transparency_weight * torch.mean(torch.stack(transparency_losses))
        
        # 如果没有透明度特性，返回0损失
        if total_loss == 0.0:
            return torch.tensor(0.0, device=target.device, requires_grad=True)
        
        return total_loss


class HybridSCSALoss(nn.Module):
    """
    混合SCSA模型专用损失函数
    结合SCSA优势和轻量化物理特性
    包含细粒度深度监督（Deep Supervision）
    """
    def __init__(self, 
                 focal_weight=0.6,  # 回退到SCSA成功配置
                 iou_weight=0.4,    # 回退到SCSA成功配置
                 physics_weight=0.2,  # 回退到边界损失权重
                 consistency_weight=0.1,  # 保持较低
                 deep_supervision_h_weight=0.3,  # 回退到SCSA配置
                 deep_supervision_l_weight=0.3,  # 回退到SCSA配置
                 adaptive=False):  # 默认关闭自适应权重，使用固定权重更稳定
        super(HybridSCSALoss, self).__init__()
        
        # 基础损失函数
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
        self.iou_loss = IoULoss(smooth=1e-6)
        
        # 物理特性损失 - 只保留透明度损失
        self.physics_loss = LightweightPhysicsLoss(transparency_weight=1.0)
        self.consistency_loss = PhysicsConsistencyLoss(transparency_weight=1.0)
        
        # 细粒度深度监督权重
        self.deep_supervision_h_weight = deep_supervision_h_weight
        self.deep_supervision_l_weight = deep_supervision_l_weight
        
        # 权重管理
        if adaptive:
            initial_weights = [focal_weight, iou_weight, physics_weight, consistency_weight, 
                             deep_supervision_h_weight, deep_supervision_l_weight]
            self.weight_manager = AdaptiveWeightManager(initial_weights)
        else:
            self.weights = [focal_weight, iou_weight, physics_weight, consistency_weight,
                          deep_supervision_h_weight, deep_supervision_l_weight]
            self.weight_manager = None
        
        self.adaptive = adaptive
    
    def forward(self, predictions, targets, performance_metrics=None):
        """
        Args:
            predictions: GDNetHybridSCSA的输出字典
            targets: 真值标签 [B, 1, H, W]
            performance_metrics: 性能指标字典 (可选)
        """
        pred_h = predictions['pred_h']
        pred_l = predictions['pred_l']
        ensemble_pred = predictions['ensemble_pred']
        physics_maps = predictions.get('physics_maps', {})
        
        # 主要损失 - 对最终ensemble预测的监督（核心优化目标）
        focal_loss_main = self.focal_loss(ensemble_pred, targets)
        iou_loss_main = self.iou_loss(ensemble_pred, targets)
        
        # 细粒度深度监督损失 - 对中间层输出的独立监督
        focal_loss_h = self.focal_loss(pred_h, targets)
        iou_loss_h = self.iou_loss(pred_h, targets)
        focal_loss_l = self.focal_loss(pred_l, targets)
        iou_loss_l = self.iou_loss(pred_l, targets)
        
        # 分别计算高层和低层的深度监督损失
        deep_supervision_h_loss = (focal_loss_h + iou_loss_h) / 2.0
        deep_supervision_l_loss = (focal_loss_l + iou_loss_l) / 2.0
        
        # 物理特性损失（辅助正则化）
        physics_loss = self.physics_loss(physics_maps, targets)
        consistency_loss = self.consistency_loss(physics_maps, targets)
        
        losses = [focal_loss_main, iou_loss_main, physics_loss, consistency_loss,
                 deep_supervision_h_loss, deep_supervision_l_loss]
        
        # 权重管理
        if self.adaptive and self.weight_manager is not None:
            total_loss, current_weights = self.weight_manager(losses, performance_metrics)
        else:
            current_weights = self.weights
            total_loss = sum(w * loss for w, loss in zip(current_weights, losses))
        
        return {
            'total_loss': total_loss,
            'focal_loss': focal_loss_main,
            'iou_loss': iou_loss_main,
            'physics_loss': physics_loss,
            'consistency_loss': consistency_loss,
            'deep_supervision_h_loss': deep_supervision_h_loss,
            'deep_supervision_l_loss': deep_supervision_l_loss,
            'deep_supervision_loss': (deep_supervision_h_loss + deep_supervision_l_loss) / 2.0,  # 平均值，便于兼容
            'deep_supervision_focal': (focal_loss_h + focal_loss_l) / 2.0,
            'deep_supervision_iou': (iou_loss_h + iou_loss_l) / 2.0,
            'pred_h_focal': focal_loss_h,
            'pred_h_iou': iou_loss_h,
            'pred_l_focal': focal_loss_l,
            'pred_l_iou': iou_loss_l,
            'weights': current_weights
        }


class FocalLoss(nn.Module):
    """Focal Loss实现"""
    def __init__(self, alpha=0.25, gamma=2.0):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, pred, target):
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        pt = torch.where(target == 1, pred, 1 - pred)
        alpha_t = torch.where(target == 1, self.alpha, 1 - self.alpha)
        focal_weight = alpha_t * (1 - pt) ** self.gamma
        
        return torch.mean(focal_weight * bce_loss)


class IoULoss(nn.Module):
    """IoU Loss实现"""
    def __init__(self, smooth=1e-6):
        super(IoULoss, self).__init__()
        self.smooth = smooth
    
    def forward(self, pred, target):
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        intersection = torch.sum(pred * target, dim=(2, 3))
        union = torch.sum(pred, dim=(2, 3)) + torch.sum(target, dim=(2, 3)) - intersection
        
        iou = (intersection + self.smooth) / (union + self.smooth)
        return 1 - torch.mean(iou)


class AdaptiveWeightManager(nn.Module):
    """自适应权重管理器"""
    def __init__(self, initial_weights, adaptation_rate=0.01):
        super(AdaptiveWeightManager, self).__init__()
        self.weights = nn.Parameter(torch.tensor(initial_weights, dtype=torch.float32))
        self.adaptation_rate = adaptation_rate
        self.step_count = 0
    
    def forward(self, losses, performance_metrics=None):
        self.step_count += 1
        
        # 归一化权重
        normalized_weights = F.softmax(self.weights, dim=0)
        
        # 计算加权损失
        total_loss = sum(w * loss for w, loss in zip(normalized_weights, losses))
        
        # 自适应调整 (每100步调整一次)
        if performance_metrics is not None and self.step_count % 100 == 0:
            self._adapt_weights(performance_metrics)
        
        return total_loss, normalized_weights.detach().cpu().numpy()
    
    def _adapt_weights(self, metrics):
        """根据性能指标调整权重"""
        with torch.no_grad():
            iou = metrics.get('iou', 0.5)
            
            # 如果IoU较低，增加IoU损失权重
            if iou < 0.88:
                self.weights[1] += self.adaptation_rate  # IoU损失权重
            
            # 如果物理特性效果不好，调整物理损失权重
            edge_quality = metrics.get('edge_f1', 0.5)
            if edge_quality < 0.8:
                self.weights[2] += self.adaptation_rate  # 物理损失权重


def create_hybrid_scsa_loss(focal_weight=0.6, iou_weight=0.4, physics_weight=0.2, 
                           consistency_weight=0.1, deep_supervision_h_weight=0.3, deep_supervision_l_weight=0.3, adaptive=False):
    """
    创建混合SCSA损失函数 - 回退到SCSA成功配置
    
    🔧 权重配置说明（回退到原始SCSA成功配置）：
    - 主任务权重：focal(0.6) + iou(0.4) = 1.0 (62.5%)
    - 深度监督权重：h(0.3) + l(0.3) = 0.6 (37.5%)  
    - 物理特性权重：physics(0.2) + consistency(0.1) = 0.3 (18.8%)
    - 总权重：1.6，平衡的权重分配
    
    回退策略：
    1. Focal权重(0.6)：主要分类损失，与原始SCSA一致
    2. IoU权重(0.4)：形状优化损失，与原始SCSA一致
    3. 深度监督权重(0.6)：保持SCSA的多尺度监督
    4. 物理特性权重(0.3)：轻量化物理增强，替代边界损失
    """
    return HybridSCSALoss(
        focal_weight=focal_weight,
        iou_weight=iou_weight,
        physics_weight=physics_weight,
        consistency_weight=consistency_weight,
        deep_supervision_h_weight=deep_supervision_h_weight,
        deep_supervision_l_weight=deep_supervision_l_weight,
        adaptive=adaptive
    )


# 使用示例
if __name__ == "__main__":
    # 测试混合SCSA损失函数
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    batch_size = 2
    height, width = 416, 416
    
    # 模拟GDNetHybridSCSA的输出
    predictions = {
        'pred_h': torch.sigmoid(torch.randn(batch_size, 1, height, width, requires_grad=True)).to(device),
        'pred_l': torch.sigmoid(torch.randn(batch_size, 1, height, width, requires_grad=True)).to(device),
        'ensemble_pred': torch.sigmoid(torch.randn(batch_size, 1, height, width, requires_grad=True)).to(device),
        'physics_maps': {
            'h_transparency': torch.sigmoid(torch.randn(batch_size, 1, height, width, requires_grad=True)).to(device),
            'l_transparency': torch.sigmoid(torch.randn(batch_size, 1, height, width, requires_grad=True)).to(device)
        }
    }
    
    targets = torch.randint(0, 2, (batch_size, 1, height, width)).float().to(device)
    
    # 创建损失函数（回退到SCSA成功配置）
    criterion = create_hybrid_scsa_loss(
        focal_weight=0.6,
        iou_weight=0.4,
        physics_weight=0.2,
        consistency_weight=0.1,
        deep_supervision_h_weight=0.3,
        deep_supervision_l_weight=0.3,
        adaptive=False
    ).to(device)
    
    # 计算损失
    loss_dict = criterion(predictions, targets)
    
    print("✅ 混合SCSA损失函数测试成功（回退到SCSA成功配置）")
    print(f"   总损失: {loss_dict['total_loss'].item():.4f}")
    print(f"   主Focal损失: {loss_dict['focal_loss'].item():.4f} (权重: 0.6)")
    print(f"   主IoU损失: {loss_dict['iou_loss'].item():.4f} (权重: 0.4)")
    print(f"   透明度物理损失: {loss_dict['physics_loss'].item():.4f} (权重: 0.2)")
    print(f"   透明度一致性损失: {loss_dict['consistency_loss'].item():.4f} (权重: 0.1)")
    print(f"   深度监督H损失: {loss_dict['deep_supervision_h_loss'].item():.4f} (权重: 0.3)")
    print(f"   深度监督L损失: {loss_dict['deep_supervision_l_loss'].item():.4f} (权重: 0.3)")
    print(f"   - pred_h focal: {loss_dict['pred_h_focal'].item():.4f}")
    print(f"   - pred_h iou: {loss_dict['pred_h_iou'].item():.4f}")
    print(f"   - pred_l focal: {loss_dict['pred_l_focal'].item():.4f}")
    print(f"   - pred_l iou: {loss_dict['pred_l_iou'].item():.4f}")
    print(f"   权重分配: {loss_dict['weights']}")
    
    # 权重总和验证
    total_weight = sum(loss_dict['weights'])
    print(f"   权重总和: {total_weight:.2f}")
    print(f"   主要任务占比: {(loss_dict['weights'][0] + loss_dict['weights'][1])/total_weight*100:.1f}%")
    print(f"   深度监督占比: {(loss_dict['weights'][4] + loss_dict['weights'][5])/total_weight*100:.1f}%")
    print(f"   透明度物理特性占比: {(loss_dict['weights'][2] + loss_dict['weights'][3])/total_weight*100:.1f}%")
    print(f"   💡 注意: 回退到SCSA成功配置，物理特性作为轻量化增强")
    
    # 测试反向传播
    loss_dict['total_loss'].backward()
    print("✅ 反向传播测试成功")


