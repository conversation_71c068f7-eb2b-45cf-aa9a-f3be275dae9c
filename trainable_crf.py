"""
端到端可训练的CRF层实现
解决了数值稳定性问题，支持自动学习参数，无需手动调参。

核心改进：
1. 参数约束：学习对数值而非原始值，确保sigma等参数始终为正
2. 消息控制：使用tanh约束消息传递幅度，防止梯度爆炸
3. 组合损失：结合Focal Loss和Dice Loss，提供高质量梯度信号
4. 健壮设计：专为端到端训练设计，避免数值不稳定性

作者：基于原始CRF理论重构，专注于深度学习训练稳定性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class TrainableCRF(nn.Module):
    """
    一个经过加固的、真正为端到端训练设计的可学习CRF层。
    通过学习参数的对数值和控制消息幅度来确保训练的稳定性。
    
    核心创新：
    - 对数空间参数学习：保证sigma等参数始终为正
    - 消息幅度控制：使用tanh约束，防止数值爆炸
    - 可学习缩放因子：自适应调整消息传递强度
    """
    
    def __init__(self,
                 n_iter: int = 5,
                 initial_bilateral_weight: float = 10.0,
                 initial_gaussian_weight: float = 3.0,
                 initial_bilateral_spatial_sigma: float = 40.0,
                 initial_bilateral_color_sigma: float = 5.0,
                 initial_gaussian_sigma: float = 3.0):
        """
        初始化可训练的CRF模块。

        Args:
            n_iter (int): 平均场迭代次数，通常3-5次即可
            initial_bilateral_weight (float): 双边滤波权重初始值
            initial_gaussian_weight (float): 高斯滤波权重初始值
            initial_bilateral_spatial_sigma (float): 双边滤波空间sigma初始值
            initial_bilateral_color_sigma (float): 双边滤波颜色sigma初始值
            initial_gaussian_sigma (float): 高斯滤波sigma初始值
        """
        super().__init__()
        self.n_iter = n_iter

        # === 核心创新：学习对数值而非原始值 ===
        # 这确保了exp(log_param)始终为正，解决数值稳定性问题
        
        # 1. 学习权重的对数值，保证权重为正
        self.log_bilateral_weight = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_weight, dtype=torch.float32))
        )
        self.log_gaussian_weight = nn.Parameter(
            torch.log(torch.tensor(initial_gaussian_weight, dtype=torch.float32))
        )

        # 2. 学习sigma的对数值，这是最关键的稳定技巧
        self.log_bilateral_spatial_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_spatial_sigma, dtype=torch.float32))
        )
        self.log_bilateral_color_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_bilateral_color_sigma, dtype=torch.float32))
        )
        self.log_gaussian_sigma = nn.Parameter(
            torch.log(torch.tensor(initial_gaussian_sigma, dtype=torch.float32))
        )
        
        # 3. 学习消息缩放因子，控制消息传递的整体强度
        self.message_scaler = nn.Parameter(torch.tensor(1.0, dtype=torch.float32))

        # 4. 可选：学习迭代衰减因子，让后续迭代的影响逐渐减小
        self.iteration_decay = nn.Parameter(torch.tensor(0.9, dtype=torch.float32))

    def _gaussian_filter(self, x: torch.Tensor, sigma: torch.Tensor) -> torch.Tensor:
        """
        稳定的高斯滤波实现
        
        Args:
            x: 输入特征图 (B, C, H, W)
            sigma: 高斯核标准差（从对数空间恢复的正值）
            
        Returns:
            torch.Tensor: 高斯滤波后的特征图
        """
        # sigma是从exp(log_sigma)计算得来，所以一定是正数
        sigma_val = torch.clamp(sigma, min=0.5, max=50.0).item()  # 合理范围约束
        
        # 动态计算核大小
        kernel_size = int(2 * sigma_val + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1
        kernel_size = min(kernel_size, 15)  # 限制最大核大小，避免计算过慢
        
        padding = kernel_size // 2
        grid = torch.arange(-padding, padding + 1, dtype=torch.float32, device=x.device)
        
        # 计算高斯核，使用clamp防止sigma过小导致数值问题
        gaussian_1d = torch.exp(-0.5 * (grid / torch.clamp(sigma, min=1e-6)).pow(2))
        gaussian_1d = gaussian_1d / torch.clamp(gaussian_1d.sum(), min=1e-6)

        # 构建2D高斯核
        gaussian_2d = gaussian_1d.view(1, 1, -1, 1) * gaussian_1d.view(1, 1, 1, -1)
        gaussian_2d = gaussian_2d.expand(x.size(1), 1, kernel_size, kernel_size)

        # 分组卷积，每个通道独立滤波
        return F.conv2d(x, gaussian_2d, padding=padding, groups=x.size(1))

    def _bilateral_approximation(self, 
                                x: torch.Tensor, 
                                guide: torch.Tensor, 
                                spatial_sigma: torch.Tensor, 
                                color_sigma: torch.Tensor) -> torch.Tensor:
        """
        双边滤波的简化近似实现
        
        这里使用简化版本，在保持效果的同时确保计算效率和数值稳定性
        
        Args:
            x: 输入特征图 (B, C, H, W)
            guide: 引导图像，通常是原始RGB图像 (B, 3, H, W)
            spatial_sigma: 空间sigma
            color_sigma: 颜色sigma
            
        Returns:
            torch.Tensor: 双边滤波后的特征图
        """
        # 1. 空间滤波（使用高斯滤波近似）
        blurred_q = self._gaussian_filter(x, spatial_sigma)
        
        # 2. 颜色相似性权重计算
        # 计算每个像素与其邻域平均颜色的差异
        guide_mean = F.avg_pool2d(guide, kernel_size=3, stride=1, padding=1)
        guide_diff = guide - guide_mean
        guide_diff_sq = guide_diff.pow(2).sum(dim=1, keepdim=True)
        
        # 颜色权重：越相似的像素权重越高
        color_sigma_sq = 2 * torch.clamp(color_sigma, min=1e-6).pow(2)
        color_weights = torch.exp(-guide_diff_sq / color_sigma_sq)
        
        # 应用颜色权重
        return blurred_q * color_weights

    def forward(self, unary: torch.Tensor, img: torch.Tensor) -> torch.Tensor:
        """
        CRF前向传播
        
        Args:
            unary: 分割网络的原始输出logits (B, C, H, W)
            img: 原始RGB输入图像, 值域[0, 1] (B, 3, H, W)

        Returns:
            torch.Tensor: 精炼后的logits (B, C, H, W)
        """
        # 确保输入在同一设备上
        if unary.device != img.device:
            img = img.to(unary.device)
        
        # === 核心创新：从对数空间恢复参数，确保数值稳定性 ===
        bilateral_weight = torch.exp(torch.clamp(self.log_bilateral_weight, min=-5, max=5))
        gaussian_weight = torch.exp(torch.clamp(self.log_gaussian_weight, min=-5, max=5))
        bilateral_spatial_sigma = torch.exp(torch.clamp(self.log_bilateral_spatial_sigma, min=-3, max=4))
        bilateral_color_sigma = torch.exp(torch.clamp(self.log_bilateral_color_sigma, min=-3, max=3))
        gaussian_sigma = torch.exp(torch.clamp(self.log_gaussian_sigma, min=-3, max=3))
        
        # 初始化
        logits = unary
        Q = F.softmax(logits, dim=1)

        # 平均场迭代
        for i in range(self.n_iter):
            # 消息传递
            gaussian_term = self._gaussian_filter(Q, gaussian_sigma)
            bilateral_term = self._bilateral_approximation(
                Q, img, bilateral_spatial_sigma, bilateral_color_sigma
            )
            
            # 组合消息
            raw_message = gaussian_weight * gaussian_term + bilateral_weight * bilateral_term
            
            # === 核心创新：消息幅度控制 ===
            # 使用tanh将消息约束在[-1, 1]范围内，防止数值爆炸
            normalized_message = torch.tanh(raw_message)
            
            # 应用可学习的缩放因子和迭代衰减
            decay_factor = torch.pow(torch.clamp(self.iteration_decay, min=0.1, max=1.0), i)
            scaled_message = self.message_scaler * decay_factor * normalized_message

            # 更新logits
            logits = unary + scaled_message
            Q = F.softmax(logits, dim=1)
            
        return logits
    
    def get_parameters_summary(self) -> dict:
        """
        获取当前学习到的参数摘要，用于监控训练过程
        
        Returns:
            dict: 包含所有参数当前值的字典
        """
        with torch.no_grad():
            return {
                'bilateral_weight': torch.exp(self.log_bilateral_weight).item(),
                'gaussian_weight': torch.exp(self.log_gaussian_weight).item(),
                'bilateral_spatial_sigma': torch.exp(self.log_bilateral_spatial_sigma).item(),
                'bilateral_color_sigma': torch.exp(self.log_bilateral_color_sigma).item(),
                'gaussian_sigma': torch.exp(self.log_gaussian_sigma).item(),
                'message_scaler': self.message_scaler.item(),
                'iteration_decay': self.iteration_decay.item()
            }


class DiceLoss(nn.Module):
    """
    Dice损失函数，用于优化分割区域的重叠度
    特别适合处理类别不平衡问题
    """
    
    def __init__(self, smooth: float = 1.0, reduction: str = 'mean'):
        """
        Args:
            smooth: 平滑因子，防止除零错误
            reduction: 损失减少方式，'mean', 'sum', 'none'
        """
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        self.reduction = reduction

    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: 模型输出logits (B, C, H, W)
            targets: 真实标签 (B, H, W) 或 (B, C, H, W)
            
        Returns:
            torch.Tensor: Dice损失值
        """
        # 转换为概率
        probs = F.softmax(logits, dim=1)
        
        # 处理标签格式
        if targets.dim() == 3:  # (B, H, W) -> (B, C, H, W)
            targets_one_hot = F.one_hot(targets.long(), num_classes=logits.size(1))
            targets_one_hot = targets_one_hot.permute(0, 3, 1, 2).float()
        else:
            targets_one_hot = targets.float()
        
        # 计算每个类别的Dice系数
        dice_losses = []
        for c in range(logits.size(1)):
            pred_c = probs[:, c, ...]
            true_c = targets_one_hot[:, c, ...]
            
            intersection = torch.sum(pred_c * true_c)
            cardinality = torch.sum(pred_c + true_c)
            
            dice_coeff = (2. * intersection + self.smooth) / (cardinality + self.smooth)
            dice_losses.append(1. - dice_coeff)
        
        dice_loss = torch.stack(dice_losses).mean()
        
        if self.reduction == 'mean':
            return dice_loss
        elif self.reduction == 'sum':
            return dice_loss * logits.size(0)
        else:
            return dice_loss


class FocalLoss(nn.Module):
    """
    Focal Loss实现，用于处理难易样本不平衡问题
    """
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        """
        Args:
            alpha: 类别权重因子
            gamma: 聚焦参数，gamma越大越关注难分样本
            reduction: 损失减少方式
        """
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            logits: 模型输出logits (B, C, H, W)
            targets: 真实标签 (B, H, W)
            
        Returns:
            torch.Tensor: Focal损失值
        """
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(logits, targets, reduction='none')
        
        # 计算概率
        probs = F.softmax(logits, dim=1)
        targets_one_hot = F.one_hot(targets.long(), num_classes=logits.size(1))
        targets_one_hot = targets_one_hot.permute(0, 3, 1, 2).float()
        
        # 计算预测概率
        pt = torch.sum(probs * targets_one_hot, dim=1)
        
        # 计算Focal Loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class CombinedLoss(nn.Module):
    """
    组合损失函数：结合Focal Loss和Dice Loss
    为CRF层提供高质量的梯度信号
    """
    
    def __init__(self, 
                 alpha_dice: float = 0.5, 
                 alpha_focal: float = 0.5,
                 focal_alpha: float = 1.0,
                 focal_gamma: float = 2.0,
                 dice_smooth: float = 1.0):
        """
        Args:
            alpha_dice: Dice损失的权重
            alpha_focal: Focal损失的权重
            focal_alpha: Focal Loss的alpha参数
            focal_gamma: Focal Loss的gamma参数
            dice_smooth: Dice Loss的平滑因子
        """
        super(CombinedLoss, self).__init__()
        self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.dice_loss = DiceLoss(smooth=dice_smooth)
        self.alpha_dice = alpha_dice
        self.alpha_focal = alpha_focal
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> dict:
        """
        Args:
            logits: 模型输出logits (B, C, H, W)
            targets: 真实标签 (B, H, W)
            
        Returns:
            dict: 包含各项损失值的字典
        """
        focal_loss_val = self.focal_loss(logits, targets)
        dice_loss_val = self.dice_loss(logits, targets)
        
        total_loss = self.alpha_focal * focal_loss_val + self.alpha_dice * dice_loss_val
        
        return {
            'total_loss': total_loss,
            'focal_loss': focal_loss_val,
            'dice_loss': dice_loss_val
        }


# === 测试和示例代码 ===

def test_trainable_crf():
    """
    测试TrainableCRF的基本功能
    """
    print("=== TrainableCRF 测试 ===")
    
    # 创建测试数据
    batch_size, num_classes, height, width = 2, 2, 64, 64
    
    # 模拟分割网络输出的logits
    unary = torch.randn(batch_size, num_classes, height, width)
    
    # 模拟原始RGB图像
    img = torch.rand(batch_size, 3, height, width)
    
    # 创建CRF层
    crf = TrainableCRF(
        n_iter=3,
        initial_bilateral_weight=8.0,
        initial_gaussian_weight=2.0
    )
    
    print(f"CRF参数数量: {sum(p.numel() for p in crf.parameters())}")
    
    # 前向传播
    refined_logits = crf(unary, img)
    
    print(f"输入logits形状: {unary.shape}")
    print(f"输出logits形状: {refined_logits.shape}")
    print(f"输入logits范围: [{unary.min():.4f}, {unary.max():.4f}]")
    print(f"输出logits范围: [{refined_logits.min():.4f}, {refined_logits.max():.4f}]")
    
    # 检查参数
    params = crf.get_parameters_summary()
    print("\n学习到的参数:")
    for name, value in params.items():
        print(f"  {name}: {value:.4f}")
    
    # 测试梯度
    dummy_loss = refined_logits.sum()
    dummy_loss.backward()
    
    print(f"\n梯度检查:")
    for name, param in crf.named_parameters():
        if param.grad is not None:
            print(f"  {name}: 梯度范数 = {param.grad.norm():.6f}")
        else:
            print(f"  {name}: 无梯度")
    
    print("✓ TrainableCRF 测试通过!\n")


def test_combined_loss():
    """
    测试组合损失函数
    """
    print("=== 组合损失函数测试 ===")
    
    # 创建测试数据
    batch_size, num_classes, height, width = 2, 2, 32, 32
    logits = torch.randn(batch_size, num_classes, height, width, requires_grad=True)
    targets = torch.randint(0, num_classes, (batch_size, height, width))
    
    # 创建损失函数
    criterion = CombinedLoss(alpha_dice=0.6, alpha_focal=0.4)
    
    # 计算损失
    loss_dict = criterion(logits, targets)
    
    print(f"总损失: {loss_dict['total_loss']:.4f}")
    print(f"Focal损失: {loss_dict['focal_loss']:.4f}")
    print(f"Dice损失: {loss_dict['dice_loss']:.4f}")
    
    # 测试梯度
    loss_dict['total_loss'].backward()
    print("✓ 组合损失函数测试通过!\n")


def demo_training_loop():
    """
    演示如何在训练循环中使用TrainableCRF
    """
    print("=== 训练循环演示 ===")
    
    # 创建模型组件
    crf = TrainableCRF(n_iter=3)
    criterion = CombinedLoss()
    optimizer = torch.optim.Adam(crf.parameters(), lr=0.001)
    
    # 模拟训练数据
    batch_size, num_classes, height, width = 4, 2, 32, 32
    
    print("开始训练演示...")
    for epoch in range(3):
        # 模拟一个batch的数据
        unary = torch.randn(batch_size, num_classes, height, width)
        img = torch.rand(batch_size, 3, height, width)
        targets = torch.randint(0, num_classes, (batch_size, height, width))
        
        # 训练步骤
        optimizer.zero_grad()
        
        # 前向传播
        refined_logits = crf(unary, img)
        
        # 计算损失
        loss_dict = criterion(refined_logits, targets)
        
        # 反向传播
        loss_dict['total_loss'].backward()
        optimizer.step()
        
        print(f"Epoch {epoch+1}: 损失 = {loss_dict['total_loss']:.4f}")
    
    # 显示最终参数
    print("\n训练后的参数:")
    params = crf.get_parameters_summary()
    for name, value in params.items():
        print(f"  {name}: {value:.4f}")
    
    print("✓ 训练循环演示完成!\n")


if __name__ == "__main__":
    print("🔥 端到端可训练CRF层 - 测试套件 🔥\n")
    
    # 运行所有测试
    test_trainable_crf()
    test_combined_loss()
    demo_training_loop()
    
    print("🎉 所有测试通过！可以开始集成到你的分割网络中了。")
    print("\n集成提示:")
    print("1. 将TrainableCRF作为网络的最后一层")
    print("2. 使用CombinedLoss替代普通的交叉熵损失")
    print("3. 确保CRF层的参数也包含在优化器中")
    print("4. 监控训练过程中的参数变化") 