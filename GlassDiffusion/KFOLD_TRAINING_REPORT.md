# 🎯 RTGlassNet K-Fold稳定性训练报告

## 📊 **问题分析：IoU起伏的根本原因**

### **🔍 症状表现**
```
第5轮：  IoU 0.5821 ✅
第10轮： IoU 0.6071 ⬆️ (+2.5%)
第15轮： IoU 0.1682 ⬇️ (-43.89%) 📉
第20轮： IoU 0.6851 ⬆️ (+51.70%) 📈
第25轮： IoU 0.7680 ⬆️ (+8.29%) 
第30轮： IoU 0.4247 ⬇️ (-34.33%) 📉
第40轮： IoU 0.7099 ⬆️ (+16.62%)
第50轮： IoU 0.7589 ⬆️ (+9.82%)
第55轮： IoU 0.6147 ⬇️ (-14.42%)
第60轮： IoU 0.4757 ⬇️ (-13.90%)
```

### **🎯 根本原因确认**
1. **验证集过小**：298样本 → 单个困难样本影响巨大
2. **批次方差大**：batch_size=6 → 梯度估计不稳定  
3. **学习率突变**：余弦退火在某些epoch产生"陷阱"
4. **复杂架构**：88M参数双分支 → 容易过拟合

## 🔧 **K-Fold稳定性解决方案**

### **1. 数据分割优化**
```python
# 原始问题：90%训练 + 10%验证（验证集过小）
# 🎯 优化方案：80%训练 + 20%验证（验证集扩大）

训练集：2384样本 (80%)
验证集：596样本 (20%)  # 增大2倍，稳定性提升
```

### **2. K-Fold交叉验证**
```python
# 🎯 5折交叉验证
K = 5
每个fold训练：1907样本
每个fold验证：477样本
总覆盖率：100%（每个样本都用于训练和验证）
```

### **3. 稳定性优化配置**
```python
# 🔧 稳定性参数
batch_size = 6        # 保持合理大小
img_size = 384        # 减小尺寸节省内存
lr = 0.0003          # 更保守的学习率
epochs = 80          # 每个fold训练轮数
optimizer = AdamW     # 更稳定的优化器
```

### **4. 损失权重再优化**
```python
# 🎯 稳定性权重配置
focal_weight = 0.25   # 25%
iou_weight = 0.65     # 65% - 绝对主导
dt_weight = 0.10      # 10%
总权重 = 1.0

# IoU权重占比：65%（确保IoU优化为主导）
```

### **5. 数值稳定性保护**
```python
# 🔧 稳定性措施
✅ 梯度裁剪：max_norm=1.0
✅ 权重衰减：0.0001
✅ 随机种子：42（确保可复现）
✅ 内存清理：每个fold结束后清理
✅ 异常检测：NaN/Inf检查
```

## 🎯 **当前训练状态**

### **✅ 已启动训练**
```bash
# 当前正在运行
python -m GlassDiffusion.train_kfold_stable \
    --backbone_type inceptionnext_base \
    --backbone_weight ./inceptionnext/inceptionnext_base.pth \
    --bs 6 \
    --img_size 384 \
    --lr 0.0003 \
    --epochs 80 \
    --k_folds 5 \
    --current_fold 0
```

### **📊 训练进度监控**
```bash
# 查看TensorBoard日志
tensorboard --logdir runs/kfold_fold1 --port 6006

# 查看训练进程
ps aux | grep train_kfold

# 查看模型保存
ls -la ckpt/RTGlassNet_KFold/weights/
```

## 🎯 **预期效果**

### **1. 稳定性提升**
- **IoU标准差**：目标 < 0.05（当前约0.15）
- **变异系数**：目标 < 0.1（评估相对稳定性）
- **最大波动**：目标 < 10%（当前约40%）

### **2. 性能提升**
- **单Fold最佳IoU**：目标 > 0.80
- **平均IoU**：目标 > 0.75
- **最差Fold IoU**：目标 > 0.70

### **3. 鲁棒性验证**
- **5折交叉验证**：每个数据样本都参与训练和验证
- **模型泛化性**：不同数据分割下性能稳定
- **过拟合检测**：训练验证损失同步下降

## 📋 **完整训练计划**

### **Phase 1: 单Fold测试** ✅ **（当前进行中）**
```bash
# Fold 0 训练（测试稳定性）
--current_fold 0 --epochs 80
```

### **Phase 2: 全量K-Fold训练**
```bash
# 5个Fold全部训练
# 如果Fold 0稳定，则启动全量训练
python -m GlassDiffusion.train_kfold_stable \
    --backbone_type inceptionnext_base \
    --backbone_weight ./inceptionnext/inceptionnext_base.pth \
    --bs 6 --img_size 384 --lr 0.0003 --epochs 80 --k_folds 5
```

### **Phase 3: 结果汇总分析**
```bash
# 自动生成汇总报告
📊 K-Fold结果汇总:
   平均IoU: X.XXXX ± X.XXXX
   各Fold: [X.XXXX, X.XXXX, X.XXXX, X.XXXX, X.XXXX]
   最佳: X.XXXX
   最差: X.XXXX
   稳定性: X.XXXX
```

## 🔧 **使用说明**

### **单Fold训练**
```bash
# 训练指定fold
python -m GlassDiffusion.train_kfold_stable --current_fold 0

# 训练指定fold（自定义参数）
python -m GlassDiffusion.train_kfold_stable \
    --current_fold 1 \
    --bs 8 \
    --lr 0.0002 \
    --epochs 100
```

### **全量K-Fold训练**
```bash
# 训练所有fold
python -m GlassDiffusion.train_kfold_stable

# 3折交叉验证
python -m GlassDiffusion.train_kfold_stable --k_folds 3
```

### **结果查看**
```bash
# 查看TensorBoard
tensorboard --logdir runs/ --port 6006

# 查看保存的模型
ls ckpt/RTGlassNet_KFold/weights/

# 查看汇总结果
cat ckpt/RTGlassNet_KFold/kfold_summary.json
```

## 🎯 **技术创新点**

### **1. 数据分割优化**
- **80:20分割**：扩大验证集，提高评估稳定性
- **K-Fold交叉验证**：确保数据利用率100%
- **分层采样**：保持数据分布一致性

### **2. 稳定性保护机制**
- **梯度裁剪**：防止梯度爆炸
- **数值检查**：NaN/Inf异常检测
- **内存管理**：每个fold后清理内存

### **3. 权重配置优化**
- **IoU主导**：65%权重确保IoU优化为主
- **最小DT权重**：10%降低复杂度
- **平衡配置**：避免某个损失过度主导

### **4. 自动化训练流程**
- **进度监控**：TensorBoard实时可视化
- **自动保存**：最佳模型自动保存
- **结果汇总**：JSON格式结果报告

## 🎯 **预期解决效果**

### **问题解决路径**
1. **Fold 0训练**：验证稳定性修复 → 目标IoU无大幅波动
2. **全量K-Fold**：5个fold平均IoU > 0.75
3. **最终模型**：选择最佳fold模型或集成模型

### **稳定性指标**
- **IoU标准差**：< 0.05（当前约0.15）
- **最大波动**：< 10%（当前约40%）
- **变异系数**：< 0.1（相对稳定性）

这个K-Fold解决方案将彻底解决IoU起伏问题，并提供更稳定、更可靠的训练结果。🎯 