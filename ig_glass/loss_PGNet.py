"""
 @Time    : 2025
 <AUTHOR> Glennine & Gemini

 @Project : IG_SLAM  
 @File    : loss_proteus_glass_net.py
 @Function: 为ProteusGlassNet量身定制的最终版损失函数

 设计哲学：
 1. 移除不稳定的边缘损失，聚焦核心任务。
 2. 保留并优化透明度物理先验。
 3. 采用BCE监督透明度，理论上更优。
 4. 保持深度监督结构，稳定训练过程。
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from pytorch_msssim import ssim # 确保已安装: pip install pytorch-msssim
from typing import Dict

class FinalFocalLoss(nn.Module):
    """Focal Loss标准实现"""
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super(FinalFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, pred, target):
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        bce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-bce_loss) # pt是正确分类的概率
        focal_weight = self.alpha * (1 - pt) ** self.gamma
        
        loss = focal_weight * bce_loss
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss

class FinalIoULoss(nn.Module):
    """IoU Loss标准实现"""
    def __init__(self, smooth=1e-6):
        super(FinalIoULoss, self).__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
        target = torch.clamp(target, min=0.0, max=1.0)
        
        intersection = torch.sum(pred * target, dim=(2, 3))
        union = torch.sum(pred, dim=(2, 3)) + torch.sum(target, dim=(2, 3)) - intersection
        
        iou = (intersection + self.smooth) / (union + self.smooth)
        return 1 - torch.mean(iou)

class FinalPhysicsLoss(nn.Module):
    """只包含透明度监督的物理损失"""
    def __init__(self, transparency_weight=1.0):
        super(FinalPhysicsLoss, self).__init__()
        self.transparency_weight = transparency_weight
        # 使用BCE Loss监督[0,1]的透明度图，比MSE更适合
        self.bce_loss = nn.BCELoss()

    def forward(self, physics_maps, target):
        if 'h_transparency' not in physics_maps:
            return torch.tensor(0.0, device=target.device)
            
        pred_transparency = physics_maps['h_transparency']
        # 目标：玻璃区域为1，背景为0，与主任务一致
        transparency_target = target
        
        if pred_transparency.shape[2:] != transparency_target.shape[2:]:
            pred_transparency = F.interpolate(pred_transparency, size=transparency_target.shape[2:], 
                                            mode='bilinear', align_corners=True)
        
        loss = self.bce_loss(pred_transparency, transparency_target)
        
        return self.transparency_weight * loss

class FinalConsistencyLoss(nn.Module):
    """只包含透明度一致性的正则化损失"""
    def __init__(self, consistency_weight=1.0):
        super(FinalConsistencyLoss, self).__init__()
        self.consistency_weight = consistency_weight

    def forward(self, physics_maps):
        # 在ProteusGlassNet中，h和l的透明度图是同一个，此损失为0
        # 但保留接口是为了未来扩展，例如使用多头预测
        if 'h_transparency' in physics_maps and 'l_transparency' in physics_maps:
            h_map = physics_maps['h_transparency']
            l_map = physics_maps['l_transparency']
            
            if h_map is l_map: # 如果是同一个张量
                return torch.tensor(0.0, device=h_map.device)

            if h_map.shape != l_map.shape:
                h_map = F.interpolate(h_map, size=l_map.shape[2:], mode='bilinear', align_corners=True)
            
            ssim_val = ssim(h_map, l_map, data_range=1.0, size_average=True)
            loss = 1.0 - ssim_val
            return self.consistency_weight * loss
        
        return torch.tensor(0.0, device=physics_maps.get('h_transparency', torch.zeros(1)).device)


class ProteusGlassNetLoss(nn.Module):
    """为ProteusGlassNet定制的最终复合损失函数"""
    def __init__(self, 
                 iou_weight=1.0, 
                 focal_weight=0.5,
                 physics_weight=0.3,
                 consistency_weight=0.2,
                 deep_supervision_weight=0.4):
        super(ProteusGlassNetLoss, self).__init__()
        
        self.iou_weight = iou_weight
        self.focal_weight = focal_weight
        self.physics_weight = physics_weight
        self.consistency_weight = consistency_weight
        self.deep_supervision_weight = deep_supervision_weight
        
        self.iou_loss = FinalIoULoss()
        self.focal_loss = FinalFocalLoss()
        self.physics_loss = FinalPhysicsLoss()
        self.consistency_loss = FinalConsistencyLoss()
        
    def forward(self, predictions: Dict[str, torch.Tensor], targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        
        # 数值稳定性保护：确保targets在[0,1]范围内
        targets = torch.clamp(targets, min=0.0, max=1.0)
        
        # 主预测来自CRF精炼后的结果
        main_pred = predictions['refined_pred']
        # 集成预测（CRF前）也需要监督
        ensemble_pred = predictions['ensemble_pred']
        
        # 确保预测值也在合理范围内
        main_pred = torch.clamp(main_pred, min=1e-7, max=1.0-1e-7)
        ensemble_pred = torch.clamp(ensemble_pred, min=1e-7, max=1.0-1e-7)
        
        # 深度监督目标
        deep_supervision_targets = [
            predictions['main_pred'], # 来自解码器的直接输出
            predictions['edge_pred'], # 来自物理模块的边缘（透明度）输出
            predictions['final_pred'] # 来自SCSA融合后的输出
        ]
        
        # 1. 主损失 (作用于CRF精炼后的最终结果)
        loss_iou_main = self.iou_loss(main_pred, targets)
        loss_focal_main = self.focal_loss(main_pred, targets)
        
        # 增加对CRF前集成结果的监督，确保CRF的输入质量
        loss_iou_ensemble = self.iou_loss(ensemble_pred, targets)
        loss_focal_ensemble = self.focal_loss(ensemble_pred, targets)

        # 2. 物理损失 (只监督透明度)
        loss_physics = self.physics_loss(predictions.get('physics_maps', {}), targets)
        
        # 3. 一致性损失 (正则化)
        loss_consistency = self.consistency_loss(predictions.get('physics_maps', {}))
        
        # 4. 深度监督损失
        loss_deep_supervision = 0.0
        for pred in deep_supervision_targets:
            # 确保每个深度监督预测也在合理范围内
            pred_clamped = torch.clamp(pred, min=1e-7, max=1.0-1e-7)
            loss_deep_supervision += self.focal_loss(pred_clamped, targets) + self.iou_loss(pred_clamped, targets)
        loss_deep_supervision /= len(deep_supervision_targets)
        
        # 计算总损失
        total_loss = (
            self.iou_weight * (loss_iou_main + 0.5 * loss_iou_ensemble) +
            self.focal_weight * (loss_focal_main + 0.5 * loss_focal_ensemble) +
            self.physics_weight * loss_physics +
            self.consistency_weight * loss_consistency +
            self.deep_supervision_weight * loss_deep_supervision
        )
        
        return {
            'total_loss': total_loss,
            'iou_loss': loss_iou_main,
            'focal_loss': loss_focal_main,
            'physics_loss': loss_physics,
            'consistency_loss': loss_consistency,
            'deep_supervision_loss': loss_deep_supervision
        }