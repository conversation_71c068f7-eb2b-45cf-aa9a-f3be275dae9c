"""
专门用于玻璃检测的数据加载器
集成玻璃感知数据增强功能
"""

from __future__ import print_function
from __future__ import absolute_import
from __future__ import division

import sys
import cv2
import numpy as np
import glob
import random
import os

import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image
from scipy.ndimage import distance_transform_edt
from typing import Tuple, Dict, Optional, List
import albumentations as A
from albumentations.pytorch import ToTensorV2

# 导入本地实现
from GlassDiffusion.glass_augmentation import apply_glass_augmentation, GLASS_AUGMENTATION_CONFIGS, GlassAugmentation
from GlassDiffusion.dataloader import pad_resize_image

# glass_dataloader.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 数据加载器
# 直接使用本地的GlassDataLoader实现
# -----------------------------------------------------------------------------

class GlassDataset(Dataset):
    """
    玻璃数据集加载器 - 支持距离变换图
    """
    def __init__(
        self,
        data_dir: str,
        split: str = 'train',
        target_size: Tuple[int, int] = (420, 420),
        split_ratio: float = 0.8,
        random_seed: int = 42,
        glass_aug_config: Optional[str] = None
    ):
        super(GlassDataset, self).__init__()
        
        self.data_dir = data_dir
        self.split = split
        self.target_size = target_size
        self.split_ratio = split_ratio
        self.random_seed = random_seed
        
        # 数据路径
        self.image_dir = os.path.join(data_dir, 'ig_glass/dataset/train_gdd/image')
        self.mask_dir = os.path.join(data_dir, 'ig_glass/dataset/train_gdd/mask')
        
        # 获取所有图像文件
        self.image_files = sorted([f for f in os.listdir(self.image_dir) if f.endswith('.jpg')])
        
        # 设置随机种子
        np.random.seed(random_seed)
        
        # 拆分数据集
        n_total = len(self.image_files)
        n_train = int(n_total * split_ratio)
        indices = np.random.permutation(n_total)
        
        if split == 'train':
            self.image_files = [self.image_files[i] for i in indices[:n_train]]
            print(f"🔄 拆分{os.path.basename(self.image_dir)}_train数据集: {len(self.image_files)}张图像 (总共{n_total}张)")
        else:
            self.image_files = [self.image_files[i] for i in indices[n_train:]]
            print(f"🔄 拆分{os.path.basename(self.image_dir)}_valid数据集: {len(self.image_files)}张图像 (总共{n_total}张)")
            
        print(f"📊 拆分比例: {split_ratio*100}% 训练集, {(1-split_ratio)*100}% 验证集")
        print(f"🎲 随机种子: {random_seed}")
        
        # 数据增强
        if split == 'train':
            if glass_aug_config:
                print(f"✅ 启用玻璃增强配置: {glass_aug_config}")
                self.glass_aug = GlassAugmentation(glass_aug_config)
            else:
                print(f"❌ 未启用玻璃增强 (配置: {glass_aug_config})")
                self.glass_aug = None
                
            self.transform = A.Compose([
                A.RandomResizedCrop(
                    height=target_size[0],
                    width=target_size[1],
                    scale=(0.8, 1.0),
                    ratio=(0.9, 1.1)
                ),
                A.HorizontalFlip(p=0.5),
                A.VerticalFlip(p=0.5),
                A.RandomRotate90(p=0.5),
                A.OneOf([
                    A.RandomBrightnessContrast(p=0.5),
                    A.RandomGamma(p=0.5),
                ], p=0.3),
                A.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                ),
                ToTensorV2()
            ], additional_targets={'dt_map': 'mask'})
        else:
            self.glass_aug = None
            self.transform = A.Compose([
                A.Resize(
                    height=target_size[0],
                    width=target_size[1]
                ),
                A.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                ),
                ToTensorV2()
            ], additional_targets={'dt_map': 'mask'})
            
        print(f"目标尺寸: {target_size[0]}x{target_size[1]}")
        
    def __len__(self):
        return len(self.image_files)
    
    def generate_dt_map(self, mask: np.ndarray) -> np.ndarray:
        """
        生成距离变换图
        Args:
            mask: [H, W] 二值掩码
        Returns:
            dt_map: [H, W] 归一化的距离变换图 [0, 1] 范围
        """
        # 确保掩码是二值的
        mask = (mask > 127).astype(np.uint8)  # 使用127作为阈值
        
        # 计算正向距离变换（从前景到背景）
        dt_pos = distance_transform_edt(mask)
        # 计算反向距离变换（从背景到前景）
        dt_neg = distance_transform_edt(1 - mask)
        
        # 合并距离变换 - 使用改进的归一化方法
        dt_map = dt_pos - dt_neg
        
        # 归一化到[0, 1]范围，而不是[-1, 1]
        if dt_map.max() != dt_map.min():
            # 先将范围调整到 [0, max-min]
            dt_map = dt_map - dt_map.min()
            # 然后归一化到 [0, 1]
            dt_map = dt_map / (dt_map.max() + 1e-8)
        else:
            # 如果所有值都相同，设为0.5
            dt_map = np.full_like(dt_map, 0.5)
        
        # 确保范围在[0, 1]
        dt_map = np.clip(dt_map, 0.0, 1.0)
        
        return dt_map.astype(np.float32)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        获取一个数据样本
        Returns:
            image: [3, H, W] 归一化的图像 - ImageNet标准化
            mask: [1, H, W] 二值掩码 [0, 1] 范围
            dt_map: [1, H, W] 距离变换图 [0, 1] 范围
        """
        # 加载图像和掩码
        image_path = os.path.join(self.image_dir, self.image_files[idx])
        mask_path = os.path.join(self.mask_dir, self.image_files[idx].replace('.jpg', '.png'))
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        if not os.path.exists(mask_path):
            raise FileNotFoundError(f"掩码文件不存在: {mask_path}")
        
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        # 数据验证
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        if mask is None:
            raise ValueError(f"无法读取掩码: {mask_path}")
        
        # 确保图像在[0, 255]范围
        image = np.clip(image, 0, 255).astype(np.uint8)
        # 确保掩码在[0, 255]范围
        mask = np.clip(mask, 0, 255).astype(np.uint8)
        
        # 玻璃增强（如果启用）
        if self.glass_aug is not None:
            image = self.glass_aug(image, mask)
        
        # 生成距离变换图 - 在数据增强之前生成
        dt_map = self.generate_dt_map(mask)
        
        # 应用数据增强 - albumentations会自动处理范围
        try:
            transformed = self.transform(
                image=image,
                mask=mask,
                dt_map=dt_map
            )
        except Exception as e:
            print(f"❌ 数据增强失败 - 图像: {image_path}, 错误: {e}")
            # 使用基础变换作为后备
            basic_transform = A.Compose([
                A.Resize(height=self.target_size[0], width=self.target_size[1]),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ], additional_targets={'dt_map': 'mask'})
            transformed = basic_transform(image=image, mask=mask, dt_map=dt_map)
        
        # 转换为张量
        image = transformed['image']  # [3, H, W] - ImageNet归一化
        mask = transformed['mask']  # [H, W] - [0, 255] 范围
        dt_map = transformed['dt_map']  # [H, W] - [0, 1] 范围
        
        # 标准化处理
        # 1. 图像已经通过albumentations归一化 (ImageNet标准)
        # 2. 掩码归一化到[0, 1]
        if isinstance(mask, torch.Tensor):
            mask = mask.float() / 255.0  # 转换到[0, 1]
        else:
            mask = torch.from_numpy(mask).float() / 255.0
            
        # 3. 距离变换图确保在[0, 1]范围
        if isinstance(dt_map, torch.Tensor):
            dt_map = dt_map.float()
        else:
            dt_map = torch.from_numpy(dt_map).float()
        
        # 确保范围正确
        mask = torch.clamp(mask, 0.0, 1.0)
        dt_map = torch.clamp(dt_map, 0.0, 1.0)
        
        # 添加通道维度
        mask = mask.unsqueeze(0)  # [1, H, W]
        dt_map = dt_map.unsqueeze(0)  # [1, H, W]
        
        # 最终验证
        assert image.shape[0] == 3, f"图像通道数错误: {image.shape}"
        assert mask.shape[0] == 1, f"掩码通道数错误: {mask.shape}"
        assert dt_map.shape[0] == 1, f"DT图通道数错误: {dt_map.shape}"
        assert 0.0 <= mask.min() and mask.max() <= 1.0, f"掩码范围错误: [{mask.min()}, {mask.max()}]"
        assert 0.0 <= dt_map.min() and dt_map.max() <= 1.0, f"DT图范围错误: [{dt_map.min()}, {dt_map.max()}]"
        
        return image, mask, dt_map

def create_glass_dataloaders(
    data_dir: str,
    batch_size: int = 6,
    target_size: Tuple[int, int] = (420, 420),
    split_ratio: float = 0.8,
    random_seed: int = 42,
    num_workers: int = 4,
    glass_aug_config: Optional[str] = None
) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    """
    print("🔄 创建数据加载器...")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - 目标尺寸: {target_size[0]}x{target_size[1]}")
    print(f"   - 数据增强: {'启用' if glass_aug_config else '禁用'}")
    print(f"   - 玻璃增强: {glass_aug_config}")
    print(f"   - 拆分比例: {split_ratio*100}% 训练 / {(1-split_ratio)*100}% 验证")
    
    # 创建训练集
    train_dataset = GlassDataset(
        data_dir=data_dir,
        split='train',
        target_size=target_size,
        split_ratio=split_ratio,
        random_seed=random_seed,
        glass_aug_config=glass_aug_config
    )
    
    # 创建验证集
    val_dataset = GlassDataset(
        data_dir=data_dir,
        split='valid',
        target_size=target_size,
        split_ratio=split_ratio,
        random_seed=random_seed
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    print("💡 数据拆分详情:")
    print(f"   总数据: {len(train_dataset) + len(val_dataset)}张")
    print(f"   训练集: {len(train_dataset)}张 ({split_ratio*100}%)")
    print(f"   验证集: {len(val_dataset)}张 ({(1-split_ratio)*100}%)")
    print(f"   当前模式: train - {len(train_dataset)}张")
    
    return train_loader, val_loader

GlassDataLoader = GlassDataset

if __name__ == '__main__':
    # 测试玻璃数据加载器
    print("测试玻璃数据加载器...")
    
    # 测试基本数据加载器
    train_loader = create_glass_dataloaders(
        mode='train',
        batch_size=4,
        target_size=416,
        augment_data=True,
        glass_augmentation='moderate'
    )
    
    print(f"训练数据加载器: {len(train_loader)}个批次")
    
    # 测试一个批次
    for batch_idx, (images, masks) in enumerate(train_loader):
        print(f"批次 {batch_idx}: 图像形状 {images.shape}, 掩码形状 {masks.shape}")
        print(f"图像值范围: [{images.min():.3f}, {images.max():.3f}]")
        print(f"掩码值范围: [{masks.min():.3f}, {masks.max():.3f}]")
        if batch_idx >= 2:
            break
    
    print("✅ 玻璃数据加载器测试成功！")

    # 测试数据加载器
    print("测试数据加载器...")
    
    train_loader, val_loader = create_dataloaders(
        batch_size=config.STAGE1_BATCH_SIZE,
        num_workers=config.NUM_WORKERS,
        target_size=config.GLASS_DATALOADER['target_size'],
        augment_data=config.GLASS_DATALOADER['augment_data'],
        glass_augmentation=config.GLASS_DATALOADER['glass_augmentation'],
        split_ratio=config.GLASS_DATALOADER['split_ratio'],
        random_seed=config.GLASS_DATALOADER['random_seed']
    )
    
    # 测试一个批次
    for batch_idx, (images, masks) in enumerate(train_loader):
        print(f"批次 {batch_idx}:")
        print(f"  - 图像形状: {images.shape}")
        print(f"  - 掩码形状: {masks.shape}")
        print(f"  - 图像范围: [{images.min():.3f}, {images.max():.3f}]")
        print(f"  - 掩码范围: [{masks.min():.3f}, {masks.max():.3f}]")
        if batch_idx >= 2:
            break
            
    print("✅ 数据加载器测试成功!") 