# train_stage_2.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 第二阶段训练脚本
# 训练扩散模型精炼器 (DiffusionRefiner)
# 基于第一阶段训练好的ProteusGlassNet进行精炼
# -----------------------------------------------------------------------------

import os
import sys
import time
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.cuda.amp import autocast, GradScaler
from tqdm import tqdm
import wandb
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from glass_dataloader import create_dataloaders
from models import ProteusGlassNet, DiffusionRefiner, NoiseScheduler
from utils.losses import create_stage2_loss
from utils.metrics import SegmentationMetrics
from utils.utils import (
    save_checkpoint, load_checkpoint, create_optimizer, create_scheduler,
    count_parameters, setup_seed, get_device, format_time,
    create_experiment_dir, log_metrics, Timer, AverageMeter, ProgressMeter
)

class Stage2Trainer:
    """第二阶段训练器 - 扩散模型精炼"""
    
    def __init__(self, stage1_model_path, resume_from=None, experiment_name=None):
        # 设置随机种子
        setup_seed(42)
        
        # 获取设备
        self.device = get_device()
        
        # 创建实验目录
        self.exp_dir = create_experiment_dir(config.OUTPUT_DIR, experiment_name or "Stage2")
        self.checkpoint_dir = os.path.join(self.exp_dir, 'checkpoints')
        self.log_file = os.path.join(self.exp_dir, 'logs', 'train_stage2.log')
        
        # 初始化wandb（离线模式）
        wandb.init(
            project="ProteusGlassDiffusion",
            name=f"Stage2_{os.path.basename(self.exp_dir)}",
            mode="offline",
            config={
                "stage": 2,
                "model": "DiffusionRefiner",
                "epochs": config.STAGE2_EPOCHS,
                "batch_size": config.STAGE2_BATCH_SIZE,
                "learning_rate": config.STAGE2_LR,
                "diffusion_timesteps": config.DIFFUSION_TIMESTEPS,
                "inference_steps": config.DIFFUSION_INFERENCE_STEPS
            }
        )
        self.use_wandb = True
        
        # 创建数据加载器
        print("🔄 创建数据加载器...")
        self.train_loader, self.val_loader = create_dataloaders(
            batch_size=config.STAGE2_BATCH_SIZE,
            num_workers=config.NUM_WORKERS,
            target_size=config.GLASS_DATALOADER['target_size'],
            augment_data=config.GLASS_DATALOADER['augment_data'],
            glass_augmentation=config.GLASS_DATALOADER['glass_augmentation'],
            split_ratio=config.GLASS_DATALOADER['split_ratio'],
            random_seed=config.GLASS_DATALOADER['random_seed']
        )
        
        # 加载第一阶段预训练模型
        print("🔄 加载第一阶段预训练模型...")
        self.stage1_model = ProteusGlassNet(
            vit_model_name=config.VIT_MODEL_NAME,
            backbone_path=config.BACKBONE_PRETRAINED_PATH,
            crf_iter=config.CRF_ITER,
            trainable_crf=config.CRF_TRAINABLE
        ).to(self.device)
        
        # 加载第一阶段权重
        if os.path.exists(stage1_model_path):
            checkpoint = torch.load(stage1_model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                self.stage1_model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.stage1_model.load_state_dict(checkpoint)
            print(f"✅ 成功加载第一阶段模型权重: {stage1_model_path}")
        else:
            raise FileNotFoundError(f"第一阶段模型权重文件不存在: {stage1_model_path}")
        
        # 冻结第一阶段模型
        for param in self.stage1_model.parameters():
            param.requires_grad = False
        self.stage1_model.eval()
        
        # 创建扩散模型
        print("🔄 创建扩散精炼模型...")
        self.diffusion_refiner = DiffusionRefiner(
            input_channels=1,
            timesteps=config.DIFFUSION_TIMESTEPS
        ).to(self.device)
        
        # 创建噪声调度器
        self.noise_scheduler = NoiseScheduler(
            timesteps=config.DIFFUSION_TIMESTEPS,
            beta_start=config.DIFFUSION_BETA_START,
            beta_end=config.DIFFUSION_BETA_END,
            device=self.device
        )
        
        # 统计参数
        print(f"📊 模型参数统计:")
        print(f"  - 第一阶段模型: {count_parameters(self.stage1_model)} 参数 (冻结)")
        print(f"  - 扩散精炼器: {count_parameters(self.diffusion_refiner)} 参数 (可训练)")
        
        # 创建损失函数
        self.criterion = create_stage2_loss()
        
        # 创建优化器和调度器
        self.optimizer = create_optimizer(
            self.diffusion_refiner,
            optimizer_type='adamw',
            lr=config.STAGE2_LR,
            weight_decay=config.STAGE2_WEIGHT_DECAY
        )
        
        self.scheduler = create_scheduler(
            self.optimizer,
            scheduler_type=config.SCHEDULER_TYPE,
            epochs=config.STAGE2_EPOCHS,
            step_size=config.STEP_SCHEDULER_STEP_SIZE,
            gamma=config.STEP_SCHEDULER_GAMMA
        )
        
        # 混合精度训练
        self.scaler = GradScaler() if config.MIXED_PRECISION else None
        
        # 训练状态
        self.start_epoch = 0
        self.best_iou = 0.0
        self.best_metrics = {}
        
        # 恢复训练（如果指定）
        if resume_from:
            self._resume_training(resume_from)
        
        # 创建评估器
        self.train_metrics = SegmentationMetrics()
        self.val_metrics = SegmentationMetrics()
        
        print("✅ 第二阶段训练器初始化完成!")
    
    def _init_wandb(self):
        """初始化wandb"""
        try:
            wandb.init(
                project="ProteusGlassDiffusion",
                name=f"Stage2_{os.path.basename(self.exp_dir)}",
                config={
                    "stage": 2,
                    "model": "DiffusionRefiner",
                    "epochs": config.STAGE2_EPOCHS,
                    "batch_size": config.STAGE2_BATCH_SIZE,
                    "learning_rate": config.STAGE2_LR,
                    "diffusion_timesteps": config.DIFFUSION_TIMESTEPS,
                    "inference_steps": config.DIFFUSION_INFERENCE_STEPS
                }
            )
            self.use_wandb = True
            print("✅ Wandb初始化成功")
        except Exception as e:
            print(f"⚠️ Wandb初始化失败: {e}")
            self.use_wandb = False
    
    def _resume_training(self, resume_path):
        """恢复训练"""
        checkpoint = torch.load(resume_path, map_location=self.device)
        self.diffusion_refiner.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.start_epoch = checkpoint['epoch'] + 1
        self.best_iou = checkpoint.get('best_iou', 0.0)
        print(f"🔄 从Epoch {self.start_epoch}恢复第二阶段训练，最佳IoU: {self.best_iou:.4f}")
    
    def generate_noise_and_timestep(self, batch_size):
        """生成噪声和时间步"""
        # 随机选择时间步
        timesteps = torch.randint(
            0, config.DIFFUSION_TIMESTEPS, (batch_size,), device=self.device
        ).long()
        
        # 生成随机噪声
        noise = torch.randn((batch_size, 1, *config.STAGE1_IMG_SIZE), device=self.device)
        
        return noise, timesteps
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.diffusion_refiner.train()
        self.train_metrics.reset()
        total_loss = 0
        
        # 创建进度条
        pbar = tqdm(
            self.train_loader,
            desc=f"Epoch [{epoch}/{config.STAGE2_EPOCHS}]",
            ncols=100,
            leave=True
        )
        
        for batch_idx, (images, masks) in enumerate(pbar):
            # 移动到GPU
            images = images.to(self.device, non_blocking=True)
            masks = masks.to(self.device, non_blocking=True)
            
            batch_size = images.size(0)
            
            # 使用第一阶段模型生成粗糙预测
            with torch.no_grad():
                stage1_outputs = self.stage1_model(images)
                coarse_pred = torch.sigmoid(stage1_outputs['main_pred_logits'])
            
            # 生成噪声和时间步
            noise, timesteps = self.generate_noise_and_timestep(batch_size)
            
            # 向粗糙预测添加噪声
            noisy_pred = self.noise_scheduler.add_noise(coarse_pred, noise, timesteps)
            
            # 前向传播
            with autocast(enabled=config.MIXED_PRECISION):
                # 扩散模型预测噪声
                predicted_noise = self.diffusion_refiner(
                    noisy_pred, timesteps, condition=images
                )
                
                # 计算损失
                loss, loss_dict = self.criterion(
                    predicted_noise, noise, 
                    refined_mask=None, coarse_mask=coarse_pred
                )
            
            # 反向传播
            self.optimizer.zero_grad()
            
            if self.scaler:
                self.scaler.scale(loss).backward()
                # 梯度裁剪
                if config.GRAD_CLIP_NORM > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        self.diffusion_refiner.parameters(), config.GRAD_CLIP_NORM
                    )
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                loss.backward()
                if config.GRAD_CLIP_NORM > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.diffusion_refiner.parameters(), config.GRAD_CLIP_NORM
                    )
                self.optimizer.step()
            
            # 更新总损失
            total_loss += loss.item()
            avg_loss = total_loss / (batch_idx + 1)
            
            # 更新进度条信息
            pbar.set_postfix({
                'Loss': f"{avg_loss:.4f}",
                'Noise': f"{loss_dict['noise_loss']:.4f}",
                'LR': f"{self.optimizer.param_groups[0]['lr']:.6f}"
            })
            
            # 每50个批次显示一次详细信息
            if (batch_idx + 1) % 50 == 0:
                print(f"\n📊 训练详情 [Batch {batch_idx}]:")
                print(f"  - 总损失: {avg_loss:.4f}")
                print(f"  - 噪声损失: {loss_dict['noise_loss']:.4f}")
                print(f"  - 学习率: {self.optimizer.param_groups[0]['lr']:.6f}\n")
        
        pbar.close()
        
        # 更新学习率
        self.scheduler.step()
        
        print(f"🔄 Epoch {epoch} 训练完成, 平均损失: {avg_loss:.4f}")
        return {'loss': avg_loss}
    
    def validate_epoch(self, epoch):
        """验证一个epoch"""
        self.diffusion_refiner.eval()
        self.val_metrics.reset()
        total_loss = 0
        
        # 创建验证进度条
        pbar = tqdm(
            self.val_loader,
            desc=f"验证 Epoch [{epoch}/{config.STAGE2_EPOCHS}]",
            ncols=100,
            leave=True
        )
        
        with torch.no_grad():
            for batch_idx, (images, masks) in enumerate(pbar):
                images = images.to(self.device, non_blocking=True)
                masks = masks.to(self.device, non_blocking=True)
                
                batch_size = images.size(0)
                
                # 使用第一阶段模型生成粗糙预测
                stage1_outputs = self.stage1_model(images)
                coarse_pred = torch.sigmoid(stage1_outputs['main_pred_logits'])
                
                # 使用扩散模型进行精炼
                refined_pred = self.inference_diffusion(coarse_pred, images)
                
                # 计算指标
                self.val_metrics.update(refined_pred, masks)
                
                # 用于验证的损失计算
                noise, timesteps = self.generate_noise_and_timestep(batch_size)
                noisy_pred = self.noise_scheduler.add_noise(coarse_pred, noise, timesteps)
                predicted_noise = self.diffusion_refiner(noisy_pred, timesteps, condition=images)
                loss, loss_dict = self.criterion(predicted_noise, noise)
                
                # 更新总损失
                total_loss += loss.item()
                avg_loss = total_loss / (batch_idx + 1)
                
                # 更新进度条
                metrics = self.val_metrics.compute()
                pbar.set_postfix({
                    'Loss': f"{avg_loss:.4f}",
                    'IoU': f"{metrics['iou']:.4f}",
                    'MAE': f"{metrics['mae']:.4f}"
                })
        
        pbar.close()
        
        # 计算最终指标
        metrics = self.val_metrics.compute()
        metrics['loss'] = avg_loss
        
        # 打印验证结果
        print(f"\n📊 验证结果 - Epoch {epoch}:")
        print(f"  - 验证损失: {metrics['loss']:.4f}")
        print(f"  - IoU: {metrics['iou']:.4f}")
        print(f"  - MAE: {metrics['mae']:.4f}")
        print(f"  - F1分数: {metrics['f1']:.4f}")
        print(f"  - 精确率: {metrics['precision']:.4f}")
        print(f"  - 召回率: {metrics['recall']:.4f}\n")
        
        return metrics
    
    def inference_diffusion(self, coarse_pred, condition=None):
        """扩散模型推理过程"""
        # 从纯噪声开始
        refined_pred = torch.randn_like(coarse_pred)
        
        # 推理步骤
        timesteps = torch.linspace(
            config.DIFFUSION_TIMESTEPS - 1, 0, 
            config.DIFFUSION_INFERENCE_STEPS
        ).long().to(self.device)
        
        for t in timesteps:
            # 为所有样本使用相同的时间步
            t_batch = t.repeat(coarse_pred.size(0))
            
            # 预测噪声
            predicted_noise = self.diffusion_refiner(refined_pred, t_batch, condition)
            
            # 去噪步骤
            refined_pred = self.noise_scheduler.remove_noise(
                refined_pred, predicted_noise, t_batch
            )
        
        return torch.sigmoid(refined_pred)
    
    def save_model(self, epoch, metrics, is_best=False):
        """保存模型"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.diffusion_refiner.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_iou': self.best_iou,
            'metrics': metrics,
            'config': {
                'diffusion_timesteps': config.DIFFUSION_TIMESTEPS,
                'inference_steps': config.DIFFUSION_INFERENCE_STEPS,
                'beta_start': config.DIFFUSION_BETA_START,
                'beta_end': config.DIFFUSION_BETA_END
            }
        }
        
        # 保存最新模型
        save_path = os.path.join(self.checkpoint_dir, 'latest_stage2.pth')
        torch.save(checkpoint, save_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_stage2.pth')
            torch.save(checkpoint, best_path)
            print(f"💾 保存最佳第二阶段模型: {best_path}")
        
        # 定期保存
        if epoch % config.SAVE_EVERY_N_EPOCHS == 0:
            epoch_path = os.path.join(
                self.checkpoint_dir, 
                f'stage2_epoch_{epoch:03d}_iou_{metrics["iou"]*100:.2f}.pth'
            )
            torch.save(checkpoint, epoch_path)
    
    def train(self):
        """完整训练流程"""
        print("\n🚀 开始第二阶段训练 - 扩散模型精炼")
        print(f"📊 训练配置:")
        print(f"  - 总轮数: {config.STAGE2_EPOCHS}")
        print(f"  - 批次大小: {config.STAGE2_BATCH_SIZE}")
        print(f"  - 学习率: {config.STAGE2_LR}")
        print(f"  - 扩散步数: {config.DIFFUSION_TIMESTEPS}")
        print(f"  - 推理步数: {config.DIFFUSION_INFERENCE_STEPS}")
        print(f"  - 混合精度: {config.MIXED_PRECISION}")
        print(f"  - 梯度裁剪: {config.GRAD_CLIP_NORM}")
        print(f"\n💫 扩散模型架构:")
        print(f"  - 时间步数: {config.DIFFUSION_TIMESTEPS}")
        print(f"  - Beta范围: [{config.DIFFUSION_BETA_START}, {config.DIFFUSION_BETA_END}]")
        print(f"  - 条件输入: 原始图像 + 第一阶段预测")
        print(f"  - 目标性能: IoU > 90%\n")
        
        for epoch in range(self.start_epoch, config.STAGE2_EPOCHS):
            print(f"\n{'='*50}")
            print(f"Epoch {epoch}/{config.STAGE2_EPOCHS}")
            print(f"{'='*50}")
            
            # 训练
            train_metrics = self.train_epoch(epoch)
            
            # 验证
            if epoch % config.EVAL_EVERY_N_EPOCHS == 0:
                val_metrics = self.validate_epoch(epoch)
                
                # 检查是否为最佳模型
                is_best = val_metrics['iou'] > self.best_iou
                if is_best:
                    self.best_iou = val_metrics['iou']
                    self.best_metrics = val_metrics
                    print(f"🏆 新的最佳模型! IoU: {self.best_iou:.4f}")
                
                # 保存模型
                self.save_model(epoch, val_metrics, is_best)
        
        print(f"\n🎉 第二阶段训练完成！")
        print(f"🏆 最佳验证指标:")
        for key, value in self.best_metrics.items():
            print(f"  - {key}: {value:.4f}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ProteusGlassDiffusion 第二阶段训练')
    parser.add_argument('--stage1_model', type=str, required=True,
                        help='第一阶段训练好的模型路径')
    parser.add_argument('--resume', type=str, default=None,
                        help='恢复训练的检查点路径')
    parser.add_argument('--name', type=str, default=None,
                        help='实验名称')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = Stage2Trainer(
        stage1_model_path=args.stage1_model,
        resume_from=args.resume,
        experiment_name=args.name
    )
    
    # 开始训练
    trainer.train()

if __name__ == "__main__":
    main() 