# config.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 项目配置文件
# 融合Proteus-ViT的强大特征与扩散模型的精准精炼，目标IoU > 90%
# 基于IG_SLAM项目的深度实践经验优化
# -----------------------------------------------------------------------------

import torch
import os

# --- 核心设置 ---
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
NUM_WORKERS = 4
MIXED_PRECISION = True  # 🔧 启用混合精度，提高训练效率

# --- 路径设置 ---
# ⚠️ 请根据您的实际环境修改这些路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Proteus和ig_glass路径配置
PROTEUS_PATH = os.path.join(PROJECT_ROOT, 'Proteus-pytorch/pretrain')
IG_GLASS_PATH = os.path.join(PROJECT_ROOT, 'ig_glass')

# --- 数据加载器配置 ---
GLASS_DATALOADER = {
    'target_size': 420,  # ViT-Base推荐使用更高分辨率
    'augment_data': True,  # 启用数据增强
    'glass_augmentation': 'moderate',  # 玻璃增强配置
    'split_ratio': 0.9,  # 训练集比例，改为9:1
    'random_seed': 42,  # 固定随机种子
}

# --- 第一阶段训练参数 (训练 ProteusGlassNet) ---
STAGE1_EPOCHS = 500
STAGE1_BATCH_SIZE = 8
STAGE1_LR = 0.00002  # 🔧 优化学习率：解码器使用2e-5，骨干网使用2e-6，平衡微调与从头学习
STAGE1_IMG_SIZE = (420, 420)  # 14的倍数，适配ViT patch size  
STAGE1_WEIGHT_DECAY = 0.01  # 🔧 增加权重衰减：从0.001增加到0.01，增强正则化
STAGE1_WARMUP_EPOCHS = 10  # 🔧 增加预热轮数，稳定训练

# 第一阶段训练好的模型权重路径，供第二阶段使用
STAGE1_BEST_MODEL_PATH = f"{os.path.join(os.path.dirname(os.path.abspath(__file__)), 'checkpoints')}/stage1_best_model.pth"

# --- 第二阶段训练参数 (训练 DiffusionRefiner) ---
STAGE2_EPOCHS = 200
STAGE2_BATCH_SIZE = 4  # 扩散模型需要更小的batch size
STAGE2_LR = 0.001  # 第二阶段可以稍低
STAGE2_WEIGHT_DECAY = 1e-4
STAGE2_WARMUP_EPOCHS = 10

# CRF参数
CRF_ITER = 3  # 减少迭代次数提高稳定性
CRF_TRAINABLE = True
CRF_BILATERAL_WEIGHT = 12.0

# ViT配置
VIT_MODEL_NAME = 'vit_small'
VIT_PATCH_SIZE = 14
BACKBONE_PRETRAINED_PATH = os.path.join(PROTEUS_PATH, 'proteus_vits_backbone.pth')

# 输出目录
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'checkpoints')

# --- 损失函数权重 (基于SCSA 87.95% IoU成功经验优化) ---
# 第一阶段损失权重 - 基于过拟合分析优化
STAGE1_FOCAL_WEIGHT = 0.5    # 🔧 降低：从0.6降到0.5，减少对难样本的过度关注
STAGE1_IOU_WEIGHT = 0.4      # 保持：IoU权重不变，这是核心指标
STAGE1_DT_WEIGHT = 0.1       # 🔧 降低：从0.2降到0.1，减少边缘损失干扰
STAGE1_TRANSPARENCY_WEIGHT = 0.0  # 🔧 暂时禁用：避免辅助损失导致过拟合
STAGE1_CONSISTENCY_WEIGHT = 0.0   # 🔧 暂时禁用：避免辅助损失导致过拟合
STAGE1_TOTAL_WEIGHT = 1.0    # 🔧 降低：从1.35降到1.0，简化损失函数

# 第二阶段损失权重
STAGE2_NOISE_WEIGHT = 1.0
STAGE2_CONSISTENCY_WEIGHT = 0.1

# --- 数据增强参数 ---
DATA_AUG_FLIP_PROB = 0.5
DATA_AUG_ROTATE_PROB = 0.3
DATA_AUG_COLOR_JITTER = True
DATA_AUG_GAUSSIAN_BLUR = True

# --- 验证和保存参数 ---
EVAL_EVERY_N_EPOCHS = 5     # 🔧 增加验证频率：从5降到2，更早发现过拟合
SAVE_EVERY_N_EPOCHS = 10      # 🔧 减少保存频率：从10降到5，避免过多检查点
LOG_INTERVAL = 50            # 日志记录间隔
GRAD_CLIP_NORM = 1.0         # 🔧 增加梯度裁剪：从0.5增加到1.0，稳定训练
KEEP_N_BEST_MODELS = 3

# --- 学习率调度器参数 ---
SCHEDULER_TYPE = 'cosine'  # 'cosine' or 'step'
STEP_SCHEDULER_STEP_SIZE = 30
STEP_SCHEDULER_GAMMA = 0.1

# --- 数值稳定性保护 (基于记忆的关键经验) ---
MIN_LOSS_SCALE = 1e-4         # 最小损失缩放
NUMERICAL_STABILITY = True    # 启用数值稳定性检查

# --- ViT分层学习率 (基于记忆的策略) ---
VIT_BACKBONE_LR_RATIO = 0.000005  # 🔧 Transformer微调：ViT使用极低学习率
ENABLE_LAYER_WISE_LR = True   # 启用分层学习率

# --- 训练策略优化 (基于记忆) ---
FROM_SCRATCH = True           # 重头训练获得最佳效果
ENABLE_WARMUP = True          # 启用学习率预热
WARMUP_RATIO = 0.1           # 预热比例

# 预训练权重路径
BACKBONE_WEIGHTS = '/home/<USER>/ws/IG_SLAM/ig_glass/ckpt/proteus_vits_backbone.pth'

# 🔧 修复混合精度训练设置 - 兼容辅助损失
ENABLE_MIXED_PRECISION = False  # 暂时禁用混合精度，避免BCE兼容性问题
ENABLE_GRAD_SCALER = False

print(f"📋 配置加载完成 (基于IG_SLAM项目优化经验):")
print(f"  - 设备: {DEVICE}")
print(f"  - 混合精度: {MIXED_PRECISION}")
print(f"  - 学习率: {STAGE1_LR} (已优化)")
print(f"  - 损失权重: Focal({STAGE1_FOCAL_WEIGHT}) + IoU({STAGE1_IOU_WEIGHT}) + DT({STAGE1_DT_WEIGHT})")
print(f"  - CRF迭代: {CRF_ITER} (已优化)")
print(f"  - 数值稳定性: {NUMERICAL_STABILITY}")
print(f"  - 数据集路径: {PROJECT_ROOT}")
print(f"  - 骨干网权重: {BACKBONE_WEIGHTS}")
print(f"  - 输出目录: {OUTPUT_DIR}") 