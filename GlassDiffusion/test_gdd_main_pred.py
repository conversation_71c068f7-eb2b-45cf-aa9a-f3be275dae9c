#!/usr/bin/env python3
"""
🔧 修复test_gdd.py - 强制使用main_pred与训练时保持一致
解决训练IoU高但测试效果差的问题
"""

import os
import warnings
import cv2
import numpy as np
import torch
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',
    'scale': 384,
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理
img_transform = <PERSON><PERSON>([
    <PERSON>.Resize(height=args['scale'], width=args['scale']),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ToTensorV2()
])

def detect_glass_and_evaluate_main_pred(image_folder, output_folder, gt_folder, model, glass_threshold):
    """使用main_pred进行检测和评估（与训练时一致）"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files.sort()
    
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0
    count = 0
    
    print(f"🎯 强制使用main_pred（与训练时验证一致）")
    
    for image_file in tqdm(image_files, desc="Processing with main_pred"):
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        
        # 读取图像
        img_rgb = cv2.imread(image_path)
        if img_rgb is None:
            continue
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
        
        # 读取GT
        if not os.path.exists(gt_path):
            continue
        gt_mask_u8 = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        if gt_mask_u8 is None:
            continue
        
        # 预处理
        transformed = img_transform(image=img_rgb)
        img_var = transformed['image'].unsqueeze(0).cuda(device_ids[0])
        
        # 模型推理
        with torch.no_grad():
            outputs = model(img_var)
            
            # 🎯 关键修复: 强制使用main_pred，与训练时验证保持一致
            pred_tensor = outputs['main_pred']  # 直接使用main_pred
            
            # 不使用refined_pred，避免CRF引起的问题
            # refined_pred = outputs.get('refined_pred')  # 注释掉
            
            # 提取预测概率图
            if pred_tensor.dim() == 4:
                pred_prob_map = pred_tensor.squeeze(0).squeeze(0)
            else:
                pred_prob_map = pred_tensor.squeeze()
        
        # 二值化（与训练时验证相同的阈值）
        binary_pred = (pred_prob_map > glass_threshold).float()
        prediction_float = binary_pred.cpu().numpy()
        
        # 处理GT（确保尺寸匹配）
        if gt_mask_u8.shape != prediction_float.shape:
            gt_resized = cv2.resize(gt_mask_u8, (prediction_float.shape[1], prediction_float.shape[0]))
        else:
            gt_resized = gt_mask_u8
        
        gt_float = (gt_resized / 255.0).astype(np.float32)
        
        # 保存预测结果
        pred_to_save = (prediction_float * 255).astype(np.uint8)
        output_path = os.path.join(output_folder, os.path.splitext(image_file)[0] + '.png')
        cv2.imwrite(output_path, pred_to_save)
        
        # 计算指标
        try:
            tiou = compute_iou(prediction_float, gt_float)
            tacc = compute_acc(prediction_float, gt_float)
            precision, recall = compute_precision_recall(prediction_float, gt_float)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(prediction_float, gt_float)
            tber = compute_ber(prediction_float, gt_float)
            taber = compute_aber(prediction_float, gt_float)
            
            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber += taber
            
        except Exception as e:
            print(f"⚠️ 计算指标时出错 {image_file}: {e}")
            continue
    
    if count > 0:
        return iou/count, acc/count, fm/count, mae/count, ber/count, aber/count
    else:
        return 0, 0, 0, 0, 0, 0

def main():
    print("🎯 使用main_pred的测试脚本（与训练时一致）")
    print("=" * 60)
    
    # 检查模型文件
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        # 列出可用的模型文件
        if os.path.exists(ckpt_path):
            available_models = [f for f in os.listdir(ckpt_path) if f.endswith('.pth')]
            print(f"可用模型文件: {available_models}")
        return
    
    print(f"✅ 加载模型: {model_path}")
    
    # 加载模型
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    print(f"骨干网络类型: {backbone_type}")
    
    model = RTGlassNet(
        backbone_type=backbone_type, 
        crf_iter=args['crf_iter'], 
        crf_bilateral_weight=args['crf_bilateral_weight']
    )
    
    # 加载权重
    if 'model' in checkpoint:
        result = model.load_state_dict(checkpoint['model'], strict=False)
    else:
        result = model.load_state_dict(checkpoint, strict=False)
    
    print(f"权重加载结果: {result}")
    
    model.cuda(device_ids[0])
    model.eval()
    
    # 检查数据路径
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    image_folder = os.path.join(data_path, "image")
    output_folder = os.path.join(data_path, "glass_mask_gdd_main_pred")  # 新的输出目录
    gt_folder = os.path.join(data_path, "mask")
    
    print(f"图像目录: {image_folder}")
    print(f"输出目录: {output_folder}")
    print(f"GT目录: {gt_folder}")
    
    # 检查目录是否存在
    for folder, name in [(image_folder, "图像"), (gt_folder, "GT")]:
        if not os.path.exists(folder):
            print(f"❌ {name}目录不存在: {folder}")
            return
        else:
            files = os.listdir(folder)
            print(f"✅ {name}目录包含 {len(files)} 个文件")
    
    print(f"\n🚀 开始测试（仅使用main_pred）...")
    
    # 运行测试
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate_main_pred(
        image_folder, output_folder, gt_folder, model, args['glass_threshold']
    )
    
    print(f"\n" + "="*60)
    print(f"        使用main_pred的测试结果（与训练一致）")
    print(f"="*60)
    print(f"  IoU (Intersection over Union): {iou:.4f}")
    print(f"  Accuracy:                      {acc:.4f}")
    print(f"  F-measure:                     {fm:.4f}")
    print(f"  MAE (Mean Absolute Error):     {mae:.4f}")
    print(f"  BER (Balanced Error Rate):     {ber:.4f}")
    print(f"  ABER (Adaptive BER):           {aber:.4f}")
    print(f"="*60)
    
    print(f"\n💡 关键修复:")
    print(f"1. ✅ 强制使用main_pred（与训练时验证一致）")
    print(f"2. ✅ 不使用refined_pred（避免CRF问题）")
    print(f"3. ✅ 相同的阈值和处理流程")
    
    print(f"\n📊 预期效果:")
    print(f"  - 测试IoU应该接近训练时的验证IoU")
    print(f"  - 如果IoU仍然很低，说明问题在其他地方")
    print(f"  - 如果IoU显著提升，说明CRF是问题根源")

if __name__ == '__main__':
    main()
