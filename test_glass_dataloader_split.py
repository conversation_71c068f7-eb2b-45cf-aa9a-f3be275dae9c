#!/usr/bin/env python3
"""
测试GlassDataLoader的数据拆分功能
验证数据是否正确拆分，无重叠，比例正确
"""

import os
import sys
from ig_glass.glass_dataloader import GlassDataLoader

def test_data_split():
    """测试数据拆分功能"""
    print("🧪 测试GlassDataLoader数据拆分功能")
    print("=" * 60)
    
    # 测试配置
    target_size = 416
    split_ratio = 0.8
    random_seed = 42
    
    try:
        # 创建训练集数据加载器
        print("📂 创建训练集数据加载器...")
        train_loader = GlassDataLoader(
            mode='train_gdd_split_train',
            augment_data=False,  # 测试时不使用数据增强
            target_size=target_size,
            split_ratio=split_ratio,
            random_seed=random_seed
        )
        
        print("\n📂 创建验证集数据加载器...")
        valid_loader = GlassDataLoader(
            mode='train_gdd_split_valid',
            augment_data=False,
            target_size=target_size,
            split_ratio=split_ratio,
            random_seed=random_seed
        )
        
        # 获取文件路径进行验证
        train_files = set(train_loader.inp_files)
        valid_files = set(valid_loader.inp_files)
        
        print("\n📊 数据拆分验证结果:")
        print(f"   训练集大小: {len(train_files)}张")
        print(f"   验证集大小: {len(valid_files)}张")
        print(f"   总数据大小: {len(train_files) + len(valid_files)}张")
        
        # 验证没有重叠
        overlap = train_files.intersection(valid_files)
        print(f"   数据重叠: {len(overlap)}张")
        
        # 验证比例
        total_size = len(train_files) + len(valid_files)
        actual_train_ratio = len(train_files) / total_size
        actual_valid_ratio = len(valid_files) / total_size
        
        print(f"   实际训练集比例: {actual_train_ratio:.3f} (期望: {split_ratio:.3f})")
        print(f"   实际验证集比例: {actual_valid_ratio:.3f} (期望: {1-split_ratio:.3f})")
        
        # 测试数据加载
        print("\n🔍 测试数据加载...")
        train_sample = train_loader[0]
        valid_sample = valid_loader[0]
        
        print(f"   训练样本形状: 图像{train_sample[0].shape}, 掩码{train_sample[1].shape}")
        print(f"   验证样本形状: 图像{valid_sample[0].shape}, 掩码{valid_sample[1].shape}")
        
        # 验证结果
        success = True
        if len(overlap) > 0:
            print("❌ 错误: 训练集和验证集存在重叠!")
            success = False
        
        if abs(actual_train_ratio - split_ratio) > 0.05:
            print("❌ 错误: 训练集比例偏差过大!")
            success = False
        
        if success:
            print("\n✅ 数据拆分测试通过!")
            print("🎯 可以安全使用该数据加载器进行训练")
        else:
            print("\n❌ 数据拆分测试失败!")
            return False
            
        # 显示文件路径示例
        print("\n📋 训练集文件示例:")
        for i, file_path in enumerate(list(train_files)[:3]):
            print(f"   {i+1}. {os.path.basename(file_path)}")
        
        print("\n📋 验证集文件示例:")
        for i, file_path in enumerate(list(valid_files)[:3]):
            print(f"   {i+1}. {os.path.basename(file_path)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reproducibility():
    """测试随机种子的可重复性"""
    print("\n🔄 测试随机种子可重复性...")
    
    # 第一次创建
    loader1 = GlassDataLoader(
        mode='train_gdd_split_train',
        augment_data=False,
        target_size=416,
        split_ratio=0.8,
        random_seed=42
    )
    
    # 第二次创建
    loader2 = GlassDataLoader(
        mode='train_gdd_split_train',
        augment_data=False,
        target_size=416,
        split_ratio=0.8,
        random_seed=42
    )
    
    # 比较文件列表
    files1 = set(loader1.inp_files)
    files2 = set(loader2.inp_files)
    
    if files1 == files2:
        print("✅ 随机种子可重复性测试通过!")
    else:
        print("❌ 随机种子可重复性测试失败!")
        return False
    
    return True

if __name__ == '__main__':
    print("🚀 开始测试GlassDataLoader...")
    
    # 测试数据拆分
    split_success = test_data_split()
    
    # 测试可重复性
    repro_success = test_reproducibility()
    
    print("\n" + "=" * 60)
    if split_success and repro_success:
        print("🎉 所有测试通过! GlassDataLoader可以安全使用")
        sys.exit(0)
    else:
        print("💥 测试失败! 请检查配置")
        sys.exit(1) 