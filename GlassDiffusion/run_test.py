#!/usr/bin/env python3
"""
🚀 RTGlassNet测试运行脚本
快速测试模型性能和IoU计算
"""

import os
import sys
import torch
import argparse

# 添加路径
sys.path.append('/home/<USER>/ws/IG_SLAM')

def test_iou_calculation():
    """测试IoU计算的正确性"""
    print("🧪 运行IoU计算验证...")
    os.system("python GlassDiffusion/test_simple_iou.py")

def test_model_performance(model_path=None, data_dir='/home/<USER>/ws/IG_SLAM/'):
    """测试模型性能"""
    print("🎯 运行模型性能测试...")
    
    if model_path and os.path.exists(model_path):
        cmd = f"python GlassDiffusion/test_iou_calculation.py --model_path {model_path} --data_dir {data_dir}"
    else:
        print("⚠️ 模型路径不存在，使用随机权重测试...")
        cmd = f"python GlassDiffusion/test_iou_calculation.py --data_dir {data_dir}"
    
    os.system(cmd)

def main():
    parser = argparse.ArgumentParser(description='🚀 RTGlassNet测试')
    parser.add_argument('--test_type', choices=['iou', 'model', 'both'], default='both',
                       help='测试类型: iou(IoU计算), model(模型性能), both(全部)')
    parser.add_argument('--model_path', default='./ckpt/RTGlassNet_KFold/weights/BEST_fold1_epoch080_iou0.8500.pth',
                       help='模型权重路径')
    parser.add_argument('--data_dir', default='/home/<USER>/ws/IG_SLAM/',
                       help='数据目录')
    
    args = parser.parse_args()
    
    print("🎯 RTGlassNet InceptionNeXt 测试")
    print("=" * 60)
    
    if args.test_type in ['iou', 'both']:
        test_iou_calculation()
        print("\n" + "="*60 + "\n")
    
    if args.test_type in ['model', 'both']:
        test_model_performance(args.model_path, args.data_dir)
    
    print("\n✅ 测试完成！")
    print("\n📋 总结:")
    print("  - IoU计算已修正为逐样本平均方法")
    print("  - 训练代码 train_kfold_stable.py 已更新")
    print("  - 建议使用修正后的代码进行训练")

if __name__ == '__main__':
    main()
