# utils/metrics.py
# -----------------------------------------------------------------------------
# ProteusGlassDiffusion 评估指标
# 包含IoU、MAE、F1-Score等常用分割指标
# -----------------------------------------------------------------------------

import torch
import numpy as np
from sklearn.metrics import f1_score

def calculate_iou(pred, target, threshold=0.5):
    """
    计算IoU (Intersection over Union)
    Args:
        pred: 预测结果 [B, 1, H, W] 或 [H, W]
        target: 真值 [B, 1, H, W] 或 [H, W]
        threshold: 二值化阈值
    """
    # 确保输入为tensor
    if isinstance(pred, np.ndarray):
        pred = torch.from_numpy(pred)
    if isinstance(target, np.ndarray):
        target = torch.from_numpy(target)
    
    # 二值化
    pred = (pred > threshold).float()
    target = (target > threshold).float()
    
    # 展平
    pred = pred.view(-1)
    target = target.view(-1)
    
    # 计算交集和并集
    intersection = (pred * target).sum()
    union = pred.sum() + target.sum() - intersection
    
    # 避免除零
    if union == 0:
        return 1.0 if intersection == 0 else 0.0
    
    iou = intersection / union
    return iou.detach().item()  # 添加detach()

def calculate_mae(pred, target):
    """
    计算MAE (Mean Absolute Error)
    Args:
        pred: 预测结果 [B, 1, H, W] 或 [H, W]
        target: 真值 [B, 1, H, W] 或 [H, W]
    """
    # 确保输入为tensor
    if isinstance(pred, np.ndarray):
        pred = torch.from_numpy(pred)
    if isinstance(target, np.ndarray):
        target = torch.from_numpy(target)
    
    mae = torch.mean(torch.abs(pred - target))
    return mae.detach().item()  # 添加detach()

def calculate_f1_score(pred, target, threshold=0.5):
    """
    计算F1-Score
    Args:
        pred: 预测结果 [B, 1, H, W] 或 [H, W]
        target: 真值 [B, 1, H, W] 或 [H, W]
        threshold: 二值化阈值
    """
    # 确保输入为numpy数组
    if isinstance(pred, torch.Tensor):
        pred = pred.detach().cpu().numpy()  # 添加detach()
    if isinstance(target, torch.Tensor):
        target = target.detach().cpu().numpy()  # 添加detach()
    
    # 二值化
    pred = (pred > threshold).astype(np.float32)
    target = (target > threshold).astype(np.float32)
    
    # 展平
    pred = pred.flatten()
    target = target.flatten()
    
    # 计算F1-Score
    f1 = f1_score(target, pred, average='binary', zero_division=0)
    return f1

def calculate_precision_recall(pred, target, threshold=0.5):
    """
    计算精确率和召回率
    Args:
        pred: 预测结果 [B, 1, H, W] 或 [H, W]
        target: 真值 [B, 1, H, W] 或 [H, W]
        threshold: 二值化阈值
    Returns:
        precision, recall: 精确率和召回率
    """
    # 确保输入为numpy数组
    if isinstance(pred, torch.Tensor):
        pred = pred.detach().cpu().numpy()  # 添加detach()
    if isinstance(target, torch.Tensor):
        target = target.detach().cpu().numpy()  # 添加detach()
    
    # 二值化
    pred = (pred > threshold).astype(np.float32)
    target = (target > threshold).astype(np.float32)
    
    # 展平
    pred = pred.flatten()
    target = target.flatten()
    
    # 计算TP, FP, FN
    tp = np.sum(pred * target)
    fp = np.sum(pred * (1 - target))
    fn = np.sum((1 - pred) * target)
    
    # 计算precision和recall
    precision = tp / (tp + fp + 1e-7)
    recall = tp / (tp + fn + 1e-7)
    
    return precision.item(), recall.item()

class SegmentationMetrics:
    """分割评估指标计算器"""
    
    def __init__(self):
        self.reset()
        
    def reset(self):
        """重置所有指标"""
        self.ious = []
        self.maes = []
        self.f1s = []
        self.precisions = []
        self.recalls = []
        
    def update(self, pred, target, threshold=0.5):
        """
        更新指标
        Args:
            pred: 预测结果 [B, 1, H, W]
            target: 真值 [B, 1, H, W]
            threshold: 二值化阈值
        """
        # 确保不计算梯度
        with torch.no_grad():
            batch_size = pred.shape[0]
            
            for i in range(batch_size):
                pred_i = pred[i]
                target_i = target[i]
                
                # 计算各项指标
                iou = calculate_iou(pred_i, target_i, threshold)
                mae = calculate_mae(pred_i, target_i)
                f1 = calculate_f1_score(pred_i, target_i, threshold)
                precision, recall = calculate_precision_recall(pred_i, target_i, threshold)
                
                # 添加到列表
                self.ious.append(iou)
                self.maes.append(mae)
                self.f1s.append(f1)
                self.precisions.append(precision)
                self.recalls.append(recall)
    
    def compute(self):
        """计算平均指标"""
        if len(self.ious) == 0:
            return {
                'iou': 0.0,
                'mae': 0.0,
                'f1': 0.0,
                'precision': 0.0,
                'recall': 0.0
            }
        
        return {
            'iou': np.mean(self.ious),
            'mae': np.mean(self.maes),
            'f1': np.mean(self.f1s),
            'precision': np.mean(self.precisions),
            'recall': np.mean(self.recalls)
        }
    
    def __str__(self):
        """格式化输出指标"""
        metrics = self.compute()
        return (f"IoU: {metrics['iou']:.4f}, "
                f"MAE: {metrics['mae']:.4f}, "
                f"F1: {metrics['f1']:.4f}, "
                f"Precision: {metrics['precision']:.4f}, "
                f"Recall: {metrics['recall']:.4f}")

def batch_iou(pred, target, threshold=0.5):
    """
    批量计算IoU
    Args:
        pred: 预测结果 [B, 1, H, W]
        target: 真值 [B, 1, H, W]
        threshold: 二值化阈值
    """
    batch_size = pred.shape[0]
    ious = []
    
    for i in range(batch_size):
        iou = calculate_iou(pred[i], target[i], threshold)
        ious.append(iou)
    
    return np.array(ious)

def batch_mae(pred, target):
    """
    批量计算MAE
    Args:
        pred: 预测结果 [B, 1, H, W]
        target: 真值 [B, 1, H, W]
    """
    batch_size = pred.shape[0]
    maes = []
    
    for i in range(batch_size):
        mae = calculate_mae(pred[i], target[i])
        maes.append(mae)
    
    return np.array(maes)

# 导出所有函数
__all__ = [
    'calculate_iou',
    'calculate_mae',
    'calculate_f1_score',
    'calculate_precision_recall',
    'SegmentationMetrics',
    'batch_iou',
    'batch_mae'
] 