import os
import warnings

# 抑制所有已知警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

import cv2
import numpy as np
import torch
from PIL import Image
from torchvision import transforms
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',  # 可根据实际权重文件名修改
    'scale': 384,
    'glass_threshold': 0.5,  # 使用标准的0.5作为阈值
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理 (使用和训练时完全一致的albumentations流程)
img_transform = A.<PERSON>([
    <PERSON><PERSON>(height=args['scale'], width=args['scale']),
    A.Normalize(
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225]
    ),
    ToTensorV2()
], additional_targets={'mask': 'mask'})

def path_set(tempdata_path, new_path):
    results_root = os.path.join(tempdata_path, new_path)
    return results_root

def detect_glass_and_evaluate(image_folder, output_folder, gt_folder, model, glass_threshold, log_interval=100):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    image_files = [f for f in os.listdir(image_folder) if os.path.isfile(os.path.join(image_folder, f))]
    image_files.sort()
    count = 0
    total_files = len(image_files)
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0
    torch.cuda.empty_cache()

    for image_file in tqdm(image_files, desc="Evaluating"):
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')

        img_rgb = cv2.imread(image_path)
        if img_rgb is None:
            print(f"Warning: Could not read image {image_path}")
            continue
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)

        if not os.path.exists(gt_path):
            print(f"Warning: Ground truth not found for {image_file}, skipping.")
            continue
        gt_mask_u8 = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        if gt_mask_u8 is None:
            print(f"Warning: Could not read mask {gt_path}")
            continue

        # 关键修复：将图像和掩码同时送入同一个albumentations变换管道
        transformed = img_transform(image=img_rgb, mask=gt_mask_u8)
        img_var = transformed['image'].unsqueeze(0).cuda(device_ids[0])
        gt_tensor_aligned = transformed['mask'].float() / 255.0

        with torch.no_grad():
            output = model(img_var)
            pred_tensor = output['refined_pred']
            if pred_tensor.max() - pred_tensor.min() < 1e-5 or pred_tensor.mean() < 1e-4:
                pred_tensor = output['main_pred']

            if pred_tensor.dim() == 4 and pred_tensor.size(1) == 2:
                pred_prob_map = pred_tensor[0, 1]
            else:
                pred_prob_map = pred_tensor.squeeze(0)

        prediction_float = (pred_prob_map > glass_threshold).float().cpu().numpy()
        gt_float = gt_tensor_aligned.cpu().numpy()

        pred_to_save = (prediction_float * 255).astype(np.uint8)
        cv2.imwrite(os.path.join(output_folder, os.path.splitext(image_file)[0] + '.png'), pred_to_save)

        tiou = compute_iou(prediction_float, gt_float)
        tacc = compute_acc(prediction_float, gt_float)
        precision, recall = compute_precision_recall(prediction_float, gt_float)
        tfm = compute_fmeasure(precision, recall)
        tmae = compute_mae(prediction_float, gt_float)
        tber = compute_ber(prediction_float, gt_float)
        taber = compute_aber(prediction_float, gt_float)
        
        count += 1
        iou += tiou
        acc += tacc
        fm += tfm
        mae += tmae
        ber += tber
        aber += taber

    if count > 0:
        iou /= count
        acc /= count
        fm /= count
        mae /= count
        ber /= count
        aber /= count

    return iou, acc, fm, mae, ber, aber

def main():
    device_name = torch.cuda.get_device_name(device_ids[0])
    print(f"--- Running on: {device_name} ---")

    print(f'Load snapshot {args["snapshot"]} for testing')
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')

    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    print(f"从检查点推断出骨干网络: {backbone_type}")

    model = RTGlassNet(backbone_type=backbone_type, crf_iter=args['crf_iter'], crf_bilateral_weight=args['crf_bilateral_weight'])
    
    if 'model' in checkpoint:
        result = model.load_state_dict(checkpoint['model'], strict=False)
    else:
        result = model.load_state_dict(checkpoint, strict=False)
    
    print(f'Load {model_path} succeed!')
    print('权重加载日志:', result)
    model.cuda(device_ids[0])
    model.eval()
    print("\n=== Starting Evaluation ===")
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    current = "glass_mask_gdd"
    image_folder = path_set(data_path, "image")
    output_folder = path_set(data_path, current)
    gt_folder = path_set(data_path, "mask")
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate(
        image_folder, output_folder, gt_folder, model, args['glass_threshold'],
        log_interval=100
    )
    print("\n" + "="*60)
    print("            COMPREHENSIVE EVALUATION RESULTS")
    print("="*60)
    print(f"  IoU (Intersection over Union): {iou:.4f}")
    print(f"  Accuracy:                      {acc:.4f}")
    print(f"  F-measure:                     {fm:.4f}")
    print(f"  MAE (Mean Absolute Error):     {mae:.4f}")
    print(f"  BER (Balanced Error Rate):     {ber:.4f}")
    print(f"  ABER (Adaptive BER):           {aber:.4f}")
    print("="*60)

if __name__ == '__main__':
    main()