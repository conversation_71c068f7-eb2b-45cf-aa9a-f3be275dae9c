#!/usr/bin/env python3
"""
🎯 RTGlassNet 最终测试与评估脚本
- 加载训练好的模型检查点
- 在指定的测试集上进行推理
- 计算并报告详细的性能指标 (IoU, MAE, F1等)
- 测试模型的端到端推理速度 (FPS)
- (可选) 保存预测的可视化结果
"""

import os
import warnings
import numpy as np
import time
from PIL import Image

# 抑制不必要的警告
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
warnings.filterwarnings('ignore', category=FutureWarning, module='timm')
warnings.filterwarnings('ignore', category=UserWarning, message='.*Overwriting.*in registry.*')

import argparse
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm
import torch.nn.functional as F

# 假设您的模块位于正确的路径下
# 您可能需要根据您的项目结构调整这里的导入路径
from models.RTGlassNet import RTGlassNet
from glass_dataloader import GlassDataLoader
from utils.metrics import SegmentationMetrics # 沿用您训练脚本中的指标计算器

def set_seed(seed=42):
    """设置随机种子以保证可复现性"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    print(f"🎲 随机种子已设置为: {seed}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='🎯 RTGlassNet 测试脚本')
    
    # --- 核心参数 ---
    parser.add_argument('--model_path', type=str, required=True, 
                        help='必须提供！指向已训练好的模型权重文件 (.pth) 的路径。')
    parser.add_argument('--data_path', type=str, default='/home/<USER>/ws/IG_SLAM/', 
                        help='数据集的根目录。')
    parser.add_argument('--data_split', type=str, default='test', 
                        help="要使用的数据集划分 ('test', 'valid', 'train')。")
                        
    # --- 模型与数据配置 (必须与训练时匹配) ---
    parser.add_argument('--backbone_type', type=str, default='inceptionnext_base', 
                        help='骨干网络类型 (必须与加载的模型匹配)。')
    parser.add_argument('--img_size', type=int, default=416, 
                        help='输入模型的图像尺寸 (必须与训练时匹配)。')

    # --- 测试配置 ---
    parser.add_argument('--bs', type=int, default=1, 
                        help='批次大小。为了准确测量FPS，推荐设为1。')
    parser.add_argument('--n_worker', type=int, default=2, 
                        help='数据加载的工作进程数。')
    parser.add_argument('--threshold', type=float, default=0.5, 
                        help='二值化预测掩码的阈值。')

    # --- 输出配置 ---
    parser.add_argument('--save_dir', type=str, default=None,
                        help='(可选) 保存预测掩码图像的目录。如果不提供，则不保存。')

    return parser.parse_args()

class Tester:
    def __init__(self, args):
        set_seed(42)
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 1. 创建并加载模型
        print("🔄 创建并加载模型...")
        self.model = self._load_model()
        
        # 2. 创建数据加载器
        print(f"🔄 创建 {args.data_split} 数据加载器...")
        self.test_loader = self._create_dataloader()
        
        # 3. 初始化指标计算器
        self.metrics_calculator = SegmentationMetrics(device=self.device)

        # 4. 准备可视化保存目录
        if args.save_dir:
            self.save_mask_dir = os.path.join(args.save_dir, 'masks')
            self.save_overlay_dir = os.path.join(args.save_dir, 'overlays')
            os.makedirs(self.save_mask_dir, exist_ok=True)
            os.makedirs(self.save_overlay_dir, exist_ok=True)
            print(f"🎨 预测结果将保存在: {args.save_dir}")

    def _load_model(self):
        """加载模型权重"""
        model = RTGlassNet(backbone_type=self.args.backbone_type).to(self.device)
        
        if not os.path.exists(self.args.model_path):
            raise FileNotFoundError(f"❌ 模型权重文件未找到: {self.args.model_path}")
            
        print(f"📦 正在从 {self.args.model_path} 加载权重...")
        
        # 加载检查点，兼容完整检查点和纯state_dict
        checkpoint = torch.load(self.args.model_path, map_location=self.device)
        
        # 尝试多种可能的key来加载权重
        if 'model' in checkpoint:
            state_dict = checkpoint['model']
        elif 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint
            
        # 解决可能存在的"module."前缀问题（当模型是用DataParallel训练时）
        if all(key.startswith('module.') for key in state_dict.keys()):
            print("🔧 检测到'module.'前缀，正在移除...")
            state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}

        model.load_state_dict(state_dict)
        print("✅ 权重加载成功!")
        
        model.eval() # 切换到评估模式
        return model

    def _create_dataloader(self):
        """创建测试数据加载器"""
        test_dataset = GlassDataLoader(
            data_dir=self.args.data_path,
            split=self.args.data_split,
            target_size=(self.args.img_size, self.args.img_size),
            split_ratio=1.0, # 使用该split下的全部数据
            random_seed=42,
            glass_aug_config=None # 测试时不使用数据增强
        )
        print(f"📊 测试数据集: {self.args.data_split}, 包含 {len(test_dataset)} 张图像。")
        
        return DataLoader(
            test_dataset,
            batch_size=self.args.bs,
            shuffle=False,
            num_workers=self.args.n_worker,
            pin_memory=True
        )

    def run_test(self):
        """执行完整的测试流程"""
        print("\n🚀 开始测试流程...")
        self.metrics_calculator.reset()
        total_time = 0
        total_images = 0

        pbar = tqdm(self.test_loader, desc="正在测试", ncols=100)

        with torch.no_grad():
            for i, (images, gt_masks, dt_maps, original_paths) in enumerate(pbar):
                images = images.to(self.device)
                gt_masks = gt_masks.to(self.device)

                # 测量推理时间
                torch.cuda.synchronize()
                start_time = time.time()
                
                outputs = self.model(images)
                
                torch.cuda.synchronize()
                end_time = time.time()
                
                total_time += (end_time - start_time)
                total_images += images.size(0)

                # 使用CRF精炼后的结果进行评估（如果可用），否则用主预测
                pred_prob = outputs.get('refined_pred', outputs['main_pred'])
                
                # 更新指标
                self.metrics_calculator.update(pred_prob, gt_masks, self.args.threshold)
                
                # (可选) 保存预测结果
                if self.args.save_dir:
                    self._save_predictions(pred_prob, original_paths)

        # 计算并打印最终结果
        final_metrics = self.metrics_calculator.compute()
        avg_fps = total_images / total_time if total_time > 0 else 0

        print("\n" + "="*50)
        print("🎯 最终测试结果 🎯")
        print("="*50)
        print(f"  - 模型: {os.path.basename(self.args.model_path)}")
        print(f"  - 数据集: {self.args.data_split}")
        print("-" * 50)
        print("📊 性能指标:")
        print(f"  - **IoU (交并比)**: \t{final_metrics['iou']:.4f}")
        print(f"  - **MAE (平均绝对误差)**:\t{final_metrics['mae']:.4f}")
        print(f"  - F1-Score: \t\t{final_metrics['f1']:.4f}")
        print(f"  - 准确率 (Accuracy): \t{final_metrics['accuracy']:.4f}")
        print(f"  - 精确率 (Precision): \t{final_metrics['precision']:.4f}")
        print(f"  - 召回率 (Recall): \t{final_metrics['recall']:.4f}")
        print("-" * 50)
        print("🚀 速度指标:")
        print(f"  - **平均FPS**: \t\t{avg_fps:.2f} 帧/秒")
        print(f"  - 平均延迟: \t\t{1000 / avg_fps:.2f} ms/张 (如果FPS > 0)")
        print("="*50)

    def _save_predictions(self, pred_probs, original_paths):
        """保存预测掩码和叠加图"""
        pred_masks = (pred_probs > self.args.threshold).cpu().numpy().astype(np.uint8) * 255

        for i in range(pred_masks.shape[0]):
            mask = pred_masks[i, 0]
            original_path = original_paths[i]
            filename = os.path.basename(original_path)
            
            # 保存二值掩码
            mask_img = Image.fromarray(mask, mode='L')
            mask_img.save(os.path.join(self.save_mask_dir, filename))
            
            # 保存叠加图 (可选)
            try:
                original_img = Image.open(original_path).convert("RGBA").resize(mask_img.size)
                overlay_mask = Image.new('RGBA', mask_img.size, (255, 0, 0, 128)) # 红色半透明
                overlay_img = Image.composite(overlay_mask, original_img, mask_img)
                overlay_img.save(os.path.join(self.save_overlay_dir, filename))
            except Exception as e:
                print(f"⚠️ 无法创建叠加图 {filename}: {e}")

def main():
    args = parse_arguments()
    tester = Tester(args)
    tester.run_test()

if __name__ == '__main__':
    main()