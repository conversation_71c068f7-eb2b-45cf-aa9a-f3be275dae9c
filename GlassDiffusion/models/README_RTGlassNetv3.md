# RTGlassNetv3 使用指南

## 概述

RTGlassNetv3是基于新的TrainableCRF的实时玻璃分割网络，实现了真正的端到端训练。相比v2版本，主要改进包括：

- 🌟 **端到端可训练CRF**：使用TrainableCRF替代SimplifiedDiffCRF
- 🔧 **数值稳定性**：解决NaN和梯度爆炸问题
- 📊 **参数自动学习**：CRF参数通过反向传播自动优化
- 🎯 **与v2兼容**：损失函数与v2保持一致

## 文件结构

```
GlassDiffusion/models/
├── RTGlassNetv3.py              # 主模型架构
├── loss_rtglassnetv3.py         # 损失函数
├── train_rtglassnetv3.py        # 训练脚本
└── README_RTGlassNetv3.md       # 本说明文档
```

## 核心组件

### 1. RTGlassNetv3.py - 模型架构

**主要类：**
- `RTGlassNetv3`: 主模型类
- `LightLCFI`: 轻量级大上下文特征集成模块
- `LightEdgeEnhancer`: 轻量级边缘增强模块
- `FeatureExtractor`: 特征提取器（支持InceptionNeXt）

**便捷函数：**
```python
from RTGlassNetv3 import create_rtglassnetv3_model

# 创建模型
model = create_rtglassnetv3_model(
    backbone_type='inceptionnext_tiny',  # 或 'small', 'base'
    crf_iter=5,                          # CRF迭代次数
    crf_initial_bilateral_weight=10.0,   # 双边滤波权重初始值
    crf_initial_gaussian_weight=3.0,     # 高斯滤波权重初始值
    # ... 其他CRF参数
)
```

### 2. loss_rtglassnetv3.py - 损失函数

**主要类：**
- `RTGlassNetv3Loss`: 标准BCE损失（与v2兼容）
- `RTGlassNetv3LossWithFocal`: 带Focal Loss的版本

**便捷函数：**
```python
from loss_rtglassnetv3 import create_rtglassnetv3_loss

# 创建损失函数
loss_fn = create_rtglassnetv3_loss(
    loss_type='bce',              # 或 'focal'
    main_loss_weight=1.0,         # 主损失权重
    sup_loss_weight=0.1,          # 深度监督损失权重
    use_supervision=True          # 是否使用深度监督
)
```

### 3. train_rtglassnetv3.py - 训练脚本

**主要类：**
- `RTGlassNetv3Trainer`: 训练器类

**命令行使用：**
```bash
python train_rtglassnetv3.py \
    --data_root /path/to/dataset \
    --backbone inceptionnext_tiny \
    --crf_iter 5 \
    --batch_size 8 \
    --lr 0.001 \
    --epochs 100 \
    --loss_type bce \
    --save_dir ./checkpoints/rtglassnetv3
```

## 快速开始

### 1. 基本使用

```python
import torch
from RTGlassNetv3 import create_rtglassnetv3_model
from loss_rtglassnetv3 import create_rtglassnetv3_loss

# 创建模型和损失函数
model = create_rtglassnetv3_model(backbone_type='inceptionnext_tiny')
loss_fn = create_rtglassnetv3_loss(loss_type='bce')

# 创建测试数据
batch_size = 2
images = torch.randn(batch_size, 3, 256, 256)
targets = torch.randint(0, 2, (batch_size, 256, 256)).float()

# 前向传播
outputs = model(images)

# 计算损失
total_loss, loss_dict = loss_fn(outputs, targets)

print(f"总损失: {total_loss:.4f}")
print(f"主损失: {loss_dict['main_loss']:.4f}")
print(f"监督损失: {loss_dict['sup_loss']:.4f}")
```

### 2. 训练循环

```python
import torch.optim as optim

# 创建优化器
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 训练循环
for epoch in range(num_epochs):
    model.train()
    
    for batch_idx, (images, targets) in enumerate(train_loader):
        # 前向传播
        outputs = model(images)
        
        # 计算损失
        total_loss, loss_dict = loss_fn(outputs, targets)
        
        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()
        
        # 打印CRF参数（可选）
        if batch_idx % 100 == 0:
            crf_params = model.get_crf_parameters()
            print(f"CRF参数: {crf_params}")
```

### 3. 模型保存和加载

```python
# 保存模型
checkpoint = {
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'epoch': current_epoch,
    'loss': best_loss,
    'crf_parameters': model.get_crf_parameters()
}
torch.save(checkpoint, 'rtglassnetv3_checkpoint.pth')

# 加载模型
checkpoint = torch.load('rtglassnetv3_checkpoint.pth')
model.load_state_dict(checkpoint['model_state_dict'])
optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
```

## 模型输出

RTGlassNetv3的输出是一个字典，包含以下键：

```python
outputs = {
    # 主要预测（用于最终评估和主损失）
    'main_pred': torch.Tensor,      # (B, 1, H, W) - 主分支预测
    'refined_pred': torch.Tensor,   # (B, 1, H, W) - CRF精炼后预测
    'main_logits': torch.Tensor,    # (B, 1, H, W) - 主分支logits
    'refined_logits': torch.Tensor, # (B, 2, H, W) - CRF精炼后logits
    
    # 深度监督预测（用于可选的监督损失）
    'sup_h_pred': torch.Tensor,     # (B, 1, H, W) - 高层监督预测
    'sup_l_pred': torch.Tensor,     # (B, 1, H, W) - 低层监督预测
    'sup_final_pred': torch.Tensor, # (B, 1, H, W) - 最终层监督预测
}
```

## CRF参数监控

TrainableCRF提供了7个可学习参数：

```python
crf_params = model.get_crf_parameters()
print(crf_params)
# 输出示例：
# {
#     'bilateral_weight': 10.01,           # 双边滤波权重
#     'gaussian_weight': 3.00,             # 高斯滤波权重
#     'bilateral_spatial_sigma': 40.04,    # 双边滤波空间sigma
#     'bilateral_color_sigma': 5.01,       # 双边滤波颜色sigma
#     'gaussian_sigma': 3.00,              # 高斯滤波sigma
#     'message_scaler': 1.00,              # 消息缩放因子
#     'iteration_decay': 0.90              # 迭代衰减因子
# }
```

## 性能特点

- **参数量**: ~30M（InceptionNeXt-Tiny）
- **CRF参数**: 7个可学习参数
- **训练稳定性**: 解决数值不稳定问题
- **端到端训练**: CRF参数自动优化
- **与v2兼容**: 损失函数保持一致

## 测试

运行完整测试：

```bash
python test_rtglassnetv3_complete.py
```

测试各个组件：

```bash
# 测试模型
python GlassDiffusion/models/RTGlassNetv3.py

# 测试损失函数
python GlassDiffusion/models/loss_rtglassnetv3.py

# 查看训练脚本帮助
python GlassDiffusion/models/train_rtglassnetv3.py --help
```

## 注意事项

1. **数据格式**: 确保输入图像为(B, 3, H, W)，标签为(B, H, W)
2. **设备兼容**: 模型会自动检测CUDA可用性
3. **CRF参数**: 参数在学习过程中会自动调整，无需手动调参
4. **内存使用**: CRF迭代次数影响内存使用，建议从3-5次开始

## 故障排除

### 常见问题

1. **ImportError: timm not available**
   ```bash
   pip install timm
   ```

2. **CUDA out of memory**
   - 减少batch_size
   - 减少CRF迭代次数
   - 使用更小的backbone

3. **数值不稳定**
   - 检查输入数据范围
   - 确保标签格式正确
   - 降低学习率

### 调试技巧

```python
# 检查CRF参数
crf_params = model.get_crf_parameters()
print("CRF参数:", crf_params)

# 检查输出范围
outputs = model(images)
for key, value in outputs.items():
    print(f"{key}: [{value.min():.4f}, {value.max():.4f}]")

# 检查梯度
for name, param in model.named_parameters():
    if param.grad is not None:
        print(f"{name}: 梯度范数 = {param.grad.norm():.6f}")
```

## 版本历史

- **v3.0**: 集成TrainableCRF，实现端到端训练
- **v2.x**: 使用SimplifiedDiffCRF，需要手动调参
- **v1.x**: 原始RTGlassNet实现

## 贡献

欢迎提交Issue和Pull Request来改进RTGlassNetv3！ 