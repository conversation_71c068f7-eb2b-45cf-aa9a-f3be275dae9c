#!/usr/bin/env python3
"""
🔧 修复后的test_gdd.py
解决输出mask全白的问题
"""

import os
import warnings
import cv2
import numpy as np
import torch
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
from ig_glass.misc import *
from GlassDiffusion.models.RTGlassNet import RTGlassNet
from tqdm import tqdm

# 抑制警告
warnings.filterwarnings('ignore')
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 设备设置
device_ids = [0]
torch.cuda.set_device(device_ids[0])

# 路径设置
ckpt_path = '/home/<USER>/ws/IG_SLAM/ckpt/RTGlassNet_KFold/weights'
args = {
    'snapshot': 'BEST_fold5_epoch075_iou0.9797',
    'scale': 384,
    'glass_threshold': 0.5,
    'crf_iter': 3,
    'crf_bilateral_weight': 5.0,
}

# 预处理
img_transform = <PERSON><PERSON>mpo<PERSON>([
    <PERSON><PERSON>ze(height=args['scale'], width=args['scale']),
    A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ToTensorV2()
])

def debug_prediction(pred_tensor, image_file):
    """调试预测结果"""
    print(f"🔍 调试 {image_file}:")
    print(f"  预测张量形状: {pred_tensor.shape}")
    print(f"  数值范围: [{pred_tensor.min():.6f}, {pred_tensor.max():.6f}]")
    print(f"  均值: {pred_tensor.mean():.6f}")
    print(f"  标准差: {pred_tensor.std():.6f}")
    
    # 检查异常值
    if torch.isnan(pred_tensor).any():
        print(f"  ⚠️ 包含NaN值")
    if torch.isinf(pred_tensor).any():
        print(f"  ⚠️ 包含Inf值")
    
    # 检查是否为常数
    if pred_tensor.max() - pred_tensor.min() < 1e-6:
        print(f"  ⚠️ 几乎为常数值")

def adaptive_threshold(pred_np):
    """自适应阈值计算"""
    # 方法1: Otsu阈值
    pred_uint8 = (pred_np * 255).astype(np.uint8)
    otsu_thresh, _ = cv2.threshold(pred_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    otsu_thresh_norm = otsu_thresh / 255.0
    
    # 方法2: 均值阈值
    mean_thresh = np.mean(pred_np)
    
    # 方法3: 中位数阈值
    median_thresh = np.median(pred_np)
    
    # 选择最合理的阈值
    thresholds = [otsu_thresh_norm, mean_thresh, median_thresh]
    # 过滤掉极端值
    valid_thresholds = [t for t in thresholds if 0.1 <= t <= 0.9]
    
    if valid_thresholds:
        return np.mean(valid_thresholds)
    else:
        return 0.5  # 默认阈值

def detect_glass_and_evaluate_fixed(image_folder, output_folder, gt_folder, model, glass_threshold):
    """修复后的检测和评估函数"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files.sort()
    
    iou, acc, fm, mae, ber, aber = 0, 0, 0, 0, 0, 0
    count = 0
    debug_count = 0  # 只调试前几张图像
    
    for image_file in tqdm(image_files, desc="Processing"):
        image_path = os.path.join(image_folder, image_file)
        gt_path = os.path.join(gt_folder, os.path.splitext(image_file)[0] + '.png')
        
        # 读取图像
        img_rgb = cv2.imread(image_path)
        if img_rgb is None:
            print(f"⚠️ 无法读取图像: {image_path}")
            continue
        img_rgb = cv2.cvtColor(img_rgb, cv2.COLOR_BGR2RGB)
        
        # 读取GT
        if not os.path.exists(gt_path):
            print(f"⚠️ GT不存在: {gt_path}")
            continue
        gt_mask_u8 = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        if gt_mask_u8 is None:
            print(f"⚠️ 无法读取GT: {gt_path}")
            continue
        
        # 预处理
        transformed = img_transform(image=img_rgb)
        img_var = transformed['image'].unsqueeze(0).cuda(device_ids[0])
        
        # 模型推理
        with torch.no_grad():
            outputs = model(img_var)
            
            # 🔧 修复1: 强制使用main_pred，与训练时保持一致
            pred_tensor = outputs['main_pred']
            use_refined = False

            # 注释掉refined_pred的使用，因为训练时验证用的是main_pred
            # refined_pred = outputs.get('refined_pred')
            # if refined_pred is not None:
            #     # 检查refined_pred的有效性
            #     if not (torch.isnan(refined_pred).any() or torch.isinf(refined_pred).any()):
            #         if refined_pred.max() - refined_pred.min() > 1e-5:
            #             if refined_pred.mean() > 1e-4:
            #                 pred_tensor = refined_pred
            #                 use_refined = True
            
            # 🔧 修复3: 数值稳定性检查
            if torch.isnan(pred_tensor).any() or torch.isinf(pred_tensor).any():
                print(f"⚠️ 检测到异常值在 {image_file}，使用零张量")
                pred_tensor = torch.zeros_like(pred_tensor)
            
            # 🔧 修复4: 确保张量在合理范围内
            pred_tensor = torch.clamp(pred_tensor, 0.0, 1.0)
            
            # 提取预测概率图
            if pred_tensor.dim() == 4:
                pred_prob_map = pred_tensor.squeeze(0).squeeze(0)
            else:
                pred_prob_map = pred_tensor.squeeze()
        
        # 调试前几张图像
        if debug_count < 3:
            debug_prediction(pred_prob_map, image_file)
            print(f"  使用{'refined_pred' if use_refined else 'main_pred'}")
            debug_count += 1
        
        # 🔧 修复5: 智能阈值选择
        pred_np = pred_prob_map.cpu().numpy()
        
        # 首先尝试固定阈值
        binary_pred = (pred_prob_map > glass_threshold).float()
        white_ratio = binary_pred.mean().item()
        
        # 如果结果异常，使用自适应阈值
        if white_ratio > 0.98 or white_ratio < 0.02:
            adaptive_thresh = adaptive_threshold(pred_np)
            binary_pred = (pred_prob_map > adaptive_thresh).float()
            new_white_ratio = binary_pred.mean().item()
            
            if debug_count <= 3:
                print(f"  固定阈值{glass_threshold}白色比例: {white_ratio:.4f}")
                print(f"  自适应阈值{adaptive_thresh:.3f}白色比例: {new_white_ratio:.4f}")
        
        prediction_float = binary_pred.cpu().numpy()
        
        # 🔧 修复6: GT处理改进
        # 确保GT和预测尺寸一致
        if gt_mask_u8.shape != prediction_float.shape:
            gt_resized = cv2.resize(gt_mask_u8, (prediction_float.shape[1], prediction_float.shape[0]))
        else:
            gt_resized = gt_mask_u8
        
        gt_float = (gt_resized / 255.0).astype(np.float32)
        
        # 保存预测结果
        pred_to_save = (prediction_float * 255).astype(np.uint8)
        output_path = os.path.join(output_folder, os.path.splitext(image_file)[0] + '.png')
        cv2.imwrite(output_path, pred_to_save)
        
        # 保存调试信息（前几张图像）
        if debug_count <= 3:
            # 保存原始预测概率图
            prob_to_save = (pred_np * 255).astype(np.uint8)
            prob_path = os.path.join(output_folder, os.path.splitext(image_file)[0] + '_prob.png')
            cv2.imwrite(prob_path, prob_to_save)
        
        # 计算指标
        try:
            tiou = compute_iou(prediction_float, gt_float)
            tacc = compute_acc(prediction_float, gt_float)
            precision, recall = compute_precision_recall(prediction_float, gt_float)
            tfm = compute_fmeasure(precision, recall)
            tmae = compute_mae(prediction_float, gt_float)
            tber = compute_ber(prediction_float, gt_float)
            taber = compute_aber(prediction_float, gt_float)
            
            count += 1
            iou += tiou
            acc += tacc
            fm += tfm
            mae += tmae
            ber += tber
            aber += taber
            
        except Exception as e:
            print(f"⚠️ 计算指标时出错 {image_file}: {e}")
            continue
    
    if count > 0:
        return iou/count, acc/count, fm/count, mae/count, ber/count, aber/count
    else:
        return 0, 0, 0, 0, 0, 0

def main():
    print("🔧 使用修复后的测试脚本")
    print("=" * 60)
    
    # 检查模型文件
    model_path = os.path.join(ckpt_path, args['snapshot'] + '.pth')
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        # 列出可用的模型文件
        if os.path.exists(ckpt_path):
            available_models = [f for f in os.listdir(ckpt_path) if f.endswith('.pth')]
            print(f"可用模型文件: {available_models}")
        return
    
    print(f"✅ 加载模型: {model_path}")
    
    # 加载模型
    checkpoint = torch.load(model_path, map_location=f'cuda:{device_ids[0]}')
    
    # 检查checkpoint内容
    print(f"Checkpoint keys: {list(checkpoint.keys())}")
    
    backbone_type = checkpoint.get('backbone_type', 'inceptionnext_base')
    print(f"骨干网络类型: {backbone_type}")
    
    model = RTGlassNet(
        backbone_type=backbone_type, 
        crf_iter=args['crf_iter'], 
        crf_bilateral_weight=args['crf_bilateral_weight']
    )
    
    # 加载权重
    if 'model' in checkpoint:
        result = model.load_state_dict(checkpoint['model'], strict=False)
    else:
        result = model.load_state_dict(checkpoint, strict=False)
    
    print(f"权重加载结果: {result}")
    
    model.cuda(device_ids[0])
    model.eval()
    
    # 检查数据路径
    data_path = "/home/<USER>/ws/IG_SLAM/ig_glass/dataset/train_gdd"
    image_folder = os.path.join(data_path, "image")
    output_folder = os.path.join(data_path, "glass_mask_gdd_fixed")
    gt_folder = os.path.join(data_path, "mask")
    
    print(f"图像目录: {image_folder}")
    print(f"输出目录: {output_folder}")
    print(f"GT目录: {gt_folder}")
    
    # 检查目录是否存在
    for folder, name in [(image_folder, "图像"), (gt_folder, "GT")]:
        if not os.path.exists(folder):
            print(f"❌ {name}目录不存在: {folder}")
            return
        else:
            files = os.listdir(folder)
            print(f"✅ {name}目录包含 {len(files)} 个文件")
    
    print(f"\n🚀 开始测试...")
    
    # 运行测试
    iou, acc, fm, mae, ber, aber = detect_glass_and_evaluate_fixed(
        image_folder, output_folder, gt_folder, model, args['glass_threshold']
    )
    
    print(f"\n" + "="*60)
    print(f"            修复后的测试结果")
    print(f"="*60)
    print(f"  IoU (Intersection over Union): {iou:.4f}")
    print(f"  Accuracy:                      {acc:.4f}")
    print(f"  F-measure:                     {fm:.4f}")
    print(f"  MAE (Mean Absolute Error):     {mae:.4f}")
    print(f"  BER (Balanced Error Rate):     {ber:.4f}")
    print(f"  ABER (Adaptive BER):           {aber:.4f}")
    print(f"="*60)
    
    print(f"\n💡 修复说明:")
    print(f"1. 优先使用main_pred，避免CRF异常")
    print(f"2. 添加数值稳定性检查")
    print(f"3. 使用自适应阈值处理异常情况")
    print(f"4. 改进GT和预测的尺寸匹配")
    print(f"5. 保存调试信息便于分析")

if __name__ == '__main__':
    main()
