# @Time    : 2025
# <AUTHOR> <PERSON>ine & Gemini

# @Project : IG_SLAM
# @File    : train_fpn_scsa.py
# @Function: Training script for the new GDNetFPNSCSA model.


import os
import sys
import datetime
import torch
import torch.nn.functional as F
import numpy as np

from torch.utils.data import DataLoader
from tensorboardX import SummaryWriter
from torch.optim import AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR
from tqdm import tqdm

# Append paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, 'ig_glass'))

# Import new model and loss
from ig_glass.gdnet_fpn_scsa import GDNetFPNSCSA
from ig_glass.loss_fpn_scsa import GDNetFPNSCSALoss

# Import dataloader and utilities
from ig_glass.glass_dataloader import GlassDataLoader, create_glass_dataloaders, GlassCrossValidationLoader
from ig_glass.misc import AvgMeter, check_mkdir


class EarlyStopping:
    """早停机制，基于IoU监控"""
    def __init__(self, patience=10, min_delta=0.002, mode='max'):
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.best_metric = float('-inf') if mode == 'max' else float('inf')
        self.counter = 0
        self.best_epoch = 0

    def step(self, metric, epoch):
        if self.mode == 'max':
            # IoU模式：期望更高的值
            if metric > self.best_metric + self.min_delta:
                self.best_metric = metric
                self.counter = 0
                self.best_epoch = epoch
            else:
                self.counter += 1
        else:
            # Loss模式：期望更低的值
            if metric < self.best_metric - self.min_delta:
                self.best_metric = metric
                self.counter = 0
                self.best_epoch = epoch
            else:
                self.counter += 1

        return self.counter >= self.patience


# --- Config ---
ckpt_path = './ckpt'
exp_name = 'GDNetFPNSCSA'
args = {
    'epoch_num': 50,
    'train_batch_size': 6,
    'val_batch_size': 6,
    'last_epoch': 0,
    'lr': 0.0005,  # 降低学习率到0.0005
    'weight_decay': 1e-2,
    'momentum': 0.9,
    'min_lr': 1e-5,
    'data_path': './ig_glass/dataset/train_gdd',
    'val_data_path': './ig_glass/dataset/validation',
    'log_path': os.path.join(ckpt_path, exp_name, 'log'),
    'ckpt_path': os.path.join(ckpt_path, exp_name, 'weights'),
    'backbone_path': './ig_glass/ckpt/resnext101_32x8.pth',
    'gpu_id': '0',
    'val_interval': 5,  # 每5个epoch验证一次
    'use_kfold': True,  # 启用3折交叉验证
    'n_folds': 3,       # 3折交叉验证
    'early_stop_patience': 10,  # 早停耐心值
    'early_stop_min_delta': 0.002,  # 早停最小改进阈值
    'resume': True,  # 是否继续训练
    'resume_fold': None,  # 要继续训练的fold，None表示所有fold都继续
    'resume_checkpoint': 'best_model_fold3.pth',  # 加载最新的模型继续训练，也可以指定具体的模型文件名
}


def train_epoch(model, train_loader, criterion, optimizer, scheduler, epoch, args, writer, fold=None):
    """训练一个epoch"""
    print(f"\n📅 Epoch [{epoch+1:03d}/{args['epoch_num']:03d}] 开始训练...")
    train_loss_record = AvgMeter()
    model.train()

    train_bar = tqdm(enumerate(train_loader), total=len(train_loader), desc=f"Epoch [{epoch+1:03d}/{args['epoch_num']:03d}]")
    for i, data in train_bar:
        inputs, masks = data
        inputs = inputs.cuda()
        masks = masks.cuda()
        
        # 转换为期望的格式
        gts = {'gt': masks}

        optimizer.zero_grad()
        predictions = model(inputs)
        loss_dict = criterion(predictions, gts)
        total_loss = loss_dict['total_loss']
        total_loss.backward()
        optimizer.step()

        train_loss_record.update(total_loss.item(), args['train_batch_size'])

        # tqdm进度条显示loss
        train_bar.set_postfix(
            loss=total_loss.item(),
            main=loss_dict['loss_main'].item(),
            refined=loss_dict['loss_refined'].item(),
            avg_loss=train_loss_record.avg
        )

        writer.add_scalar('train/total_loss', total_loss.item(), epoch * len(train_loader) + i)
        writer.add_scalar('train/loss_main', loss_dict['loss_main'].item(), epoch * len(train_loader) + i)
        writer.add_scalar('train/loss_refined', loss_dict['loss_refined'].item(), epoch * len(train_loader) + i)

    current_lr = scheduler.get_last_lr()[0]
    writer.add_scalar('train/learning_rate', current_lr, epoch)
    scheduler.step()
    
    return train_loss_record.avg, current_lr


def validate_epoch(model, val_loader, criterion, epoch, args, writer, fold=None):
    """验证一个epoch"""
    print(f"\n🔍 Epoch [{epoch+1:03d}] 开始验证...")
    val_iou, val_loss = validate(model, val_loader, criterion)
    writer.add_scalar('val/iou', val_iou, epoch)
    writer.add_scalar('val/loss', val_loss, epoch)
    return val_iou, val_loss


# --- Main Function ---
def main():
    # --- Setup ---
    torch.manual_seed(2025)
    os.environ['CUDA_VISIBLE_DEVICES'] = args['gpu_id']
    torch.cuda.set_device(0)

    check_mkdir(args['log_path'])
    check_mkdir(args['ckpt_path'])
    writer = SummaryWriter(log_dir=args['log_path'])

    # --- Model, Loss, Optimizer, Scheduler ---
    print("🔧 初始化模型...")
    model = GDNetFPNSCSA(backbone_path=args['backbone_path']).cuda()
    print(f"✅ 模型加载成功，参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    print("🔧 初始化损失函数...")
    criterion = GDNetFPNSCSALoss().cuda()
    
    print("🔧 初始化优化器...")
    optimizer = AdamW(model.parameters(), lr=args['lr'], weight_decay=args['weight_decay'])
    scheduler = CosineAnnealingLR(optimizer, T_max=args['epoch_num'], eta_min=args['min_lr'])

    # 如果继续训练，加载之前的权重和优化器状态
    start_epoch = args['last_epoch']
    best_iou = 0
    if args['resume']:
        print(f"\n📥 加载检查点继续训练...")
        if args['resume_checkpoint'] == 'latest_model':
            # 查找最新的模型文件
            model_files = [f for f in os.listdir(args['ckpt_path']) if f.startswith('latest_model')]
            if model_files:
                latest_model = sorted(model_files)[-1]
                checkpoint_path = os.path.join(args['ckpt_path'], latest_model)
                print(f"找到最新模型: {latest_model}")
            else:
                print("⚠️ 未找到latest_model，将从头开始训练")
                checkpoint_path = None
        else:
            # 使用指定的检查点文件
            checkpoint_path = os.path.join(args['ckpt_path'], args['resume_checkpoint'])
            if not os.path.exists(checkpoint_path):
                print(f"⚠️ 指定的检查点文件不存在: {checkpoint_path}")
                checkpoint_path = None

        if checkpoint_path:
            checkpoint = torch.load(checkpoint_path)
            # 检查检查点格式并相应处理
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                # 完整的训练状态检查点
                model.load_state_dict(checkpoint['model_state_dict'])
                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                start_epoch = checkpoint['epoch']
                best_iou = checkpoint.get('best_iou', 0)
                print(f"✅ 成功加载完整检查点: epoch {start_epoch}, 最佳IoU {best_iou:.4f}")
            else:
                # 只包含模型权重的检查点
                try:
                    model.load_state_dict(checkpoint)
                    print(f"✅ 成功加载模型权重")
                except Exception as e:
                    print(f"⚠️ 加载模型权重失败: {str(e)}")
                    if isinstance(checkpoint, dict):
                        # 尝试加载不同的状态字典格式
                        if 'state_dict' in checkpoint:
                            model.load_state_dict(checkpoint['state_dict'])
                            print("✅ 使用'state_dict'键成功加载模型权重")
                        elif 'model' in checkpoint:
                            model.load_state_dict(checkpoint['model'])
                            print("✅ 使用'model'键成功加载模型权重")
                        else:
                            print("❌ 无法识别的检查点格式")
                            return
                
                # 重新初始化训练状态
                start_epoch = 0
                best_iou = 0
                print("📊 从epoch 0开始训练，重置最佳IoU")
                
            # 更新优化器的学习率
            for param_group in optimizer.param_groups:
                param_group['lr'] = args['lr']
            print(f"📊 学习率已更新为: {args['lr']}")

    print(f"✅ 优化器初始化完成，学习率: {args['lr']}, 权重衰减: {args['weight_decay']}")

    log_txt = os.path.join(args['log_path'], 'train_log.txt')

    # --- K-Fold Training Loop ---
    if args['use_kfold']:
        print(f"\n🚀 开始{args['n_folds']}折交叉验证训练！")
        print("=" * 80)
        
        for fold in range(args['n_folds']):
            # 如果指定了继续训练的fold，跳过其他fold
            if args['resume_fold'] is not None and fold != args['resume_fold']:
                continue
                
            print(f"\n🔄 开始第 {fold+1}/{args['n_folds']} 折训练...")
            
            # 为当前折创建数据加载器
            train_dataset = GlassCrossValidationLoader(mode='train', fold=fold, augment_data=True, target_size=416, n_folds=args['n_folds'])
            val_dataset = GlassCrossValidationLoader(mode='test', fold=fold, augment_data=False, target_size=416, n_folds=args['n_folds'])
            train_loader = DataLoader(train_dataset, batch_size=args['train_batch_size'], shuffle=True, num_workers=4)
            val_loader = DataLoader(val_dataset, batch_size=args['val_batch_size'], shuffle=False, num_workers=4)
            
            print(f"📊 第{fold+1}折 - 训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")
            
            # 初始化早停机制
            early_stopping = EarlyStopping(
                patience=args['early_stop_patience'],
                min_delta=args['early_stop_min_delta'],
                mode='max'
            )
            
            # 当前折的训练循环
            for epoch in range(start_epoch, args['epoch_num']):
                # 训练
                train_loss, current_lr = train_epoch(model, train_loader, criterion, optimizer, scheduler, epoch, args, writer, fold)
                
                # 验证（每val_interval个epoch或最后一个epoch）
                if (epoch + 1) % args['val_interval'] == 0 or epoch == args['epoch_num'] - 1:
                    val_iou, val_loss = validate_epoch(model, val_loader, criterion, epoch, args, writer, fold)
                    
                    log_msg = f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Fold {fold+1}, Epoch [{epoch+1:03d}/{args['epoch_num']:03d}], Train Loss: {train_loss:.5f}, Val Loss: {val_loss:.5f}, Val IoU: {val_iou:.4f}, LR: {current_lr:.7f}"
                    print(log_msg)
                    with open(log_txt, 'a') as f:
                        f.write(log_msg + '\n')

                    # 保存最佳模型
                    if val_iou > best_iou:
                        best_iou = val_iou
                        torch.save(model.state_dict(), os.path.join(args['ckpt_path'], f'best_model_fold{fold+1}.pth'))
                        print(f"🏆 *** 第{fold+1}折最佳模型已保存，IoU: {best_iou:.4f} ***")

                    torch.save(model.state_dict(), os.path.join(args['ckpt_path'], f'latest_model_fold{fold+1}.pth'))
                    
                    # 检查早停
                    if early_stopping.step(val_iou, epoch):
                        print(f"🛑 第 {fold+1} 折在第 {epoch+1} 轮触发早停，最佳IoU: {early_stopping.best_metric:.4f}")
                        break
                else:
                    # 只记录训练loss
                    log_msg = f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Fold {fold+1}, Epoch [{epoch+1:03d}/{args['epoch_num']:03d}], Train Loss: {train_loss:.5f}, LR: {current_lr:.7f}"
                    print(log_msg)
                    with open(log_txt, 'a') as f:
                        f.write(log_msg + '\n')
            
            print(f"✅ 第 {fold+1} 折训练完成！")
    else:
        # 使用8:2分割的普通训练
        print("📊 使用8:2数据集分割...")
        train_dataset = GlassDataLoader(mode='train_gdd', augment_data=True, target_size=416)
        val_dataset = GlassDataLoader(mode='train_gdd_split_valid', augment_data=False, target_size=416)
        train_loader = DataLoader(train_dataset, batch_size=args['train_batch_size'], shuffle=True, num_workers=4)
        val_loader = DataLoader(val_dataset, batch_size=args['val_batch_size'], shuffle=False, num_workers=4)
        
        print(f"\n🚀 开始训练！总轮次: {args['epoch_num']}, 批次大小: {args['train_batch_size']}")
        print(f"📊 训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")
        print("=" * 80)
        
        # 初始化早停机制
        early_stopping = EarlyStopping(
            patience=args['early_stop_patience'],
            min_delta=args['early_stop_min_delta'],
            mode='max'
        )
        
        for epoch in range(start_epoch, args['epoch_num']):
            # 训练
            train_loss, current_lr = train_epoch(model, train_loader, criterion, optimizer, scheduler, epoch, args, writer)
            
            # 验证（每val_interval个epoch或最后一个epoch）
            if (epoch + 1) % args['val_interval'] == 0 or epoch == args['epoch_num'] - 1:
                val_iou, val_loss = validate_epoch(model, val_loader, criterion, epoch, args, writer)
                
                log_msg = f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Epoch [{epoch+1:03d}/{args['epoch_num']:03d}], Train Loss: {train_loss:.5f}, Val Loss: {val_loss:.5f}, Val IoU: {val_iou:.4f}, LR: {current_lr:.7f}"
                print(log_msg)
                with open(log_txt, 'a') as f:
                    f.write(log_msg + '\n')

                # 保存最佳模型
                if val_iou > best_iou:
                    best_iou = val_iou
                    torch.save(model.state_dict(), os.path.join(args['ckpt_path'], 'best_model.pth'))
                    print(f"🏆 *** Best model saved with IoU: {best_iou:.4f} ***")

                torch.save(model.state_dict(), os.path.join(args['ckpt_path'], 'latest_model.pth'))
                
                # 检查早停
                if early_stopping.step(val_iou, epoch):
                    print(f"🛑 在第 {epoch+1} 轮触发早停，最佳IoU: {early_stopping.best_metric:.4f}")
                    break
            else:
                # 只记录训练loss
                log_msg = f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Epoch [{epoch+1:03d}/{args['epoch_num']:03d}], Train Loss: {train_loss:.5f}, LR: {current_lr:.7f}"
                print(log_msg)
                with open(log_txt, 'a') as f:
                    f.write(log_msg + '\n')

    writer.close()


def validate(model, val_loader, criterion):
    model.eval()
    avg_iou = AvgMeter()
    avg_loss = AvgMeter()
    
    val_bar = tqdm(val_loader, desc="验证中")
    with torch.no_grad():
        for data in val_bar:
            inputs, masks = data
            inputs = inputs.cuda()
            masks = masks.cuda()
            
            # 转换为期望的格式
            gts = {'gt': masks}

            predictions = model(inputs)
            loss_dict = criterion(predictions, gts)
            avg_loss.update(loss_dict['total_loss'].item(), inputs.size(0))

            # Use the refined prediction for IoU calculation
            pred = predictions['refined_pred']
            pred = (pred > 0.5).float()
            gt_mask = gts['gt']

            intersection = (pred * gt_mask).sum()
            union = pred.sum() + gt_mask.sum() - intersection
            iou = (intersection + 1e-6) / (union + 1e-6)
            avg_iou.update(iou.item(), inputs.size(0))
            
            # 更新进度条
            val_bar.set_postfix(loss=avg_loss.avg, iou=avg_iou.avg)

    return avg_iou.avg, avg_loss.avg


if __name__ == '__main__':
    main()
